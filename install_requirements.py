#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت تثبيت المكتبات المطلوبة
"""

import subprocess
import sys
import os

def install_package(package):
    """تثبيت مكتبة باستخدام طرق متعددة"""
    print(f"جاري تثبيت {package}...")
    
    # الطريقة الأولى: python -m pip
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "install", package], 
                              capture_output=True, text=True, timeout=300)
        if result.returncode == 0:
            print(f"تم تثبيت {package} بنجاح")
            return True
        else:
            print(f"فشل في تثبيت {package}: {result.stderr}")
    except Exception as e:
        print(f"خطأ في تثبيت {package}: {e}")
    
    # الطريقة الثانية: استخدام المسار الكامل
    try:
        python_path = sys.executable
        result = subprocess.run([python_path, "-m", "pip", "install", package], 
                              capture_output=True, text=True, timeout=300)
        if result.returncode == 0:
            print(f"تم تثبيت {package} بنجاح (الطريقة الثانية)")
            return True
    except Exception as e:
        print(f"فشل في الطريقة الثانية: {e}")
    
    return False

def check_package(package_name, import_name=None):
    """فحص وجود مكتبة"""
    if import_name is None:
        import_name = package_name
    
    try:
        __import__(import_name)
        print(f"{package_name} موجود")
        return True
    except ImportError:
        print(f"{package_name} غير موجود")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("    تثبيت المكتبات المطلوبة للبرنامج")
    print("=" * 60)
    
    # قائمة المكتبات المطلوبة
    packages = [
        ("PyQt5", "PyQt5"),
        ("fpdf2", "fpdf"),
        ("arabic-reshaper", "arabic_reshaper"),
        ("python-bidi", "bidi"),
        ("openpyxl", "openpyxl"),
        ("matplotlib", "matplotlib"),
        ("numpy", "numpy"),
        ("Pillow", "PIL")
    ]
    
    print(f"إصدار Python: {sys.version}")
    print(f"مسار Python: {sys.executable}")
    print()
    
    # فحص المكتبات الموجودة
    print("فحص المكتبات الموجودة:")
    print("-" * 30)
    
    missing_packages = []
    for package_name, import_name in packages:
        if not check_package(package_name, import_name):
            missing_packages.append(package_name)
    
    if not missing_packages:
        print("\nجميع المكتبات موجودة!")
        return
    
    print(f"\nالمكتبات المفقودة: {', '.join(missing_packages)}")
    print("\nبدء التثبيت...")
    print("-" * 30)
    
    # تثبيت المكتبات المفقودة
    failed_packages = []
    for package in missing_packages:
        if not install_package(package):
            failed_packages.append(package)
    
    print("\n" + "=" * 60)
    print("ملخص التثبيت:")
    print("=" * 60)
    
    if failed_packages:
        print(f"فشل في تثبيت: {', '.join(failed_packages)}")
        print("\nحلول بديلة:")
        print("1. شغل Command Prompt كـ Administrator")
        print("2. جرب: python -m ensurepip --upgrade")
        print("3. جرب: python -m pip install --upgrade pip")
        print("4. ثم أعد تشغيل هذا السكريبت")
    else:
        print("تم تثبيت جميع المكتبات بنجاح!")
        print("يمكنك الآن تشغيل البرنامج")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nتم إيقاف العملية")
    except Exception as e:
        print(f"\nخطأ غير متوقع: {e}")
    
    input("\nاضغط Enter للخروج...")
