#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت تشخيص شامل لمشاكل البرنامج
"""

import os
import sys
import sqlite3
import importlib.util
from pathlib import Path

def print_header():
    """طباعة رأس التشخيص"""
    print("=" * 60)
    print("    🔍 تشخيص شامل لمشاكل البرنامج")
    print("=" * 60)

def check_python_environment():
    """فحص بيئة Python"""
    print("\n📋 فحص بيئة Python:")
    print(f"   إصدار Python: {sys.version}")
    print(f"   مسار Python: {sys.executable}")
    print(f"   مجلد العمل: {os.getcwd()}")

def check_required_modules():
    """فحص المكتبات المطلوبة"""
    print("\n📦 فحص المكتبات المطلوبة:")
    
    required_modules = [
        'PyQt5',
        'sqlite3',
        'fpdf',
        'arabic_reshaper',
        'bidi',
        'openpyxl',
        'matplotlib',
        'numpy',
        'PIL'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            if module == 'sqlite3':
                import sqlite3
                print(f"   ✅ {module} - متوفر")
            elif module == 'PyQt5':
                import PyQt5
                print(f"   ✅ {module} - متوفر")
            elif module == 'fpdf':
                import fpdf
                print(f"   ✅ {module} - متوفر")
            elif module == 'arabic_reshaper':
                import arabic_reshaper
                print(f"   ✅ {module} - متوفر")
            elif module == 'bidi':
                import bidi
                print(f"   ✅ {module} - متوفر")
            elif module == 'openpyxl':
                import openpyxl
                print(f"   ✅ {module} - متوفر")
            elif module == 'matplotlib':
                import matplotlib
                print(f"   ✅ {module} - متوفر")
            elif module == 'numpy':
                import numpy
                print(f"   ✅ {module} - متوفر")
            elif module == 'PIL':
                import PIL
                print(f"   ✅ {module} - متوفر")
        except ImportError:
            print(f"   ❌ {module} - مفقود")
            missing_modules.append(module)
    
    return missing_modules

def check_essential_files():
    """فحص الملفات الأساسية"""
    print("\n📁 فحص الملفات الأساسية:")
    
    essential_files = [
        'main_window.py',
        'sub01_window.py',
        'budget_planning_window.py',
        'data.db',
        '01.ico'
    ]
    
    missing_files = []
    
    for file in essential_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"   ✅ {file} - موجود ({size} bytes)")
        else:
            print(f"   ❌ {file} - مفقود")
            missing_files.append(file)
    
    return missing_files

def check_database():
    """فحص قاعدة البيانات"""
    print("\n🗄️ فحص قاعدة البيانات:")
    
    if not os.path.exists('data.db'):
        print("   ❌ ملف قاعدة البيانات غير موجود")
        return False
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # فحص الجداول الأساسية
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [row[0] for row in cursor.fetchall()]
        
        print(f"   📋 عدد الجداول: {len(tables)}")
        
        # فحص جدول الموازنة السنوية
        if 'الموازنة_السنوية' in tables:
            cursor.execute("PRAGMA table_info(الموازنة_السنوية);")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]
            
            if 'السنة_المالية' in column_names:
                print("   ✅ جدول الموازنة السنوية - بنية صحيحة")
            elif 'السنة' in column_names:
                print("   ⚠️ جدول الموازنة السنوية - يحتاج إصلاح (يستخدم 'السنة' بدلاً من 'السنة_المالية')")
                return False
            else:
                print("   ❌ جدول الموازنة السنوية - بنية خاطئة")
                return False
        else:
            print("   ❌ جدول الموازنة السنوية غير موجود")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_main_imports():
    """اختبار استيراد الملفات الرئيسية"""
    print("\n🧪 اختبار استيراد الملفات:")
    
    test_files = [
        'main_window.py',
        'budget_planning_window.py'
    ]
    
    for file in test_files:
        if os.path.exists(file):
            try:
                # محاولة قراءة الملف
                with open(file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # فحص الأخطاء الأساسية
                if 'import' in content:
                    print(f"   ✅ {file} - يمكن قراءته")
                else:
                    print(f"   ⚠️ {file} - قد يحتوي على مشاكل")
                    
            except Exception as e:
                print(f"   ❌ {file} - خطأ في القراءة: {e}")
        else:
            print(f"   ❌ {file} - غير موجود")

def create_fix_script():
    """إنشاء سكريبت إصلاح سريع"""
    print("\n🔧 إنشاء سكريبت الإصلاح...")
    
    fix_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

print("🔧 بدء إصلاح قاعدة البيانات...")

# حذف قاعدة البيانات القديمة
if os.path.exists("data.db"):
    os.remove("data.db")
    print("✅ تم حذف قاعدة البيانات القديمة")

# إنشاء قاعدة بيانات جديدة
conn = sqlite3.connect("data.db")
cursor = conn.cursor()

# جدول السنوات المالية
cursor.execute("""
CREATE TABLE السنوات_المالية (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    السنة_المالية INTEGER UNIQUE NOT NULL,
    تاريخ_البداية DATE NOT NULL,
    تاريخ_النهاية DATE NOT NULL,
    الحالة TEXT DEFAULT 'نشطة'
)
""")

# جدول الموازنة السنوية
cursor.execute("""
CREATE TABLE الموازنة_السنوية (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    السنة_المالية INTEGER NOT NULL,
    نوع_البند TEXT NOT NULL,
    اسم_البند TEXT NOT NULL,
    المبلغ_المتوقع REAL NOT NULL DEFAULT 0,
    المبلغ_الفعلي REAL DEFAULT 0,
    النسبة_المحققة REAL DEFAULT 0,
    الحالة TEXT DEFAULT 'نشط',
    تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(السنة_المالية, نوع_البند, اسم_البند)
)
""")

# جدول إعدادات النظام
cursor.execute("""
CREATE TABLE إعدادات_النظام (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    اسم_الإعداد TEXT UNIQUE NOT NULL,
    قيمة_الإعداد TEXT NOT NULL,
    وصف_الإعداد TEXT
)
""")

# إضافة السنوات
cursor.execute("INSERT INTO السنوات_المالية (السنة_المالية, تاريخ_البداية, تاريخ_النهاية) VALUES (2024, '2024-09-01', '2025-08-31')")
cursor.execute("INSERT INTO السنوات_المالية (السنة_المالية, تاريخ_البداية, تاريخ_النهاية) VALUES (2025, '2025-09-01', '2026-08-31')")

# إضافة بيانات تجريبية
cursor.execute("INSERT INTO الموازنة_السنوية (السنة_المالية, نوع_البند, اسم_البند, المبلغ_المتوقع) VALUES (2024, 'إيرادات', 'registration_fees', 50000)")
cursor.execute("INSERT INTO الموازنة_السنوية (السنة_المالية, نوع_البند, اسم_البند, المبلغ_المتوقع) VALUES (2024, 'إيرادات', 'monthly_duties', 300000)")
cursor.execute("INSERT INTO الموازنة_السنوية (السنة_المالية, نوع_البند, اسم_البند, المبلغ_المتوقع) VALUES (2025, 'إيرادات', 'registration_fees', 55000)")
cursor.execute("INSERT INTO الموازنة_السنوية (السنة_المالية, نوع_البند, اسم_البند, المبلغ_المتوقع) VALUES (2025, 'إيرادات', 'monthly_duties', 320000)")

# إضافة إعداد السنة المختارة
cursor.execute("INSERT INTO إعدادات_النظام (اسم_الإعداد, قيمة_الإعداد, وصف_الإعداد) VALUES ('السنة_المالية_المختارة', '2025', 'السنة المختارة')")

conn.commit()
conn.close()

print("🎉 تم إصلاح قاعدة البيانات بنجاح!")
print("يمكنك الآن تشغيل البرنامج")
'''
    
    with open('emergency_fix.py', 'w', encoding='utf-8') as f:
        f.write(fix_script)
    
    print("   ✅ تم إنشاء ملف emergency_fix.py")

def main():
    """الدالة الرئيسية للتشخيص"""
    print_header()
    
    # فحص بيئة Python
    check_python_environment()
    
    # فحص المكتبات
    missing_modules = check_required_modules()
    
    # فحص الملفات
    missing_files = check_essential_files()
    
    # فحص قاعدة البيانات
    db_ok = check_database()
    
    # اختبار الاستيراد
    test_main_imports()
    
    # إنشاء سكريبت الإصلاح
    create_fix_script()
    
    # ملخص المشاكل
    print("\n" + "=" * 60)
    print("📋 ملخص المشاكل:")
    print("=" * 60)
    
    if missing_modules:
        print(f"❌ مكتبات مفقودة: {', '.join(missing_modules)}")
        print("   💡 الحل: pip install " + " ".join(missing_modules))
    
    if missing_files:
        print(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
    
    if not db_ok:
        print("❌ مشكلة في قاعدة البيانات")
        print("   💡 الحل: شغل python emergency_fix.py")
    
    if not missing_modules and not missing_files and db_ok:
        print("✅ جميع الفحوصات نجحت!")
        print("   💡 يمكنك تشغيل البرنامج الآن")
    
    print("\n🔧 خطوات الإصلاح المقترحة:")
    print("1. شغل: python emergency_fix.py")
    print("2. ثم شغل: python main_window.py")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
