#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة التعليمات - sub263_window.py
تحتوي على شرح تفصيلي لكيفية استخدام نظام إدارة المواد الدراسية والأساتذة
"""

import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class InstructionsWindow(QDialog):
    """نافذة التعليمات التفاعلية"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setupUI()
    
    def setupUI(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("📖 تعليمات استخدام النظام")
        self.setFixedSize(1100, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق نمط احترافي للنافذة
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 12px;
            }
            QScrollArea {
                border: none;
                background-color: white;
                border-radius: 8px;
            }
            QLabel {
                background-color: transparent;
                padding: 8px;
                border-radius: 4px;
            }
        """)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # عنوان النافذة
        title_label = QLabel("📖 دليل استخدام نظام إدارة المواد الدراسية والأساتذة")
        title_font = QFont("Calibri", 15, QFont.Bold)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #1565C0; background-color: #E3F2FD; padding: 15px; border-radius: 8px; margin-bottom: 10px;")
        main_layout.addWidget(title_label)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # محتوى التعليمات
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(20, 20, 20, 20)
        content_layout.setSpacing(15)
        
        # إضافة محتوى التعليمات
        self.add_instructions_content(content_layout)
        
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)
        
        # زر الإغلاق
        close_button = QPushButton("✅ فهمت - إغلاق")
        close_button.setFont(QFont("Calibri", 13, QFont.Bold))
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        close_button.clicked.connect(self.accept)
        main_layout.addWidget(close_button)
    
    def add_instructions_content(self, layout):
        """إضافة محتوى التعليمات"""
        
        # خط النص العادي
        normal_font = QFont("Calibri", 14, QFont.Bold)
        # خط العناوين
        heading_font = QFont("Calibri", 18, QFont.Bold)
        
        # مقدمة
        intro_label = QLabel("مرحباً بك في نظام إدارة المواد الدراسية والأساتذة!")
        intro_label.setFont(heading_font)
        intro_label.setStyleSheet("color: #1565C0; background-color: #E8F4FD; padding: 12px; border-radius: 6px;")
        layout.addWidget(intro_label)
        
        intro_text = QLabel("هذا النظام يساعدك في إدارة المواد الدراسية والأساتذة والأقسام بطريقة منظمة وفعالة. يتكون من عدة تبويبات رئيسية:")
        intro_text.setFont(normal_font)
        intro_text.setWordWrap(True)
        intro_text.setStyleSheet("color: #333333; line-height: 1.6;")
        layout.addWidget(intro_text)
        
        # تبويب المجموعات
        self.add_section(layout, "📂 تبويب المجموعات", [
            "• هذا هو التبويب الأول والأهم في النظام",
            "• يقصد بالمجموعة عبارة عن عن مجموعة من التلاميذ والطلبة يدرسون مادة دراسية أو مواد دراسية في فترة زمنية محددة او فترة زمية مفتوحة",
            "• لإضافة مجموعة جديدة: اكتب اسم المجموعة واضغط 'إضافة'",
            "• يمكنك تعديل أو حذف المجموعات الموجودة باستخدام الأزرار المناسبة",
            "• كل مجموعة لها تاريخ إنشاء لتسهيل التتبع"
        ], heading_font, normal_font)
        
        # تبويب المواد الدراسية
        self.add_section(layout, "📚 تبويب المواد الدراسية", [
            "• يحتوي على جميع المواد الدراسية في المؤسسة",
            "• لإضافة مادة جديدة: اكتب اسم المادة واضغط 'إضافة'",
            "• يتم عرض المواد في جدول مع تاريخ الإضافة",
            "• يمكن تعديل أو حذف المواد حسب الحاجة",
            "• تأكد من إضافة جميع المواد قبل الانتقال للتبويبات الأخرى"
        ], heading_font, normal_font)
        
        # تبويب الأقسام
        self.add_section(layout, "🏫 تبويب الأقسام", [
            "• يحتوي على جميع الأقسام الدراسية (الصفوف)",
            "• لإضافة قسم جديد: اكتب اسم القسم واضغط 'إضافة'",
            "• مثال على الأقسام: 'الصف الأول أ'، 'الصف الثاني ب'، إلخ",
            "• يتم عرض الأقسام مع تاريخ الإضافة",
            "• يمكن إدارة الأقسام (تعديل/حذف) بسهولة"
        ], heading_font, normal_font)
        
        # تبويب الأساتذة والأقسام
        self.add_section(layout, "👨‍🏫 تبويب الأساتذة والأقسام", [
            "• هذا التبويب لربط الأساتذة بالمواد والأقسام",
            "• الخطوات المطلوبة:",
            "  1. اكتب اسم الأستاذ",
            "  2. اختر المادة من القائمة المنسدلة",
            "  3. اختر القسم من القائمة المنسدلة", 
            "  4. اختر المجموعة من القائمة المنسدلة",
            "  5. حدد نسبة الواجبات (افتراضياً 100%)",
            "  6. اضغط 'إضافة' لحفظ البيانات",
            "• يمكنك إضافة أساتذة متعددين للمجموعة المحددة دفعة واحدة"
        ], heading_font, normal_font)
        
        # تبويب سجلات الأساتذة
        self.add_section(layout, "📋 تبويب سجلات الأساتذة", [
            "• يعرض جميع سجلات الأساتذة المسجلة في النظام",
            "• يمكنك البحث عن أستاذ معين أو تصفية النتائج",
            "• التصفية متاحة حسب المادة والمجموعة",
            "• يمكن نسخ السجلات المحددة إلى مجموعة أخرى",
            "• يمكن إضافة مجموعة للأساتذة المحددين دفعة واحدة",
            "• يمكن طباعة التقارير وتصدير البيانات",
            "• يعرض إحصائيات مفيدة عن الأساتذة والمواد"
        ], heading_font, normal_font)
        
        # نصائح مهمة
        self.add_section(layout, "💡 نصائح مهمة", [
            "• ابدأ دائماً بإضافة المجموعات والمواد والأقسام قبل تسجيل الأساتذة",
            "• تأكد من صحة البيانات قبل الحفظ",
            "• استخدم البحث والتصفية للعثور على البيانات بسرعة",
            "• راجع الإحصائيات بانتظام للتأكد من اكتمال البيانات",
            "• يمكنك تعديل البيانات في أي وقت",
            "• لا تنس عمل نسخة احتياطية من قاعدة البيانات",
            "• في حالة وجود مشاكل، راجع رسائل الخطأ بعناية"
        ], heading_font, normal_font)
        
        # خاتمة
        conclusion_label = QLabel("🎯 خاتمة")
        conclusion_label.setFont(heading_font)
        conclusion_label.setStyleSheet("color: #1565C0; background-color: #E8F4FD; padding: 12px; border-radius: 6px;")
        layout.addWidget(conclusion_label)
        
        conclusion_text = QLabel("بهذا تكون قد تعلمت كيفية استخدام جميع ميزات النظام. ابدأ بالتبويب الأول وتدرج في إدخال البيانات. النظام مصمم ليكون سهل الاستخدام ومرن لتلبية احتياجاتك.")
        conclusion_text.setFont(normal_font)
        conclusion_text.setWordWrap(True)
        conclusion_text.setStyleSheet("color: #333333; background-color: #F8F9FA; padding: 12px; border-radius: 6px; line-height: 1.6;")
        layout.addWidget(conclusion_text)
    
    def add_section(self, layout, title, content_list, heading_font, normal_font):
        """إضافة قسم تعليمي"""
        # عنوان القسم
        section_title = QLabel(title)
        section_title.setFont(heading_font)
        section_title.setStyleSheet("color: #1565C0; background-color: #E8F4FD; padding: 12px; border-radius: 6px; margin-top: 10px;")
        layout.addWidget(section_title)
        
        # محتوى القسم
        for item in content_list:
            item_label = QLabel(item)
            item_label.setFont(normal_font)
            item_label.setWordWrap(True)
            item_label.setStyleSheet("color: #333333; padding: 4px 8px; line-height: 1.5;")
            layout.addWidget(item_label)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = InstructionsWindow()
    window.show()
    sys.exit(app.exec_())
