#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار النافذة الموحدة للواجبات الشهرية وواجبات التسجيل
"""

import sys
import os

def test_unified_window():
    """اختبار النافذة الموحدة"""
    try:
        print("🧪 اختبار النافذة الموحدة...")
        print("=" * 50)
        
        # استيراد النافذة
        from monthly_duties_window import MonthlyDutiesManagementWindow
        print("✅ تم استيراد النافذة بنجاح")
        
        # اختبار إنشاء النافذة
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء النافذة
        window = MonthlyDutiesManagementWindow(student_id=1)
        print("✅ تم إنشاء النافذة بنجاح")
        
        # التحقق من وجود التبويب الموحد
        if hasattr(window, 'unified_records_tab'):
            print("✅ التبويب الموحد موجود")
        else:
            print("❌ التبويب الموحد مفقود")
            return False
        
        # التحقق من وجود الجدول الموحد
        if hasattr(window, 'unified_table'):
            print("✅ الجدول الموحد موجود")
        else:
            print("❌ الجدول الموحد مفقود")
            return False
        
        # التحقق من وجود الدوال المطلوبة
        required_methods = [
            'create_unified_records_tab',
            'setup_unified_table',
            'load_unified_data',
            'select_all_records',
            'deselect_all_records',
            'update_selection_summary',
            'print_unified_receipt',
            'create_unified_receipt_content'
        ]
        
        for method in required_methods:
            if hasattr(window, method):
                print(f"✅ الدالة {method} موجودة")
            else:
                print(f"❌ الدالة {method} مفقودة")
                return False
        
        # التحقق من وجود عناصر الواجهة
        ui_elements = [
            'selected_count_label',
            'selected_total_label'
        ]
        
        for element in ui_elements:
            if hasattr(window, element):
                print(f"✅ العنصر {element} موجود")
            else:
                print(f"❌ العنصر {element} مفقود")
                return False
        
        # اختبار تحميل البيانات
        try:
            window.load_unified_data()
            print("✅ تحميل البيانات الموحدة يعمل")
        except Exception as e:
            print(f"⚠️ تحميل البيانات الموحدة: {e}")
        
        # عرض النافذة للاختبار البصري
        window.show()
        print("✅ تم عرض النافذة للاختبار")
        
        # الانتقال للتبويب الموحد
        if hasattr(window, 'tab_widget'):
            # البحث عن فهرس التبويب الموحد
            for i in range(window.tab_widget.count()):
                tab_text = window.tab_widget.tabText(i)
                if "الجدول الموحد" in tab_text:
                    window.tab_widget.setCurrentIndex(i)
                    print(f"✅ تم الانتقال للتبويب الموحد (فهرس {i})")
                    break
        
        print("\n🎉 جميع الاختبارات نجحت!")
        print("💡 يمكنك الآن:")
        print("   • عرض جميع السجلات في جدول واحد")
        print("   • تحديد السجلات باستخدام مربعات الاختيار")
        print("   • طباعة توصيل موحد للسجلات المحددة")
        print("   • رؤية ملخص الاختيار في الوقت الفعلي")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔍 اختبار النافذة الموحدة للواجبات")
    print("=" * 60)
    print(f"🐍 Python: {sys.version}")
    print(f"📁 المجلد: {os.getcwd()}")
    
    success = test_unified_window()
    
    if success:
        print("\n✅ الاختبار نجح!")
        print("🚀 النافذة الموحدة جاهزة للاستخدام")
    else:
        print("\n❌ الاختبار فشل!")
        print("🔧 راجع الأخطاء أعلاه")
    
    print("\n📋 المميزات الجديدة:")
    print("• جدول موحد يجمع سجلات الأداء وواجبات التسجيل")
    print("• مربعات اختيار لتحديد السجلات المطلوبة")
    print("• أزرار تحديد/إلغاء تحديد الكل")
    print("• ملخص فوري للسجلات المحددة والمبلغ الإجمالي")
    print("• طباعة توصيل موحد للسجلات المحددة")
    print("• تلوين مختلف لأنواع السجلات المختلفة")
    print("• المبلغ المطلوب لواجبات التسجيل من جدول_البيانات")
    print("• ترتيب السجلات: واجبات التسجيل أولاً ثم واجبات الأداء")
    print("• خط الجدول: Calibri 13 أسود غامق")
    print("• طباعة التوصيل بنفس طريقة واجبات الأداء (طابعة حرارية)")
    print("• إلغاء التحديد تلقائياً بعد طباعة التوصيل")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
    
    input("\nاضغط Enter للخروج...")
