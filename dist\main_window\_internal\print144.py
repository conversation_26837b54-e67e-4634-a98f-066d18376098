#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import sqlite3
from datetime import datetime
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                             QComboBox, QPushButton, QMessageBox, QApplication)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

def get_database_path():
    """
    الحصول على مسار قاعدة البيانات الصحيح
    يعمل في البيئة العادية وبعد التحزيم
    """
    if getattr(sys, 'frozen', False):
        # البرنامج محزم - البحث في مجلد البرنامج الرئيسي
        application_path = os.path.dirname(sys.executable)
    else:
        # البرنامج يعمل من المصدر
        application_path = os.path.dirname(os.path.abspath(__file__))

    db_path = os.path.join(application_path, 'data.db')

    # التحقق من وجود قاعدة البيانات
    if not os.path.exists(db_path):
        # محاولة البحث في مجلد أعلى
        parent_path = os.path.dirname(application_path)
        alternative_db_path = os.path.join(parent_path, 'data.db')
        if os.path.exists(alternative_db_path):
            return alternative_db_path

    return db_path

try:
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display
except ImportError:
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display

class ArabicPDF(FPDF):
    def __init__(self):
        super().__init__('P', 'mm', 'A4')
        self.set_margins(5, 5, 5)
        self.set_auto_page_break(auto=True, margin=5)
        
        # إضافة الخطوط
        fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
        calibri_path = os.path.join(fonts_dir, 'calibri.ttf')
        calibri_bold_path = os.path.join(fonts_dir, 'calibrib.ttf')
        
        if os.path.exists(calibri_path):
            self.add_font('Calibri', '', calibri_path)
            self.calibri_available = True
        else:
            self.calibri_available = False
            
        if os.path.exists(calibri_bold_path):
            self.add_font('Calibri', 'B', calibri_bold_path)
            self.calibri_bold_available = True
        else:
            self.calibri_bold_available = False

    def ar_text(self, txt):
        reshaped = arabic_reshaper.reshape(str(txt))
        return get_display(reshaped)
        
    def set_main_title_font(self):
        """خط العناوين الرئيسية - Calibri 15 Bold"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', 15)
        else:
            self.set_font('Arial', 'B', 15)

    def set_subtitle_font(self):
        """خط العناوين الفرعية - Calibri 14 Bold"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', 14)
        else:
            self.set_font('Arial', 'B', 14)

    def set_detail_font(self):
        """خط التفاصيل - Calibri 13 Bold"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', 13)
        else:
            self.set_font('Arial', 'B', 13)

    def set_table_header_font(self):
        """خط رؤوس الجدول - Calibri 13 Bold"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', 13)
        else:
            self.set_font('Arial', 'B', 13)

    def set_table_row_font(self):
        """خط صفوف الجدول - Calibri 12 Bold"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', 12)
        else:
            self.set_font('Arial', 'B', 12)

class StudentReportDialog(QDialog):
    def __init__(self, db_path, parent=None):
        super().__init__(parent)
        self.db_path = db_path
        self.setWindowTitle("تقرير الغياب حسب التلميذ")
        self.setModal(True)
        self.resize(400, 200)
        self.setup_ui()
        self.load_students()

    def setup_ui(self):
        layout = QVBoxLayout()
        
        # عنوان
        title = QLabel("تقرير الغياب حسب التلميذ")
        title.setFont(QFont("Calibri", 15, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # اختيار التلميذ
        student_layout = QHBoxLayout()
        student_label = QLabel("اختر التلميذ:")
        student_label.setFont(QFont("Calibri", 13, QFont.Bold))
        self.student_combo = QComboBox()
        self.student_combo.setFont(QFont("Calibri", 12, QFont.Bold))
        student_layout.addWidget(student_label)
        student_layout.addWidget(self.student_combo)
        layout.addLayout(student_layout)
        
        # أزرار
        buttons_layout = QHBoxLayout()
        
        generate_btn = QPushButton("إنشاء التقرير")
        generate_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        generate_btn.clicked.connect(self.generate_report)
        
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(generate_btn)
        buttons_layout.addWidget(cancel_btn)
        layout.addLayout(buttons_layout)
        
        self.setLayout(layout)

    def load_students(self):
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("""
                SELECT id, اسم_التلميذ, رمز_التلميذ, القسم
                FROM جدول_البيانات 
                WHERE اسم_التلميذ IS NOT NULL AND اسم_التلميذ != ''
                ORDER BY اسم_التلميذ
            """)
            students = cursor.fetchall()
            conn.close()
            
            for student in students:
                student_id, name, code, section = student
                display_text = f"{name} - {code} - {section}"
                self.student_combo.addItem(display_text, student_id)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل التلاميذ: {str(e)}")

    def generate_report(self):
        if self.student_combo.currentIndex() == -1:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار تلميذ")
            return
            
        student_id = self.student_combo.currentData()
        try:
            success, output_path = create_student_report(student_id, self.db_path)
            if success:
                QMessageBox.information(self, "نجح", f"تم إنشاء التقرير بنجاح!\n\nالمسار: {output_path}")
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في إنشاء التقرير")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء التقرير: {str(e)}")

class SectionReportDialog(QDialog):
    def __init__(self, db_path, parent=None):
        super().__init__(parent)
        self.db_path = db_path
        self.setWindowTitle("تقرير الغياب حسب القسم")
        self.setModal(True)
        self.resize(400, 200)
        self.setup_ui()
        self.load_sections()

    def setup_ui(self):
        layout = QVBoxLayout()
        
        # عنوان
        title = QLabel("تقرير الغياب حسب القسم")
        title.setFont(QFont("Calibri", 15, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # اختيار القسم
        section_layout = QHBoxLayout()
        section_label = QLabel("اختر القسم:")
        section_label.setFont(QFont("Calibri", 13, QFont.Bold))
        self.section_combo = QComboBox()
        self.section_combo.setFont(QFont("Calibri", 12, QFont.Bold))
        section_layout.addWidget(section_label)
        section_layout.addWidget(self.section_combo)
        layout.addLayout(section_layout)
        
        # أزرار
        buttons_layout = QHBoxLayout()
        
        generate_btn = QPushButton("إنشاء التقرير")
        generate_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        generate_btn.clicked.connect(self.generate_report)
        
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(generate_btn)
        buttons_layout.addWidget(cancel_btn)
        layout.addLayout(buttons_layout)
        
        self.setLayout(layout)

    def load_sections(self):
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("""
                SELECT DISTINCT القسم 
                FROM جدول_البيانات 
                WHERE القسم IS NOT NULL AND القسم != ''
                ORDER BY القسم
            """)
            sections = cursor.fetchall()
            conn.close()
            
            for section in sections:
                self.section_combo.addItem(section[0])
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الأقسام: {str(e)}")

    def generate_report(self):
        if self.section_combo.currentIndex() == -1:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار قسم")
            return
            
        section_name = self.section_combo.currentText()
        try:
            success, output_path = create_section_report(section_name, self.db_path)
            if success:
                QMessageBox.information(self, "نجح", f"تم إنشاء التقرير بنجاح!\n\nالمسار: {output_path}")
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في إنشاء التقرير")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء التقرير: {str(e)}")

def create_student_report(student_id, db_path):
    """إنشاء تقرير الغياب حسب التلميذ"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # جلب بيانات التلميذ
        cursor.execute("""
            SELECT اسم_التلميذ, رمز_التلميذ, القسم
            FROM جدول_البيانات 
            WHERE id = ?
        """, (student_id,))
        student_data = cursor.fetchone()
        
        if not student_data:
            return False, None
            
        student_name, student_code, student_section = student_data
        
        # جلب معلومات القسم
        cursor.execute("""
            SELECT القسم, المادة, اسم_الاستاذ
            FROM جدول_المواد_والاقسام
            WHERE القسم = ?
            LIMIT 1
        """, (student_section,))
        section_info = cursor.fetchone()
        if not section_info:
            section_info = (student_section, "غير محدد", "غير محدد")
        
        # جلب بيانات الغياب مجمعة حسب الشهر
        cursor.execute("""
            SELECT 
                strftime('%Y', تاريخ_الغياب) as السنة,
                strftime('%m', تاريخ_الغياب) as الشهر,
                COUNT(*) as عدد_ايام_الغياب,
                SUM(عدد_الحصص_المتغيب_عنها) as اجمالي_حصص_الغياب,
                GROUP_CONCAT(تاريخ_الغياب || ' (' || عدد_الحصص_المتغيب_عنها || ' حصص)', ', ') as تفاصيل_الغياب
            FROM تدوين_الغياب 
            WHERE معرف_التلميذ = ?
            GROUP BY السنة, الشهر
            ORDER BY السنة DESC, الشهر DESC
        """, (student_id,))
        absence_data = cursor.fetchall()
        
        conn.close()
        
        # إنشاء PDF
        pdf = ArabicPDF()
        pdf.add_page()
        
        # الشعار والعنوان
        add_header(pdf, "تقرير الغياب حسب التلميذ")
        
        y = 40
        
        # معلومات التلميذ
        pdf.set_subtitle_font()
        pdf.set_xy(5, y)
        pdf.cell(0, 8, pdf.ar_text(f"التلميذ: {student_name} - الرمز: {student_code}"), 0, 1, 'R')
        y += 10
        
        # جدول معلومات القسم
        y = add_section_table(pdf, section_info, y)
        y += 10
        
        # جدول الغياب بالتفصيل
        add_student_absence_table(pdf, absence_data, y)
        
        # حفظ الملف
        reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الغياب التفصيلية')
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"تقرير_غياب_التلميذ_{student_name}_{timestamp}.pdf"
        output_path = os.path.join(reports_dir, filename)
        
        pdf.output(output_path)
        
        # فتح الملف
        if os.name == 'nt':
            os.startfile(output_path)
        
        return True, output_path
        
    except Exception as e:
        print(f"خطأ في إنشاء تقرير التلميذ: {e}")
        return False, None

def create_section_report(section_name, db_path):
    """إنشاء تقرير الغياب حسب القسم"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # جلب معلومات القسم
        cursor.execute("""
            SELECT القسم, المادة, اسم_الاستاذ
            FROM جدول_المواد_والاقسام
            WHERE القسم = ?
            LIMIT 1
        """, (section_name,))
        section_info = cursor.fetchone()
        if not section_info:
            section_info = (section_name, "غير محدد", "غير محدد")

        # جلب تلاميذ القسم مع بيانات الغياب مجمعة حسب الشهر
        cursor.execute("""
            SELECT
                ت.اسم_التلميذ,
                ت.رمز_التلميذ,
                strftime('%Y', غ.تاريخ_الغياب) as السنة,
                strftime('%m', غ.تاريخ_الغياب) as الشهر,
                COUNT(*) as عدد_ايام_الغياب,
                SUM(غ.عدد_الحصص_المتغيب_عنها) as اجمالي_حصص_الغياب
            FROM جدول_البيانات ت
            LEFT JOIN تدوين_الغياب غ ON ت.id = غ.معرف_التلميذ
            WHERE ت.القسم = ? AND ت.اسم_التلميذ IS NOT NULL AND ت.اسم_التلميذ != ''
            GROUP BY ت.id, السنة, الشهر
            ORDER BY ت.اسم_التلميذ, السنة DESC, الشهر DESC
        """, (section_name,))
        students_data = cursor.fetchall()

        conn.close()

        # إنشاء PDF
        pdf = ArabicPDF()
        pdf.add_page()

        # الشعار والعنوان
        add_header(pdf, f"تقرير الغياب حسب القسم - {section_name}")

        y = 40

        # جدول معلومات القسم
        y = add_section_table(pdf, section_info, y)
        y += 10

        # جدول الغياب بالتفصيل حسب التلاميذ
        add_section_absence_table(pdf, students_data, y)

        # حفظ الملف
        reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الغياب التفصيلية')
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"تقرير_غياب_القسم_{section_name}_{timestamp}.pdf"
        output_path = os.path.join(reports_dir, filename)

        pdf.output(output_path)

        # فتح الملف
        if os.name == 'nt':
            os.startfile(output_path)

        return True, output_path

    except Exception as e:
        print(f"خطأ في إنشاء تقرير القسم: {e}")
        return False, None

def add_header(pdf, title):
    """إضافة الشعار والعنوان"""
    # الشعار (إذا كان متوفراً)
    logo_path = None
    try:
        conn = sqlite3.connect(get_database_path())
        cursor = conn.cursor()
        cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
        logo_row = cursor.fetchone()
        if logo_row and os.path.exists(logo_row[0]):
            logo_path = logo_row[0]
        conn.close()
    except:
        pass

    y = 5
    if logo_path:
        pdf.image(logo_path, x=85, y=y, w=40, h=20)
        y += 25
    else:
        # شعار نصي
        pdf.set_main_title_font()
        pdf.set_xy(5, y)
        pdf.cell(0, 10, pdf.ar_text("🏫 مؤسسة التعليم"), 0, 1, 'C')
        y += 15

    # العنوان الرئيسي
    pdf.set_main_title_font()
    pdf.set_xy(5, y)
    pdf.cell(0, 10, pdf.ar_text(title), 0, 1, 'C')

def add_section_table(pdf, section_info, y):
    """إضافة جدول معلومات القسم"""
    pdf.set_subtitle_font()
    pdf.set_xy(5, y)
    pdf.cell(0, 8, pdf.ar_text("معلومات القسم"), 0, 1, 'R')
    y += 10

    # رأس الجدول
    pdf.set_table_header_font()
    pdf.set_xy(5, y)
    pdf.cell(60, 8, pdf.ar_text("القسم"), 1, 0, 'C')
    pdf.cell(60, 8, pdf.ar_text("المادة"), 1, 0, 'C')
    pdf.cell(70, 8, pdf.ar_text("الأستاذ(ة)"), 1, 1, 'C')
    y += 8

    # بيانات القسم
    pdf.set_table_row_font()
    pdf.set_xy(5, y)
    pdf.cell(60, 8, pdf.ar_text(str(section_info[0])), 1, 0, 'C')
    pdf.cell(60, 8, pdf.ar_text(str(section_info[1])), 1, 0, 'C')
    pdf.cell(70, 8, pdf.ar_text(str(section_info[2])), 1, 1, 'C')

    return y + 8

def add_student_absence_table(pdf, absence_data, y):
    """إضافة جدول الغياب للتلميذ مجمع حسب الشهر"""
    pdf.set_subtitle_font()
    pdf.set_xy(5, y)
    pdf.cell(0, 8, pdf.ar_text("تفاصيل الغياب حسب الشهر"), 0, 1, 'R')
    y += 10

    if not absence_data:
        pdf.set_detail_font()
        pdf.set_xy(5, y)
        pdf.cell(0, 8, pdf.ar_text("لا توجد بيانات غياب لهذا التلميذ"), 0, 1, 'C')
        return

    # رأس الجدول
    pdf.set_table_header_font()
    pdf.set_xy(5, y)
    pdf.cell(30, 8, pdf.ar_text("السنة"), 1, 0, 'C')
    pdf.cell(30, 8, pdf.ar_text("الشهر"), 1, 0, 'C')
    pdf.cell(30, 8, pdf.ar_text("أيام الغياب"), 1, 0, 'C')
    pdf.cell(30, 8, pdf.ar_text("حصص الغياب"), 1, 0, 'C')
    pdf.cell(70, 8, pdf.ar_text("تفاصيل الغياب"), 1, 1, 'C')
    y += 8

    # بيانات الغياب
    pdf.set_table_row_font()
    month_names = ["", "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                   "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]

    for row in absence_data:
        year, month, absence_days, absence_sessions, details = row
        month_name = month_names[int(month)] if month else ""

        # تقسيم التفاصيل إذا كانت طويلة
        details_text = str(details) if details else ""
        if len(details_text) > 50:
            details_text = details_text[:47] + "..."

        pdf.set_xy(5, y)
        pdf.cell(30, 6, str(year) if year else "", 1, 0, 'C')
        pdf.cell(30, 6, pdf.ar_text(month_name), 1, 0, 'C')
        pdf.cell(30, 6, str(absence_days) if absence_days else "0", 1, 0, 'C')
        pdf.cell(30, 6, str(absence_sessions) if absence_sessions else "0", 1, 0, 'C')
        pdf.cell(70, 6, pdf.ar_text(details_text), 1, 1, 'C')
        y += 6

        # انتقال لصفحة جديدة عند الحاجة
        if y > 270:
            pdf.add_page()
            y = 20

def add_section_absence_table(pdf, students_data, y):
    """إضافة جدول الغياب للقسم حسب التلاميذ مجمع حسب الشهر"""
    pdf.set_subtitle_font()
    pdf.set_xy(5, y)
    pdf.cell(0, 8, pdf.ar_text("تفاصيل الغياب حسب التلاميذ والشهور"), 0, 1, 'R')
    y += 10

    if not students_data:
        pdf.set_detail_font()
        pdf.set_xy(5, y)
        pdf.cell(0, 8, pdf.ar_text("لا توجد بيانات غياب لهذا القسم"), 0, 1, 'C')
        return

    # رأس الجدول
    pdf.set_table_header_font()
    pdf.set_xy(5, y)
    pdf.cell(50, 8, pdf.ar_text("اسم التلميذ"), 1, 0, 'C')
    pdf.cell(30, 8, pdf.ar_text("الرمز"), 1, 0, 'C')
    pdf.cell(25, 8, pdf.ar_text("السنة"), 1, 0, 'C')
    pdf.cell(25, 8, pdf.ar_text("الشهر"), 1, 0, 'C')
    pdf.cell(25, 8, pdf.ar_text("أيام الغياب"), 1, 0, 'C')
    pdf.cell(25, 8, pdf.ar_text("حصص الغياب"), 1, 1, 'C')
    y += 8

    # بيانات التلاميذ
    pdf.set_table_row_font()
    month_names = ["", "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                   "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]

    for row in students_data:
        name, code, year, month, absence_days, absence_sessions = row
        month_name = month_names[int(month)] if month else ""

        pdf.set_xy(5, y)
        pdf.cell(50, 6, pdf.ar_text(str(name) if name else ""), 1, 0, 'C')
        pdf.cell(30, 6, pdf.ar_text(str(code) if code else ""), 1, 0, 'C')
        pdf.cell(25, 6, str(year) if year else "", 1, 0, 'C')
        pdf.cell(25, 6, pdf.ar_text(month_name), 1, 0, 'C')
        pdf.cell(25, 6, str(absence_days) if absence_days else "0", 1, 0, 'C')
        pdf.cell(25, 6, str(absence_sessions) if absence_sessions else "0", 1, 1, 'C')
        y += 6

        # انتقال لصفحة جديدة عند الحاجة
        if y > 270:
            pdf.add_page()
            y = 20

def create_vertical_statistics_report(year, db_path):
    """إنشاء تقرير إحصائيات الغياب مع جداول عمودية متساوية"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # إنشاء PDF
        pdf = ArabicPDF()
        pdf.add_page()

        # الشعار والعنوان
        add_header(pdf, f"تقرير إحصائيات الغياب الورقية - {year}")

        y = 50

        # إحصائيات عامة للسنة
        cursor.execute("""
            SELECT
                COUNT(DISTINCT معرف_التلميذ) as عدد_التلاميذ_الغائبين,
                COUNT(DISTINCT تاريخ_الغياب) as عدد_ايام_الغياب,
                SUM(عدد_الحصص_المتغيب_عنها) as اجمالي_حصص_الغياب,
                AVG(عدد_الحصص_المتغيب_عنها) as متوسط_حصص_الغياب
            FROM تدوين_الغياب
            WHERE strftime('%Y', تاريخ_الغياب) = ?
        """, (str(year),))

        general_stats = cursor.fetchone()

        if general_stats and general_stats[0]:
            عدد_التلاميذ, عدد_الايام, اجمالي_الحصص, متوسط_الحصص = general_stats

            # عنوان الإحصائيات العامة
            pdf.set_subtitle_font()
            pdf.set_xy(5, y)
            pdf.cell(0, 8, pdf.ar_text("📊 الإحصائيات العامة"), 0, 1, 'C')
            y += 10

            # إطار للإحصائيات
            pdf.set_detail_font()

            # عدد التلاميذ الغائبين
            pdf.set_xy(50, y)
            pdf.cell(100, 6, pdf.ar_text(f"عدد التلاميذ الغائبين: {عدد_التلاميذ}"), 1, 1, 'C')
            y += 7

            # عدد أيام الغياب
            pdf.set_xy(50, y)
            pdf.cell(100, 6, pdf.ar_text(f"إجمالي أيام الغياب: {عدد_الايام}"), 1, 1, 'C')
            y += 7

            # إجمالي حصص الغياب
            pdf.set_xy(50, y)
            pdf.cell(100, 6, pdf.ar_text(f"إجمالي حصص الغياب: {اجمالي_الحصص or 0}"), 1, 1, 'C')
            y += 7

            # متوسط حصص الغياب
            pdf.set_xy(50, y)
            pdf.cell(100, 6, pdf.ar_text(f"متوسط حصص الغياب: {متوسط_الحصص:.1f}"), 1, 1, 'C')
            y += 15

            # جلب بيانات الأقسام
            cursor.execute("""
                SELECT
                    القسم,
                    COUNT(DISTINCT معرف_التلميذ) as عدد_التلاميذ_الغائبين,
                    COUNT(DISTINCT تاريخ_الغياب) as اجمالي_ايام_الغياب,
                    SUM(عدد_الحصص_المتغيب_عنها) as اجمالي_حصص_الغياب
                FROM تدوين_الغياب
                WHERE strftime('%Y', تاريخ_الغياب) = ?
                GROUP BY القسم
                ORDER BY اجمالي_حصص_الغياب DESC
            """, (str(year),))

            sections_data = cursor.fetchall()

            # جدول ملخص الغياب حسب الأقسام (الجدول الأول)
            if sections_data:
                # عنوان جدول الأقسام
                pdf.set_subtitle_font()
                pdf.set_xy(5, y)
                pdf.cell(0, 8, pdf.ar_text("🏫 ملخص الغياب حسب الأقسام"), 0, 1, 'C')
                y += 10

                # رأس جدول الأقسام - في الوسط ومتساوي العرض
                pdf.set_table_header_font()
                pdf.set_xy(30, y)
                pdf.cell(35, 6, pdf.ar_text("القسم"), 1, 0, 'C')
                pdf.cell(35, 6, pdf.ar_text("التلاميذ الغائبين"), 1, 0, 'C')
                pdf.cell(35, 6, pdf.ar_text("أيام الغياب"), 1, 0, 'C')
                pdf.cell(35, 6, pdf.ar_text("حصص الغياب"), 1, 1, 'C')
                y += 6

                # بيانات الأقسام
                pdf.set_table_row_font()
                for section_data in sections_data:
                    section_name, absent_students, absence_days, absence_sessions = section_data
                    pdf.set_xy(30, y)
                    pdf.cell(35, 5, pdf.ar_text(str(section_name or "")), 1, 0, 'C')
                    pdf.cell(35, 5, str(absent_students), 1, 0, 'C')
                    pdf.cell(35, 5, str(absence_days), 1, 0, 'C')
                    pdf.cell(35, 5, str(absence_sessions or 0), 1, 1, 'C')
                    y += 5

                y += 15  # مسافة بين الجداول

            # جلب البيانات الشهرية
            cursor.execute("""
                SELECT
                    strftime('%m', تاريخ_الغياب) as الشهر,
                    COUNT(DISTINCT معرف_التلميذ) as عدد_التلاميذ_الغائبين,
                    COUNT(DISTINCT تاريخ_الغياب) as اجمالي_ايام_الغياب,
                    SUM(عدد_الحصص_المتغيب_عنها) as اجمالي_حصص_الغياب
                FROM تدوين_الغياب
                WHERE strftime('%Y', تاريخ_الغياب) = ?
                GROUP BY strftime('%m', تاريخ_الغياب)
                ORDER BY الشهر
            """, (str(year),))

            monthly_data = cursor.fetchall()

            # جدول الغيابات الشهرية (الجدول الثاني - تحت الأول)
            if monthly_data:
                # عنوان الجدول الشهري
                pdf.set_subtitle_font()
                pdf.set_xy(5, y)
                pdf.cell(0, 8, pdf.ar_text("📅 الغيابات الشهرية"), 0, 1, 'C')
                y += 10

                # رأس الجدول الشهري - نفس العرض والمحاذاة
                pdf.set_table_header_font()
                pdf.set_xy(30, y)
                pdf.cell(35, 6, pdf.ar_text("الشهر"), 1, 0, 'C')
                pdf.cell(35, 6, pdf.ar_text("التلاميذ الغائبين"), 1, 0, 'C')
                pdf.cell(35, 6, pdf.ar_text("أيام الغياب"), 1, 0, 'C')
                pdf.cell(35, 6, pdf.ar_text("حصص الغياب"), 1, 1, 'C')
                y += 6

                # أسماء الشهور
                month_names = ["", "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                              "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]

                # بيانات الشهور
                pdf.set_table_row_font()
                for month_data in monthly_data:
                    month_num, absent_students, absence_days, absence_sessions = month_data
                    month_name = month_names[int(month_num)]
                    pdf.set_xy(30, y)
                    pdf.cell(35, 5, pdf.ar_text(month_name), 1, 0, 'C')
                    pdf.cell(35, 5, str(absent_students), 1, 0, 'C')
                    pdf.cell(35, 5, str(absence_days), 1, 0, 'C')
                    pdf.cell(35, 5, str(absence_sessions or 0), 1, 1, 'C')
                    y += 5

            # إذا لم توجد بيانات
            if not sections_data and not monthly_data:
                pdf.set_detail_font()
                pdf.set_xy(5, y)
                pdf.cell(0, 6, pdf.ar_text("لا توجد بيانات للأقسام أو الشهور"), 0, 1, 'C')
        else:
            # لا توجد بيانات
            y = 80
            pdf.set_subtitle_font()
            pdf.set_xy(5, y)
            pdf.cell(0, 8, pdf.ar_text("📊 لا توجد بيانات غياب للسنة المحددة"), 0, 1, 'C')

        conn.close()

        # حفظ الملف
        reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الغياب التفصيلية')
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"احصائيات_الغياب_العمودية_{year}_{timestamp}.pdf"
        output_path = os.path.join(reports_dir, filename)

        pdf.output(output_path)

        # فتح الملف
        if os.name == 'nt':
            os.startfile(output_path)

        return True, output_path

    except Exception as e:
        print(f"خطأ في إنشاء تقرير الإحصائيات: {e}")
        return False, None

def show_student_report_dialog(db_path, parent=None):
    """عرض نافذة تقرير التلميذ"""
    dialog = StudentReportDialog(db_path, parent)
    return dialog.exec_()

def show_section_report_dialog(db_path, parent=None):
    """عرض نافذة تقرير القسم"""
    dialog = SectionReportDialog(db_path, parent)
    return dialog.exec_()

if __name__ == "__main__":
    app = QApplication(sys.argv)

    # اختبار النوافذ
    db_path = get_database_path()

    # عرض نافذة اختيار نوع التقرير
    from PyQt5.QtWidgets import QInputDialog

    items = ["تقرير التلميذ", "تقرير القسم"]
    item, ok = QInputDialog.getItem(None, "اختيار التقرير", "اختر نوع التقرير:", items, 0, False)

    if ok and item:
        if item == "تقرير التلميذ":
            show_student_report_dialog(db_path)
        elif item == "تقرير القسم":
            show_section_report_dialog(db_path)

    sys.exit()
