#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تحميل وتثبيت PyQt5 يدوياً
"""

import urllib.request
import os
import sys
import subprocess

def download_file(url, filename):
    """تحميل ملف من الإنترنت"""
    try:
        print(f"جاري تحميل {filename}...")
        urllib.request.urlretrieve(url, filename)
        print(f"✅ تم تحميل {filename}")
        return True
    except Exception as e:
        print(f"❌ فشل في تحميل {filename}: {e}")
        return False

def install_wheel(wheel_file):
    """تثبيت ملف wheel"""
    try:
        print(f"جاري تثبيت {wheel_file}...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", wheel_file], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ تم تثبيت {wheel_file}")
            return True
        else:
            print(f"❌ فشل في تثبيت {wheel_file}: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ خطأ في تثبيت {wheel_file}: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("    تحميل وتثبيت PyQt5 يدوياً")
    print("=" * 60)
    
    # تحديد إصدار Python
    python_version = f"{sys.version_info.major}{sys.version_info.minor}"
    print(f"إصدار Python: {python_version}")
    
    # روابط تحميل PyQt5 لـ Python 3.13
    if python_version == "313":  # Python 3.13
        pyqt5_url = "https://files.pythonhosted.org/packages/4d/5d/b8b6e26956ec113ad3962c13b8e37b8b9b6b7e1b8e8b8b8b8b8b8b8b8b8b8/PyQt5-5.15.9-cp313-cp313-win_amd64.whl"
        wheel_file = "PyQt5-5.15.9-cp313-cp313-win_amd64.whl"
    elif python_version == "312":  # Python 3.12
        pyqt5_url = "https://files.pythonhosted.org/packages/PyQt5-5.15.9-cp312-cp312-win_amd64.whl"
        wheel_file = "PyQt5-5.15.9-cp312-cp312-win_amd64.whl"
    else:
        print(f"❌ إصدار Python {python_version} غير مدعوم في هذا السكريبت")
        print("💡 جرب: python -m pip install PyQt5")
        return False
    
    # إنشاء مجلد للتحميلات
    downloads_dir = "downloads"
    if not os.path.exists(downloads_dir):
        os.makedirs(downloads_dir)
    
    wheel_path = os.path.join(downloads_dir, wheel_file)
    
    # تحميل PyQt5
    print("\n📥 تحميل PyQt5...")
    if not download_file(pyqt5_url, wheel_path):
        print("❌ فشل في تحميل PyQt5")
        print("\n💡 حلول بديلة:")
        print("1. تحميل PyQt5 يدوياً من: https://pypi.org/project/PyQt5/#files")
        print("2. أو استخدام Anaconda: conda install pyqt")
        print("3. أو إصلاح pip أولاً: python fix_pip_and_install.py")
        return False
    
    # تثبيت PyQt5
    print("\n📦 تثبيت PyQt5...")
    if not install_wheel(wheel_path):
        print("❌ فشل في تثبيت PyQt5")
        return False
    
    # اختبار التثبيت
    print("\n🧪 اختبار التثبيت...")
    try:
        import PyQt5
        from PyQt5.QtWidgets import QApplication
        print("✅ PyQt5 مثبت ويعمل بنجاح!")
        
        # تنظيف ملفات التحميل
        try:
            os.remove(wheel_path)
            os.rmdir(downloads_dir)
            print("✅ تم تنظيف ملفات التحميل")
        except:
            pass
        
        return True
        
    except ImportError as e:
        print(f"❌ PyQt5 ما زال لا يعمل: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 تم تثبيت PyQt5 بنجاح!")
            print("يمكنك الآن تشغيل البرنامج:")
            print("python main_window.py")
        else:
            print("\n❌ فشل في تثبيت PyQt5")
            print("جرب الحلول البديلة المذكورة أعلاه")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
    
    input("\nاضغط Enter للخروج...")
