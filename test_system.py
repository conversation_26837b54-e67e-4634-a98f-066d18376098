#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ملف اختبار النظام الجديد
"""

import sqlite3
import os

def create_test_data():
    """إنشاء بيانات تجريبية للاختبار"""
    
    # إنشاء قاعدة بيانات تجريبية
    conn = sqlite3.connect('data.db')
    cursor = conn.cursor()
    
    # إنشاء جدول المواد والأقسام
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS جدول_المواد_والاقسام (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            المادة TEXT,
            القسم TEXT,
            اسم_الاستاذ TEXT
        )
    ''')
    
    # حذف البيانات الموجودة
    cursor.execute('DELETE FROM جدول_المواد_والاقسام')
    
    # إدخال بيانات تجريبية
    test_data = [
        ('الرياضيات', 'قسم أ', 'أحمد محمد'),
        ('الرياضيات', 'قسم ب', 'فاطمة علي'),
        ('اللغة العربية', 'قسم أ', 'محمد حسن'),
        ('اللغة العربية', 'قسم ب', 'عائشة أحمد'),
        ('اللغة الفرنسية', 'قسم أ', 'سارة محمود'),
        ('اللغة الفرنسية', 'قسم ب', 'يوسف إبراهيم'),
        ('الفيزياء والكيمياء', 'قسم أ', 'خالد عبدالله'),
        ('الفيزياء والكيمياء', 'قسم ب', 'نور الدين'),
        ('علوم الحياة والأرض', 'قسم أ', 'مريم سعيد'),
        ('علوم الحياة والأرض', 'قسم ب', 'عبدالرحمن علي'),
        ('التاريخ والجغرافيا', 'قسم أ', 'زينب محمد'),
        ('التاريخ والجغرافيا', 'قسم ب', 'حسام الدين'),
        ('التربية الإسلامية', 'قسم أ', 'عبدالله أحمد'),
        ('التربية الإسلامية', 'قسم ب', 'خديجة حسن'),
        ('اللغة الإنجليزية', 'قسم أ', 'ليلى عبدالعزيز'),
        ('اللغة الإنجليزية', 'قسم ب', 'عمر سالم'),
    ]
    
    cursor.executemany('''
        INSERT INTO جدول_المواد_والاقسام (المادة, القسم, اسم_الاستاذ)
        VALUES (?, ?, ?)
    ''', test_data)
    
    conn.commit()
    conn.close()
    
    print("✅ تم إنشاء البيانات التجريبية بنجاح!")
    print(f"📊 تم إدخال {len(test_data)} قسم دراسي")
    print("\n📚 الأقسام المتاحة:")
    for i, (subject, section, teacher) in enumerate(test_data, 1):
        print(f"{i:2d}. {subject} - {section} - {teacher}")

def check_database():
    """فحص قاعدة البيانات"""
    if not os.path.exists('data.db'):
        print("❌ قاعدة البيانات غير موجودة")
        return False
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # فحص جدول المواد والأقسام
        cursor.execute("SELECT COUNT(*) FROM جدول_المواد_والاقسام")
        sections_count = cursor.fetchone()[0]
        
        # فحص جدول التسجيل
        cursor.execute("""
            SELECT COUNT(*) FROM sqlite_master 
            WHERE type='table' AND name='تسجيل_التلاميذ_متعدد_الاقسام'
        """)
        table_exists = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"✅ قاعدة البيانات موجودة")
        print(f"📊 عدد الأقسام المتاحة: {sections_count}")
        print(f"📋 جدول التسجيل: {'موجود' if table_exists else 'غير موجود'}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🎓 اختبار نظام تسجيل التلاميذ في أقسام متعددة")
    print("=" * 50)
    
    # فحص قاعدة البيانات
    print("\n1️⃣ فحص قاعدة البيانات...")
    if not check_database():
        print("\n2️⃣ إنشاء بيانات تجريبية...")
        create_test_data()
    else:
        print("\n2️⃣ إنشاء بيانات تجريبية جديدة...")
        create_test_data()
    
    print("\n" + "=" * 50)
    print("🚀 يمكنك الآن تشغيل النظام باستخدام:")
    print("   python run_student_registration.py")
    print("\n💡 نصائح للاختبار:")
    print("   - جرب إدخال اسم تلميذ لرؤية الرمز التلقائي")
    print("   - اختر عدة أقسام وأدخل واجبات مختلفة")
    print("   - راجع الملخص لرؤية المجاميع المالية")
    print("   - جرب طباعة التقرير")

if __name__ == "__main__":
    main()
