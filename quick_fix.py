import sqlite3
import os

# حذف قاعدة البيانات القديمة
if os.path.exists("data.db"):
    os.remove("data.db")

# إنشاء قاعدة بيانات جديدة
conn = sqlite3.connect("data.db")
cursor = conn.cursor()

# جدول السنوات المالية
cursor.execute("""
CREATE TABLE السنوات_المالية (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    السنة_المالية INTEGER UNIQUE NOT NULL,
    تاريخ_البداية DATE NOT NULL,
    تاريخ_النهاية DATE NOT NULL,
    الحالة TEXT DEFAULT 'نشطة'
)
""")

# جدول الموازنة السنوية
cursor.execute("""
CREATE TABLE الموازنة_السنوية (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    السنة_المالية INTEGER NOT NULL,
    نوع_البند TEXT NOT NULL,
    اسم_البند TEXT NOT NULL,
    المبلغ_المتوقع REAL NOT NULL DEFAULT 0,
    المبلغ_الفعلي REAL DEFAULT 0,
    UNIQUE(السنة_المالية, نوع_البند, اسم_البند)
)
""")

# جدول إعدادات النظام
cursor.execute("""
CREATE TABLE إعدادات_النظام (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    اسم_الإعداد TEXT UNIQUE NOT NULL,
    قيمة_الإعداد TEXT NOT NULL,
    وصف_الإعداد TEXT
)
""")

# إضافة السنوات
cursor.execute("INSERT INTO السنوات_المالية (السنة_المالية, تاريخ_البداية, تاريخ_النهاية) VALUES (2024, '2024-09-01', '2025-08-31')")
cursor.execute("INSERT INTO السنوات_المالية (السنة_المالية, تاريخ_البداية, تاريخ_النهاية) VALUES (2025, '2025-09-01', '2026-08-31')")

# إضافة بيانات تجريبية
cursor.execute("INSERT INTO الموازنة_السنوية (السنة_المالية, نوع_البند, اسم_البند, المبلغ_المتوقع) VALUES (2024, 'إيرادات', 'registration_fees', 50000)")
cursor.execute("INSERT INTO الموازنة_السنوية (السنة_المالية, نوع_البند, اسم_البند, المبلغ_المتوقع) VALUES (2024, 'إيرادات', 'monthly_duties', 300000)")
cursor.execute("INSERT INTO الموازنة_السنوية (السنة_المالية, نوع_البند, اسم_البند, المبلغ_المتوقع) VALUES (2024, 'مصاريف', 'رواتب', 200000)")
cursor.execute("INSERT INTO الموازنة_السنوية (السنة_المالية, نوع_البند, اسم_البند, المبلغ_المتوقع) VALUES (2025, 'إيرادات', 'registration_fees', 55000)")
cursor.execute("INSERT INTO الموازنة_السنوية (السنة_المالية, نوع_البند, اسم_البند, المبلغ_المتوقع) VALUES (2025, 'إيرادات', 'monthly_duties', 320000)")
cursor.execute("INSERT INTO الموازنة_السنوية (السنة_المالية, نوع_البند, اسم_البند, المبلغ_المتوقع) VALUES (2025, 'مصاريف', 'رواتب', 220000)")

# إضافة إعداد السنة المختارة
cursor.execute("INSERT INTO إعدادات_النظام (اسم_الإعداد, قيمة_الإعداد, وصف_الإعداد) VALUES ('السنة_المالية_المختارة', '2025', 'السنة المختارة')")

conn.commit()
conn.close()

print("Done!")
