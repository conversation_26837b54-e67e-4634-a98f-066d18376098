import sqlite3
from PyQt5.QtGui import QFont, QPixmap, QColor, QIcon
from PyQt5.QtWidgets import (QWidget, QLabel, QLineEdit, QVBoxLayout, QHBoxLayout,
                             QPushButton, QMessageBox, QGroupBox, QGridLayout,
                             QComboBox, QFileDialog, QFrame, QGraphicsDropShadowEffect, QTableWidget,
                             QApplication, QDialog, QTextBrowser, QStyle, QDialogButtonBox)
from PyQt5.QtCore import Qt, pyqtSignal, QSize
import os


class CustomDialogs:
    """فئة تحتوي على دوال إنشاء رسائل التأكيد المخصصة"""

    @staticmethod
    def show_custom_success_message(parent, message, title="نجاح"):
        """عرض رسالة نجاح مخصصة"""
        success_dialog = QDialog(parent)
        success_dialog.setWindowTitle(title)
        success_dialog.setFixedSize(450, 250)
        success_dialog.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            success_dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تنسيق النافذة
        success_dialog.setStyleSheet("""
            QDialog {
                background-color: #f0fff0;
                border: 2px solid #2ecc71;
                border-radius: 10px;
            }
            QLabel {
                color: #333333;
                font-weight: bold;
            }
            QLabel#icon_label {
                padding: 10px;
            }
            QLabel#message_label {
                background-color: #eafaf1;
                border: 1px solid #2ecc71;
                border-radius: 5px;
                padding: 15px;
                font-size: 14pt;
            }
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                min-height: 35px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #27ae60;
                border: 2px solid #2ecc71;
            }
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(success_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة أيقونة وعنوان
        header_layout = QHBoxLayout()

        # محاولة إضافة أيقونة النجاح
        icon_label = QLabel()
        icon_label.setObjectName("icon_label")
        icon_label.setAlignment(Qt.AlignCenter)
        try:
            # يمكن استخدام أيقونة النجاح المخصصة إذا كانت متاحة
            success_icon = QPixmap("success.png")  # استبدل بالمسار الصحيح إذا كان متاحاً
            if success_icon.isNull():
                success_icon = QStyle.standardPixmap(QStyle.SP_MessageBoxInformation, QStyle.StyleOptionButton(), parent)
            success_icon = success_icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            icon_label.setPixmap(success_icon)
        except Exception:
            # استخدام أيقونة قياسية في حالة الفشل
            icon_label.setText("✓")
            icon_label.setFont(QFont("Arial", 24))
            icon_label.setStyleSheet("color: #2ecc71;")

        header_layout.addWidget(icon_label)

        # إضافة عنوان النافذة
        title_label = QLabel(title)
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #27ae60;")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label, 1)

        layout.addLayout(header_layout)

        # إضافة رسالة النجاح
        message_label = QLabel(message)
        message_label.setObjectName("message_label")
        message_label.setFont(QFont("Calibri", 13))
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        layout.addWidget(message_label)

        # إضافة زر الموافقة
        button_layout = QHBoxLayout()
        ok_button = QPushButton("موافق")
        ok_button.setFont(QFont("Calibri", 12, QFont.Bold))
        ok_button.setCursor(Qt.PointingHandCursor)
        ok_button.clicked.connect(success_dialog.accept)
        button_layout.addWidget(ok_button)

        layout.addLayout(button_layout)

        # عرض النافذة
        success_dialog.exec_()
        return True

    @staticmethod
    def show_custom_warning_message(parent, message, title="تنبيه"):
        """عرض رسالة تحذير مخصصة"""
        warning_dialog = QDialog(parent)
        warning_dialog.setWindowTitle(title)
        warning_dialog.setFixedSize(450, 250)
        warning_dialog.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            warning_dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تنسيق النافذة
        warning_dialog.setStyleSheet("""
            QDialog {
                background-color: #fffbf0;
                border: 2px solid #f39c12;
                border-radius: 10px;
            }
            QLabel {
                color: #333333;
                font-weight: bold;
            }
            QLabel#icon_label {
                padding: 10px;
            }
            QLabel#message_label {
                background-color: #fef9e7;
                border: 1px solid #f39c12;
                border-radius: 5px;
                padding: 15px;
                font-size: 14pt;
            }
            QPushButton {
                background-color: #f39c12;
                color: white;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                min-height: 35px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #e67e22;
                border: 2px solid #f39c12;
            }
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(warning_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة أيقونة وعنوان
        header_layout = QHBoxLayout()

        # محاولة إضافة أيقونة التحذير
        icon_label = QLabel()
        icon_label.setObjectName("icon_label")
        icon_label.setAlignment(Qt.AlignCenter)
        try:
            warning_icon = QPixmap("warning.png")  # استبدل بالمسار الصحيح إذا كان متاحاً
            if warning_icon.isNull():
                warning_icon = QStyle.standardPixmap(QStyle.SP_MessageBoxWarning, QStyle.StyleOptionButton(), parent)
            warning_icon = warning_icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            icon_label.setPixmap(warning_icon)
        except Exception:
            # استخدام نص بديل في حالة الفشل
            icon_label.setText("⚠")
            icon_label.setFont(QFont("Arial", 24))
            icon_label.setStyleSheet("color: #f39c12;")

        header_layout.addWidget(icon_label)

        # إضافة عنوان النافذة
        title_label = QLabel(title)
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #e67e22;")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label, 1)

        layout.addLayout(header_layout)

        # إضافة رسالة التحذير
        message_label = QLabel(message)
        message_label.setObjectName("message_label")
        message_label.setFont(QFont("Calibri", 13))
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        layout.addWidget(message_label)

        # إضافة زر الموافقة
        button_layout = QHBoxLayout()
        ok_button = QPushButton("موافق")
        ok_button.setFont(QFont("Calibri", 12, QFont.Bold))
        ok_button.setCursor(Qt.PointingHandCursor)
        ok_button.clicked.connect(warning_dialog.accept)
        button_layout.addWidget(ok_button)

        layout.addLayout(button_layout)

        # عرض النافذة
        warning_dialog.exec_()
        return True

    @staticmethod
    def show_custom_error_message(parent, message, title="خطأ"):
        """عرض رسالة خطأ مخصصة"""
        error_dialog = QDialog(parent)
        error_dialog.setWindowTitle(title)
        error_dialog.setFixedSize(450, 250)
        error_dialog.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            error_dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تنسيق النافذة
        error_dialog.setStyleSheet("""
            QDialog {
                background-color: #fff0f0;
                border: 2px solid #e74c3c;
                border-radius: 10px;
            }
            QLabel {
                color: #333333;
                font-weight: bold;
            }
            QLabel#icon_label {
                padding: 10px;
            }
            QLabel#message_label {
                background-color: #fdedec;
                border: 1px solid #e74c3c;
                border-radius: 5px;
                padding: 15px;
                font-size: 14pt;
            }
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                min-height: 35px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #c0392b;
                border: 2px solid #e74c3c;
            }
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(error_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة أيقونة وعنوان
        header_layout = QHBoxLayout()

        # محاولة إضافة أيقونة الخطأ
        icon_label = QLabel()
        icon_label.setObjectName("icon_label")
        icon_label.setAlignment(Qt.AlignCenter)
        try:
            error_icon = QPixmap("error.png")  # استبدل بالمسار الصحيح إذا كان متاحاً
            if error_icon.isNull():
                error_icon = QStyle.standardPixmap(QStyle.SP_MessageBoxCritical, QStyle.StyleOptionButton(), parent)
            error_icon = error_icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            icon_label.setPixmap(error_icon)
        except Exception:
            # استخدام نص بديل في حالة الفشل
            icon_label.setText("✖")
            icon_label.setFont(QFont("Arial", 24))
            icon_label.setStyleSheet("color: #e74c3c;")

        header_layout.addWidget(icon_label)

        # إضافة عنوان النافذة
        title_label = QLabel(title)
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #c0392b;")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label, 1)

        layout.addLayout(header_layout)

        # إضافة رسالة الخطأ
        message_label = QLabel(message)
        message_label.setObjectName("message_label")
        message_label.setFont(QFont("Calibri", 13))
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        layout.addWidget(message_label)

        # إضافة زر الموافقة
        button_layout = QHBoxLayout()
        ok_button = QPushButton("موافق")
        ok_button.setFont(QFont("Calibri", 12, QFont.Bold))
        ok_button.setCursor(Qt.PointingHandCursor)
        ok_button.clicked.connect(error_dialog.accept)
        button_layout.addWidget(ok_button)

        layout.addLayout(button_layout)

        # عرض النافذة
        error_dialog.exec_()
        return True

class SubWindow(QWidget):
    # إضافة إشارة للتحديث
    data_updated_signal = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("بيانات المؤسسة")
        # لا نستخدم setFixedSize عندما تكون النافذة مدمجة
        if parent is None:  # إذا كانت نافذة مستقلة
            self.setFixedSize(800, 550)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setStyleSheet("background-color: #f5f5f5;")  # لون خلفية خفيف لتحسين المظهر
        self.setFont(QFont("Calibri", 13, QFont.Bold))  # تم تغيير الخط من Amiri إلى Calibri

        # تحديد مسار قاعدة البيانات
        self.db_path = "data.db"
        self.setup_database()

        # إضافة متغير للتحقق من حالة التحديث
        self.is_updating = False

        # ✅ إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(15)  # ترك مسافات متساوية بين العناصر


        # ✅ شعار المؤسسة (في الأعلى)
        logo_layout = QVBoxLayout()
        self.logo_label = QLabel()
        self.logo_label.setFixedSize(400, 150)  # تقليل حجم الشعار ليناسب النافذة الأصغر
        self.logo_label.setStyleSheet("border: 1px solid black; background-color: #ffffff;")
        self.logo_label.setAlignment(Qt.AlignCenter)

        # إنشاء تخطيط أفقي للأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)  # مسافة بين الأزرار

        # زر تحميل الشعار
        self.upload_btn = QPushButton("📷 تحميل الشعار", self)
        self.upload_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 15px;
                font-family: 'Calibri';
                font-size: 13px;
                font-weight: bold;
                min-width: 130px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
        """)
        self.upload_btn.clicked.connect(self.upload_logo)

        # ✅ زر حفظ البيانات (تم نقله إلى الأعلى بجانب زر تحميل الشعار)
        self.save_btn = QPushButton("💾 حفظ البيانات", self)
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 15px;
                font-family: 'Calibri';
                font-size: 13px;
                font-weight: bold;
                min-width: 130px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
        """)
        self.save_btn.clicked.connect(self.save_school_info)

        # زر تحديث البيانات (جديد)
        self.refresh_btn = QPushButton("🔄 تحديث البيانات", self)
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 15px;
                font-family: 'Calibri';
                font-size: 13px;
                font-weight: bold;
                min-width: 130px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
            QPushButton:pressed {
                background-color: #117a8b;
            }
        """)
        self.refresh_btn.clicked.connect(self.update_all_windows)

        # زر عرض بيانات المؤسسة
        self.info_btn = QPushButton("ℹ️ معلومات المؤسسة", self)
        self.info_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 15px;
                font-family: 'Calibri';
                font-size: 13px;
                font-weight: bold;
                min-width: 130px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
            QPushButton:pressed {
                background-color: #4e555b;
            }
        """)
        self.info_btn.clicked.connect(self.show_school_info)

        # إضافة الأزرار إلى التخطيط الأفقي
        buttons_layout.addWidget(self.upload_btn)
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.refresh_btn)  # إضافة زر التحديث
        buttons_layout.addWidget(self.info_btn)

        # إضافة العناصر إلى تخطيط الشعار
        logo_layout.addWidget(self.logo_label, alignment=Qt.AlignCenter)
        logo_layout.addLayout(buttons_layout)  # إضافة تخطيط الأزرار

        # إنشاء إطار رئيسي مع تأثير الظل
        main_frame = QFrame(self)
        main_frame.setFrameShape(QFrame.Box)
        main_frame.setFrameShadow(QFrame.Raised)
        main_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 10px;
                border: 1px solid #bdc3c7;
            }
        """)

        # إضافة تأثير الظل
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setXOffset(5)
        shadow.setYOffset(5)
        shadow.setColor(QColor(0, 0, 0, 50))
        main_frame.setGraphicsEffect(shadow)

        # نقل التخطيط الرئيسي إلى الإطار
        frame_layout = QVBoxLayout(main_frame)
        frame_layout.setContentsMargins(15, 15, 15, 15)
        frame_layout.setSpacing(15)

        # نقل جميع العناصر من main_layout إلى frame_layout
        frame_layout.addLayout(logo_layout)

        # ✅ إطار بيانات المؤسسة
        self.group_box = QGroupBox("بيانات المؤسسة")
        self.group_box.setFont(QFont('Calibri', 12, QFont.Bold))  # تم تغيير الخط من Amiri إلى Calibri
        self.group_box.setStyleSheet("""
            QGroupBox {
                border: 2px solid #1976d2;
                border-radius: 10px;
                padding: 10px;
                font-size: 14px;
                background-color: white;
            }
        """)

        grid_layout = QGridLayout()
        grid_layout.setSpacing(0)  # إزالة المسافة بين الحقول
        grid_layout.setVerticalSpacing(0)  # إزالة المسافة العمودية تمامًا
        grid_layout.setHorizontalSpacing(6)  # مسافة صغيرة أفقيًا للقراءة
        grid_layout.setContentsMargins(5, 5, 5, 5)  # تقليل الهوامش

        # ✅ إنشاء الحقول والإدخالات
        # إنشاء قائمة السنوات الدراسية من 2024/2025 إلى 20 سنة قادمة
        academic_years = ["اختر السنة"]
        for i in range(20):
            year_start = 2024 + i
            year_end = year_start + 1
            academic_years.append(f"{year_start}/{year_end}")

        fields = [
            ("رقم الهاتف", "text", []),  # تغيير من "الأكاديمية" إلى "رقم الهاتف"
            ("المؤسسة", "text", []),
            ("السنة الدراسية", "combo", academic_years),  # تحديث السنوات الدراسية
            ("المدينة", "text", []),  # تغيير من "البلدة" إلى "المدينة"
            ("المسؤول", "text", []),  # تغيير إلى مربع نص لإدخال اسم المسؤول
            ("رقم التسجيل", "text", [])
        ]

        self.fields = {}

        # تنظيم الحقول في صفين متلاصقين عموديًا
        for i, (label_text, field_type, options) in enumerate(fields):
            label = QLabel(label_text + ":")
            label.setFont(QFont('Calibri', 12))  # تم تغيير الخط من Amiri إلى Calibri
            label.setAlignment(Qt.AlignRight)
            label.setStyleSheet("margin-top: 0px; padding-top: 0px; margin-bottom: 0px; padding-bottom: 0px;")

            if field_type == "combo":
                widget = QComboBox()
                widget.addItems(options)
            else:
                widget = QLineEdit()

            widget.setFont(QFont('Calibri', 12))  # تم تغيير الخط من Amiri إلى Calibri
            widget.setStyleSheet("""
                border: 1px solid #ccc;
                padding: 3px 5px;
                background-color: #ffffff;
                margin-top: 0px;
                padding-top: 0px;
                margin-bottom: 0px;
                padding-bottom: 0px;
            """)

            # تحديد الصف والعمود - تقسيم إلى صفين
            row = i % 6  # 6 حقول في كل صف
            col_group = i // 6  # مجموعة الصفوف (0 أو 1)

            grid_layout.addWidget(label, row, col_group * 2)
            grid_layout.addWidget(widget, row, col_group * 2 + 1)
            self.fields[label_text] = widget

        self.group_box.setLayout(grid_layout)
        frame_layout.addWidget(self.group_box)

        # أزلنا زر الحفظ من هنا لأنه تم نقله إلى الأعلى

        # إضافة الإطار إلى التخطيط الرئيسي
        main_layout.addWidget(main_frame)

        # ✅ تحميل البيانات عند فتح النافذة
        self.load_academic_years()  # تحميل السنوات الدراسية من قاعدة البيانات
        self.load_school_info()
        self.load_logo()
        self.setup_ui()

        # إضافة دالة لتحديث البيانات عند تغيير قاعدة البيانات
        if parent and hasattr(parent, 'refresh_signal'):
            parent.refresh_signal.connect(self.refresh_data)

        # إعداد مربع نص لعرض البيانات الرقمية للمؤسسة
        self.school_code_text = QLineEdit(self)
        self.school_code_text.setReadOnly(True)  # غير قابل للتعديل
        self.school_code_text.setAlignment(Qt.AlignCenter)
        self.school_code_text.setFont(QFont('Calibri', 12, QFont.Bold))
        self.school_code_text.setStyleSheet("""
            QLineEdit {
                background-color: #f8f9fa;
                color: #2c3e50;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                padding: 5px;
                margin-top: 5px;
                margin-bottom: 10px;
            }
        """)

        # إضافة مربع النص تحت الشعار
        logo_layout.addWidget(self.school_code_text)

        # استدعاء دالة تحديث البيانات لملء مربع النص
        self.load_institution_data()

    def setup_database(self):
        """إنشاء جدول البيانات في قاعدة البيانات إذا لم يكن موجودًا"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('''CREATE TABLE IF NOT EXISTS بيانات_المؤسسة (
                رقم_الهاتف TEXT,
                المؤسسة TEXT,
                السنة_الدراسية TEXT,
                المدينة TEXT,
                المسؤول TEXT,
                رقم_التسجيل TEXT,
                ImagePath1 TEXT
            )''')

            # التحقق من وجود سجلات في الجدول
            cursor.execute("SELECT COUNT(*) FROM بيانات_المؤسسة")
            count = cursor.fetchone()[0]

            # إذا لم يكن هناك سجلات، قم بإنشاء سجل فارغ
            if count == 0:
                cursor.execute("""
                    INSERT INTO بيانات_المؤسسة (
                        رقم_الهاتف, المؤسسة, السنة_الدراسية,
                        المدينة, المسؤول, رقم_التسجيل, ImagePath1
                    ) VALUES (
                        '', '', '', '', '', '', ''
                    )
                """)
                print("تم إنشاء سجل فارغ في جدول بيانات_المؤسسة")

            conn.commit()
            conn.close()
        except Exception as e:
            print(f"خطأ في إعداد قاعدة البيانات: {e}")

    def save_school_info(self):
        """حفظ البيانات في قاعدة البيانات"""
        try:
            # جمع البيانات من الحقول
            data = {}
            for field, widget in self.fields.items():
                if isinstance(widget, QComboBox):
                    data[field] = widget.currentText()
                else:
                    data[field] = widget.text()

            # التحقق من جميع الحقول الإلزامية (عدا رقم التسجيل)
            required_fields = ["رقم الهاتف", "المؤسسة", "السنة الدراسية", "المدينة", "المسؤول"]
            empty_fields = []

            for field in required_fields:
                if field in data:
                    value = data[field].strip() if data[field] else ""
                    if not value or value == "اختر السنة":
                        empty_fields.append(field)

            if empty_fields:
                # إنشاء مربع حوار تنبيه للحقول الفارغة
                msg_box = QMessageBox(self)
                msg_box.setWindowTitle("تنبيه: حقول مطلوبة فارغة")

                # إضافة أيقونة البرنامج إلى نافذة الرسالة
                icon_path = "01.ico"
                if os.path.exists(icon_path):
                    msg_box.setWindowIcon(QIcon(icon_path))

                # تعيين النص الرئيسي بخط Calibri 13 أزرق غامق
                fields_text = "، ".join(empty_fields)
                msg_box.setText(f"<p style='font-family: Calibri; font-size: 13pt; color: #0D47A1; font-weight: bold;'>الحقول التالية مطلوبة: {fields_text}</p>")

                # إضافة نص توضيحي
                msg_box.setInformativeText("<p style='font-family: Calibri; font-size: 11pt;'>يجب ملء جميع الحقول المطلوبة قبل حفظ البيانات.</p><p style='font-family: Calibri; font-size: 11pt;'>رقم التسجيل اختياري ويمكن تركه فارغاً.</p>")

                # تعيين أيقونة التحذير
                msg_box.setIcon(QMessageBox.Warning)

                # إنشاء زر موافق فقط
                ok_button = msg_box.addButton("موافق", QMessageBox.AcceptRole)

                # تخصيص نمط الزر
                ok_button.setStyleSheet("""
                    QPushButton {
                        font-family: Calibri;
                        font-size: 13pt;
                        font-weight: bold;
                        color: white;
                        background-color: #0D47A1;
                        border: none;
                        border-radius: 5px;
                        padding: 5px 15px;
                        min-width: 140px;
                    }
                    QPushButton:hover {
                        background-color: #1565C0;
                    }
                    QPushButton:pressed {
                        background-color: #0D47A1;
                    }
                """)

                # تعيين الزر الافتراضي
                msg_box.setDefaultButton(ok_button)

                # تعيين نمط مربع الحوار
                msg_box.setStyleSheet("""
                    QMessageBox {
                        background-color: white;
                    }
                    QLabel {
                        font-family: Calibri;
                        min-width: 300px;
                    }
                """)

                # عرض مربع الحوار وانتظار الرد
                msg_box.exec_()
                return



            # التحقق من وجود شعار المؤسسة - إلزامي
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة WHERE rowid=1")
            result = cursor.fetchone()
            conn.close()

            has_logo = result and result[0] and os.path.exists(result[0])

            if not has_logo:
                # إنشاء مربع حوار تنبيه أنيق
                msg_box = QMessageBox(self)
                msg_box.setWindowTitle("تنبيه: شعار المؤسسة غير موجود")

                # إضافة أيقونة البرنامج إلى نافذة الرسالة
                icon_path = "01.ico"
                if os.path.exists(icon_path):
                    msg_box.setWindowIcon(QIcon(icon_path))

                # تعيين النص الرئيسي بخط Calibri 13 أزرق غامق
                msg_box.setText("<p style='font-family: Calibri; font-size: 13pt; color: #0D47A1; font-weight: bold;'>شعار المؤسسة غير موجود</p>")

                # إضافة نص توضيحي
                msg_box.setInformativeText("<p style='font-family: Calibri; font-size: 11pt;'>يجب تحميل شعار المؤسسة قبل حفظ البيانات.</p><p style='font-family: Calibri; font-size: 11pt;'>هذا الحقل إلزامي ولا يمكن تركه فارغاً.</p>")

                # تعيين أيقونة التحذير
                msg_box.setIcon(QMessageBox.Warning)

                # إنشاء زر موافق فقط
                ok_button = msg_box.addButton("موافق", QMessageBox.AcceptRole)

                # تخصيص نمط الزر
                ok_button.setStyleSheet("""
                    QPushButton {
                        font-family: Calibri;
                        font-size: 13pt;
                        font-weight: bold;
                        color: white;
                        background-color: #0D47A1;
                        border: none;
                        border-radius: 5px;
                        padding: 5px 15px;
                        min-width: 140px;
                    }
                    QPushButton:hover {
                        background-color: #1565C0;
                    }
                    QPushButton:pressed {
                        background-color: #0D47A1;
                    }
                """)

                # تعيين الزر الافتراضي
                msg_box.setDefaultButton(ok_button)

                # تعيين نمط مربع الحوار
                msg_box.setStyleSheet("""
                    QMessageBox {
                        background-color: white;
                    }
                    QLabel {
                        font-family: Calibri;
                        min-width: 300px;
                    }
                """)

                # عرض مربع الحوار وانتظار الرد
                msg_box.exec_()

                # تركيز على زر تحميل الشعار
                self.upload_btn.setFocus()
                return

            # تحويل أسماء الحقول إلى أسماء الأعمدة في قاعدة البيانات
            column_mapping = {
                "رقم الهاتف": "رقم_الهاتف",
                "المؤسسة": "المؤسسة",
                "السنة الدراسية": "السنة_الدراسية",
                "المدينة": "المدينة",
                "المسؤول": "المسؤول",
                "رقم التسجيل": "رقم_التسجيل"
            }

            # إعداد بيانات الاستعلام
            update_columns = []
            update_values = []

            # بناء قائمة الأعمدة والقيم للتحديث
            for field, value in data.items():
                if field in column_mapping:
                    update_columns.append(f"{column_mapping[field]} = ?")
                    update_values.append(value)

            # التحقق من وجود بيانات للتحديث
            if not update_columns:
                self.show_message_box("تنبيه", "لا توجد بيانات للتحديث!", QMessageBox.Warning)
                return

            # التحقق من وجود سجل على الأقل قبل التحديث
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT COUNT(*) FROM بيانات_المؤسسة")
            count = cursor.fetchone()[0]

            if count == 0:
                # إذا كان الجدول فارغاً، نقوم بإنشاء سجل جديد أولاً
                cursor.execute("INSERT INTO بيانات_المؤسسة DEFAULT VALUES")
                conn.commit()

            # بناء استعلام التحديث
            update_query = f"UPDATE بيانات_المؤسسة SET {', '.join(update_columns)} WHERE rowid=1"

            # تنفيذ الاستعلام
            cursor.execute(update_query, update_values)
            conn.commit()
            conn.close()

            # استخراج رمز المؤسسة من اسم المؤسسة ورقم الهاتف
            self.generate_institution_code()

            # التحقق من وجود رقم التسجيل
            registration_number = data.get("رقم التسجيل", "").strip()

            if registration_number:
                # إذا كان رقم التسجيل موجود، التحقق من صحته
                if self.verify_activation_code():
                    self.show_message_box("تم بنجاح", "✅ تم حفظ بيانات المؤسسة وتفعيل البرنامج بنجاح", QMessageBox.Information)
                else:
                    self.show_message_box("خطأ في كود التفعيل",
                                        "تم حفظ البيانات ولكن كود التفعيل غير صحيح.\n"
                                        "الرجاء التأكد من رقم التسجيل المدخل.",
                                        QMessageBox.Warning)
            else:
                # إذا لم يكن رقم التسجيل موجود، عرض رمز المؤسسة وطلب رقم التسجيل
                self.show_activation_dialog()

            # إرسال إشارة لتحديث النوافذ الأخرى
            self.data_updated_signal.emit()
            print("تم إرسال إشارة تحديث البيانات للنوافذ الأخرى")

        except Exception as e:
            self.show_message_box("خطأ", f"حدث خطأ أثناء حفظ البيانات:\n{str(e)}", QMessageBox.Critical)
            print(f"خطأ عند حفظ البيانات: {e}")

    def generate_institution_code(self):
        """إنشاء رمز المؤسسة من اسم المؤسسة ورقم الهاتف فقط"""
        try:
            # الحصول على اسم المؤسسة ورقم الهاتف من الحقول
            institution_name = self.fields["المؤسسة"].text().strip()
            phone_number = self.fields["رقم الهاتف"].text().strip()

            if not institution_name or not phone_number:
                self.school_code_text.setText("رمز المؤسسة: غير متوفر")
                return

            # توليد رقم فريد 10 أرقام بناءً على اسم المؤسسة ورقم الهاتف
            combined_text = f"{institution_name}-{phone_number}"

            # حساب قيمة هاش ثابتة باستخدام خوارزمية بسيطة
            hash_value = 0
            for char in combined_text:
                hash_value = (hash_value * 31 + ord(char)) & 0xFFFFFFFF

            # تأكد من أن الرقم يكون دائمًا 10 أرقام
            numeric_code = hash_value % 10000000000
            if numeric_code < 1000000000:  # إذا كان أقل من 10 أرقام
                numeric_code += 1000000000  # أضف 1 في أول خانة

            # تنسيق الرقم كنص
            numeric_code_str = f"{numeric_code:010d}"

            # عرض البيانات الرقمية في مربع النص
            self.school_code_text.setText(f"رمز المؤسسة: {numeric_code_str}")

            # عرض تلميح عند تحويم المؤشر ليعرض البيانات الأصلية
            self.school_code_text.setToolTip(
                f"اسم المؤسسة: {institution_name}\n"
                f"رقم الهاتف: {phone_number}"
            )

            print(f"تم إنشاء رمز المؤسسة: {numeric_code_str}")

        except Exception as e:
            print(f"خطأ في إنشاء رمز المؤسسة: {str(e)}")
            self.school_code_text.setText("رمز المؤسسة: غير متوفر")

    def show_activation_dialog(self):
        """عرض نافذة طلب رقم التسجيل للتفعيل"""
        try:
            # الحصول على رمز المؤسسة
            school_code_text = self.school_code_text.text()
            school_code_parts = school_code_text.split(": ")
            institution_code = school_code_parts[1].strip() if len(school_code_parts) > 1 else "غير متوفر"

            # إنشاء نافذة حوار مخصصة
            dialog = QDialog(self)
            dialog.setWindowTitle("تفعيل البرنامج")
            dialog.setFixedSize(500, 300)
            dialog.setLayoutDirection(Qt.RightToLeft)

            # إضافة أيقونة البرنامج
            try:
                app_icon = QIcon("01.ico")
                dialog.setWindowIcon(app_icon)
            except:
                pass

            # تخطيط النافذة
            layout = QVBoxLayout(dialog)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(15)

            # عنوان النافذة
            title_label = QLabel("تم حفظ البيانات بنجاح!")
            title_label.setFont(QFont("Calibri", 16, QFont.Bold))
            title_label.setStyleSheet("color: #2ecc71; text-align: center;")
            title_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(title_label)

            # عرض رمز المؤسسة
            code_label = QLabel(f"رمز المؤسسة: {institution_code}")
            code_label.setFont(QFont("Calibri", 14, QFont.Bold))
            code_label.setStyleSheet("color: #3498db; background-color: #ecf0f1; padding: 10px; border-radius: 5px;")
            code_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(code_label)

            # رسالة توضيحية
            info_label = QLabel("لتفعيل البرنامج، يرجى إدخال رقم التسجيل:")
            info_label.setFont(QFont("Calibri", 12))
            info_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(info_label)

            # حقل إدخال رقم التسجيل
            registration_input = QLineEdit()
            registration_input.setFont(QFont("Calibri", 12))
            registration_input.setPlaceholderText("أدخل رقم التسجيل هنا...")
            registration_input.setStyleSheet("""
                QLineEdit {
                    padding: 8px;
                    border: 2px solid #bdc3c7;
                    border-radius: 5px;
                    font-size: 12pt;
                }
                QLineEdit:focus {
                    border-color: #3498db;
                }
            """)
            layout.addWidget(registration_input)

            # أزرار النافذة
            button_layout = QHBoxLayout()

            activate_btn = QPushButton("تفعيل")
            activate_btn.setFont(QFont("Calibri", 12, QFont.Bold))
            activate_btn.setStyleSheet("""
                QPushButton {
                    background-color: #2ecc71;
                    color: white;
                    border-radius: 5px;
                    padding: 8px 15px;
                    min-width: 100px;
                }
                QPushButton:hover {
                    background-color: #27ae60;
                }
            """)

            later_btn = QPushButton("لاحقاً")
            later_btn.setFont(QFont("Calibri", 12, QFont.Bold))
            later_btn.setStyleSheet("""
                QPushButton {
                    background-color: #95a5a6;
                    color: white;
                    border-radius: 5px;
                    padding: 8px 15px;
                    min-width: 100px;
                }
                QPushButton:hover {
                    background-color: #7f8c8d;
                }
            """)

            button_layout.addWidget(activate_btn)
            button_layout.addWidget(later_btn)
            layout.addLayout(button_layout)

            # ربط الأحداث
            def activate_program():
                registration_number = registration_input.text().strip()
                if registration_number:
                    # تحديث حقل رقم التسجيل
                    self.fields["رقم التسجيل"].setText(registration_number)

                    # التحقق من صحة رقم التسجيل
                    if self.verify_activation_code():
                        # حفظ رقم التسجيل في قاعدة البيانات
                        try:
                            conn = sqlite3.connect(self.db_path)
                            cursor = conn.cursor()
                            cursor.execute("UPDATE بيانات_المؤسسة SET رقم_التسجيل = ? WHERE rowid=1", (registration_number,))
                            conn.commit()
                            conn.close()

                            dialog.accept()
                            self.show_message_box("تم التفعيل", "✅ تم تفعيل البرنامج بنجاح!", QMessageBox.Information)
                        except Exception as e:
                            print(f"خطأ في حفظ رقم التسجيل: {e}")
                            dialog.accept()
                    else:
                        QMessageBox.warning(dialog, "خطأ", "رقم التسجيل غير صحيح. يرجى المحاولة مرة أخرى.")
                else:
                    QMessageBox.warning(dialog, "تنبيه", "يرجى إدخال رقم التسجيل.")

            def close_dialog():
                dialog.accept()

            activate_btn.clicked.connect(activate_program)
            later_btn.clicked.connect(close_dialog)

            # عرض النافذة
            dialog.exec_()

        except Exception as e:
            print(f"خطأ في عرض نافذة التفعيل: {str(e)}")

    def show_message_box(self, title, message, icon=QMessageBox.Information):
        """عرض رسالة موحدة بتنسيق محدد"""
        # استخدام الرسائل المميزة بدلاً من الرسائل القياسية
        if icon == QMessageBox.Information:
            # رسالة نجاح
            CustomDialogs.show_custom_success_message(self, message, title)
        elif icon == QMessageBox.Warning:
            # رسالة تحذير
            CustomDialogs.show_custom_warning_message(self, message, title)
        elif icon == QMessageBox.Critical:
            # رسالة خطأ
            CustomDialogs.show_custom_error_message(self, message, title)
        else:
            # استخدام الرسالة القياسية في حالة عدم وجود نوع مناسب
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle(title)
            msg_box.setText(message)
            msg_box.setIcon(icon)

            # تعيين خط Calibri 13 أسود غليظ للرسالة
            font = QFont("Calibri", 13, QFont.Bold)
            msg_box.setFont(font)

            msg_box.setStyleSheet("""
                QMessageBox {
                    background-color: #ffffff;
                }
                QLabel {
                    color: #000000;
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                }
                QPushButton {
                    background-color: #4B9CD3;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 6px 12px;
                    font-family: 'Calibri';
                    font-size: 13px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background-color: #3A7CA5;
                }
            """)

            # إضافة زر موافق فقط
            msg_box.addButton("موافق", QMessageBox.AcceptRole)

            # عرض رسالة النجاح البسيطة
            msg_box.exec_()

    def verify_activation_code(self):
        """التحقق من كود التفعيل باستخدام المعادلة المحددة"""
        try:
            # الحصول على رمز المؤسسة (بإزالة النص الوصفي)
            school_code_text = self.school_code_text.text()
            school_code_parts = school_code_text.split(": ")
            if len(school_code_parts) != 2:
                return False

            school_code = school_code_parts[1].strip()

            # التأكد من أن رمز المؤسسة عبارة عن رقم
            if not school_code.isdigit():
                return False

            # الحصول على رقم التسجيل المدخل
            registration_number = self.fields["رقم التسجيل"].text().strip()

            # التأكد من أن رقم التسجيل عبارة عن رقم
            if not registration_number.isdigit():
                return False

            # تطبيق المعادلة: (رمز المؤسسة * 98) + (أول 3 أرقام من رمز المؤسسة * 71)
            school_code_int = int(school_code)
            first_three_digits = int(school_code[:3])

            expected_registration = (school_code_int * 98) + (first_three_digits * 71)

            # التحقق مما إذا كان رقم التسجيل المدخل يطابق النتيجة المتوقعة
            return int(registration_number) == expected_registration

        except Exception as e:
            print(f"خطأ في التحقق من كود التفعيل: {str(e)}")
            return False

    def upload_logo(self):
        """تحميل الشعار وتحديثه في قاعدة البيانات"""
        file_path, _ = QFileDialog.getOpenFileName(self, "اختر صورة الشعار", "", "Images (*.png *.jpg *.jpeg)")
        if file_path:
            self.logo_label.setPixmap(QPixmap(file_path).scaled(300, 120, Qt.KeepAspectRatio, Qt.SmoothTransformation))
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute("UPDATE بيانات_المؤسسة SET ImagePath1=? WHERE rowid=1", (file_path,))
                conn.commit()
                conn.close()
                self.show_message_box("تم بنجاح", "✅ تم حفظ الشعار بنجاح", QMessageBox.Information)
            except Exception as e:
                self.show_message_box("خطأ", f"حدث خطأ أثناء حفظ الشعار:\n{str(e)}", QMessageBox.Critical)

    def load_logo(self):
        """تحميل الشعار"""
        try:
            # إعادة فتح الاتصال بقاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة WHERE rowid=1")
            result = cursor.fetchone()
            if (result and result[0] and os.path.exists(result[0])):
                self.logo_label.setPixmap(QPixmap(result[0]).scaled(300, 120, Qt.KeepAspectRatio, Qt.SmoothTransformation))
                print(f"تم تحميل الشعار من المسار: {result[0]}")
            else:
                # إزالة الشعار الحالي إذا لم يتم العثور على مسار صحيح
                self.logo_label.clear()
                self.logo_label.setText("لا يوجد شعار")
                print("لم يتم العثور على شعار صالح")
            conn.close()
        except Exception as e:
            print(f"خطأ في تحميل الشعار: {e}")
            # إزالة الشعار الحالي في حالة حدوث خطأ
            self.logo_label.clear()
            self.logo_label.setText("خطأ في تحميل الشعار")

    def load_school_info(self):
        """تحميل بيانات المؤسسة عند فتح النافذة"""
        try:
            # إعادة فتح الاتصال بقاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من وجود سجلات في الجدول
            cursor.execute("SELECT COUNT(*) FROM بيانات_المؤسسة")
            count = cursor.fetchone()[0]

            if count > 0:
                # استعلام لاسترجاع البيانات من أول سجل فقط
                cursor.execute("SELECT * FROM بيانات_المؤسسة LIMIT 1")
                row = cursor.fetchone()

                if row:
                    # تعيين النص للحقول المقابلة
                    # استخراج أسماء الأعمدة
                    cursor.execute("PRAGMA table_info(بيانات_المؤسسة)")
                    columns = [info[1] for info in cursor.fetchall()]

                    # تعيين القيم في الحقول المناسبة
                    column_mapping = {
                        "رقم_الهاتف": "رقم الهاتف",
                        "المؤسسة": "المؤسسة",
                        "السنة_الدراسية": "السنة الدراسية",
                        "المدينة": "المدينة",
                        "المسؤول": "المسؤول",
                        "رقم_التسجيل": "رقم التسجيل"
                    }

                    for i, col_name in enumerate(columns):
                        if i < len(row) and col_name in column_mapping:
                            field_name = column_mapping.get(col_name)
                            if field_name in self.fields:
                                widget = self.fields[field_name]
                                if row[i]:  # تأكد من أن القيمة ليست فارغة
                                    if isinstance(widget, QComboBox):
                                        index = widget.findText(row[i])
                                        if index >= 0:
                                            widget.setCurrentIndex(index)
                                    else:
                                        widget.setText(row[i])

            conn.close()
        except Exception as e:
            self.show_message_box("تنبيه", f"حدث خطأ أثناء تحميل البيانات:\n{str(e)}", QMessageBox.Warning)
            print(f"خطأ في تحميل بيانات المؤسسة: {e}")

    def load_academic_years(self):
        """تحميل السنوات الدراسية - السنوات محددة مسبقاً من 2024/2025 إلى 20 سنة قادمة"""
        print("السنوات الدراسية محددة مسبقاً من 2024/2025 إلى 20 سنة قادمة")

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # تحديث جميع الخطوط في النافذة
        main_font = QFont("Calibri", 13, QFont.Bold)  # تم تغيير الخط من Amiri إلى Calibri

        # تطبيق الخط على العناوين
        for label in self.findChildren(QLabel):
            label.setFont(main_font)

        # تطبيق الخط على حقول الإدخال
        for input_field in self.findChildren(QLineEdit):
            input_field.setFont(main_font)

        # تطبيق الخط على القوائم المنسدلة
        for combo in self.findChildren(QComboBox):
            combo.setFont(main_font)

        # تطبيق الخط على الأزرار
        for button in self.findChildren(QPushButton):
            button.setFont(main_font)

        # تطبيق الخط على الجداول إن وجدت
        for table in self.findChildren(QTableWidget):
            table.setFont(main_font)
            header = table.horizontalHeader()
            if header:
                header.setFont(main_font)

    def load_institution_data(self):
        """استخراج بيانات المؤسسة وتحويلها لبيانات رقمية"""
        try:
            # إعادة فتح الاتصال بقاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # استخراج البيانات من جدول بيانات_المؤسسة (اسم المؤسسة ورقم الهاتف فقط)
            cursor.execute("SELECT المؤسسة, رقم_الهاتف FROM بيانات_المؤسسة LIMIT 1")
            data = cursor.fetchone()
            conn.close()

            if data:
                school, phone = data

                # التحقق من وجود البيانات قبل معالجتها
                school = school or "غير متوفر"
                phone = phone or "غير متوفر"

                # توليد رقم فريد 10 أرقام بناءً على اسم المؤسسة ورقم الهاتف فقط
                combined_text = f"{school}-{phone}"

                # حساب قيمة هاش ثابتة باستخدام خوارزمية بسيطة
                hash_value = 0
                for char in combined_text:
                    hash_value = (hash_value * 31 + ord(char)) & 0xFFFFFFFF

                # تأكد من أن الرقم يكون دائمًا 10 أرقام
                numeric_code = hash_value % 10000000000
                if numeric_code < 1000000000:  # إذا كان أقل من 10 أرقام
                    numeric_code += 1000000000  # أضف 1 في أول خانة

                # تنسيق الرقم كنص
                numeric_code_str = f"{numeric_code:010d}"

                # عرض البيانات الرقمية في مربع النص
                self.school_code_text.setText(f"رمز المؤسسة: {numeric_code_str}")

                # عرض تلميح عند تحويم المؤشر ليعرض البيانات الأصلية
                self.school_code_text.setToolTip(
                    f"اسم المؤسسة: {school}\n"
                    f"رقم الهاتف: {phone}"
                )

                print(f"تم تحديث بيانات المؤسسة الرقمية: {numeric_code_str}")
            else:
                self.school_code_text.setText("رمز المؤسسة: غير متوفر")
                self.school_code_text.setToolTip("لم يتم العثور على بيانات المؤسسة")
                print("لم يتم العثور على بيانات المؤسسة")

        except Exception as e:
            print(f"خطأ في استخراج بيانات المؤسسة: {str(e)}")
            self.school_code_text.setText("رمز المؤسسة: غير متوفر")

    def update_all_windows(self):
        """تحديث جميع النوافذ في البرنامج"""
        try:
            # تحديث بيانات النافذة الحالية أولاً
            success = self.refresh_data()

            if not success:
                self.show_message_box("تنبيه", "لم يتم تحديث البيانات بنجاح. يرجى المحاولة مرة أخرى.", QMessageBox.Warning)
                return

            # إرسال إشارة لتحديث النوافذ الأخرى
            self.data_updated_signal.emit()

            # إذا كان هناك parent (النافذة الرئيسية)، قم بتحديث جميع النوافذ
            if self.parent():
                parent = self.parent()

                # التحقق من وجود قاموس windows في النافذة الرئيسية
                if hasattr(parent, 'windows'):
                    # عرض رسالة تقدم العملية
                    progress_msg = QMessageBox(self)
                    progress_msg.setWindowTitle("جاري التحديث")
                    progress_msg.setText("جاري تحديث جميع النوافذ في البرنامج...")
                    progress_msg.setStandardButtons(QMessageBox.NoButton)
                    progress_msg.setStyleSheet("""
                        QMessageBox {
                            background-color: #ffffff;
                        }
                        QLabel {
                            color: #000000;
                            font-family: 'Calibri';
                            font-size: 13pt;
                            font-weight: bold;
                        }
                    """)
                    progress_msg.show()

                    # تحديث واجهة المستخدم
                    QApplication.processEvents()

                    # تحديث النافذة الرئيسية أولاً إذا كانت تحتوي على دالة تحديث
                    try:
                        if hasattr(parent, 'refresh_data') and callable(getattr(parent, 'refresh_data')):
                            parent.refresh_data()
                            print("تم تحديث النافذة الرئيسية")
                        elif hasattr(parent, 'update_data') and callable(getattr(parent, 'update_data')):
                            parent.update_data()
                            print("تم تحديث النافذة الرئيسية باستخدام update_data")
                    except Exception as e:
                        print(f"خطأ في تحديث النافذة الرئيسية: {str(e)}")

                    # تحديث جميع النوافذ
                    updated_count = 0
                    for key, window in parent.windows.items():
                        if window:
                            try:
                                # التحقق من وجود دالة refresh_data
                                if hasattr(window, 'refresh_data') and callable(getattr(window, 'refresh_data')):
                                    window.refresh_data()
                                    updated_count += 1
                                    print(f"تم تحديث النافذة: {key}")
                                # التحقق من وجود دالة load_data كبديل
                                elif hasattr(window, 'load_data') and callable(getattr(window, 'load_data')):
                                    window.load_data()
                                    updated_count += 1
                                    print(f"تم تحديث النافذة: {key} باستخدام load_data")
                                # التحقق من وجود دالة update_data كبديل آخر
                                elif hasattr(window, 'update_data') and callable(getattr(window, 'update_data')):
                                    window.update_data()
                                    updated_count += 1
                                    print(f"تم تحديث النافذة: {key} باستخدام update_data")
                            except Exception as e:
                                print(f"خطأ في تحديث النافذة {key}: {str(e)}")

                    # إغلاق رسالة التقدم
                    progress_msg.close()

                    # عرض رسالة نجاح
                    self.show_message_box("تم بنجاح", f"✅ تم تحديث {updated_count} نافذة بنجاح", QMessageBox.Information)
                else:
                    # إذا لم يكن هناك قاموس windows، قم بتحديث النافذة الحالية فقط
                    self.show_message_box("تم بنجاح", "✅ تم تحديث البيانات بنجاح", QMessageBox.Information)
            else:
                # إذا لم يكن هناك parent، قم بتحديث النافذة الحالية فقط
                self.show_message_box("تم بنجاح", "✅ تم تحديث البيانات بنجاح", QMessageBox.Information)

        except Exception as e:
            self.show_message_box("خطأ", f"حدث خطأ أثناء تحديث النوافذ:\n{str(e)}", QMessageBox.Critical)
            print(f"خطأ في تحديث جميع النوافذ: {str(e)}")

    def refresh_data(self):
        """تحديث البيانات في النافذة"""
        # التحقق من حالة التحديث لمنع التحديثات المتكررة
        if self.is_updating:
            print("جاري تحديث البيانات بالفعل، يرجى الانتظار...")
            return False

        try:
            # تعيين حالة التحديث إلى True
            self.is_updating = True
            print("جاري تحديث بيانات المؤسسة...")

            # إعادة فتح الاتصال بقاعدة البيانات
            if hasattr(self, 'db_path'):
                conn = sqlite3.connect(self.db_path)
                conn.close()
                print("تم التحقق من الاتصال بقاعدة البيانات")

            # تحديث السنوات الدراسية
            self.load_academic_years()

            # تحديث بيانات المؤسسة
            self.load_school_info()

            # تحديث الشعار
            self.load_logo()

            # تحديث بيانات المؤسسة الرقمية
            self.load_institution_data()

            print("تم تحديث بيانات المؤسسة بنجاح")

            # إعادة رسم الواجهة
            self.repaint()

            # تعيين حالة التحديث إلى False
            self.is_updating = False
            return True
        except Exception as e:
            print(f"خطأ في تحديث بيانات المؤسسة: {str(e)}")
            # تعيين حالة التحديث إلى False حتى في حالة حدوث خطأ
            self.is_updating = False
            return False

    def show_school_info(self):
        """عرض بيانات المؤسسة في رسالة تعليمات"""
        try:
            # استخراج البيانات من قاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("""
                SELECT رقم_الهاتف, المدينة, المؤسسة, المسؤول, رقم_التسجيل
                FROM بيانات_المؤسسة
                WHERE rowid=1
            """)
            result = cursor.fetchone()
            conn.close()

            if not result:
                self.show_message_box("لا توجد بيانات",
                               "لم يتم العثور على بيانات المؤسسة. الرجاء إدخال البيانات أولاً.",
                               QMessageBox.Warning)
                return

            # استخراج البيانات
            phone, city, school, manager, registration_code = result

            # استخراج رمز المؤسسة من مربع النص
            school_code_text = self.school_code_text.text()
            school_code_parts = school_code_text.split(": ")
            institution_code = school_code_parts[1].strip() if len(school_code_parts) > 1 else "غير متوفر"

            # تجهيز نص للعرض
            info_html = f"""
            <div dir="rtl" style="text-align: center; font-family: 'Calibri'; padding: 10px;">
                <h2 style="color: #007bff; margin-bottom: 20px;">بيانات المؤسسة</h2>

                <table style="width: 90%; margin: 0 auto; border-collapse: collapse; border: 2px solid #dee2e6;">
                    <tr style="background-color: #f8f9fa;">
                        <th style="padding: 10px; border: 1px solid #dee2e6; text-align: center; color: #495057;">البيان</th>
                        <th style="padding: 10px; border: 1px solid #dee2e6; text-align: center; color: #495057;">القيمة</th>
                    </tr>
                    <tr>
                        <td style="padding: 10px; border: 1px solid #dee2e6; text-align: right; font-weight: bold;">رقم الهاتف</td>
                        <td style="padding: 10px; border: 1px solid #dee2e6; text-align: right;">{phone or 'غير متوفر'}</td>
                    </tr>
                    <tr style="background-color: #f8f9fa;">
                        <td style="padding: 10px; border: 1px solid #dee2e6; text-align: right; font-weight: bold;">المدينة</td>
                        <td style="padding: 10px; border: 1px solid #dee2e6; text-align: right;">{city or 'غير متوفر'}</td>
                    </tr>
                    <tr>
                        <td style="padding: 10px; border: 1px solid #dee2e6; text-align: right; font-weight: bold;">المؤسسة</td>
                        <td style="padding: 10px; border: 1px solid #dee2e6; text-align: right;">{school or 'غير متوفر'}</td>
                    </tr>
                    <tr style="background-color: #f8f9fa;">
                        <td style="padding: 10px; border: 1px solid #dee2e6; text-align: right; font-weight: bold;">المسؤول</td>
                        <td style="padding: 10px; border: 1px solid #dee2e6; text-align: right;">{manager or 'غير متوفر'}</td>
                    </tr>
                    <tr>
                        <td style="padding: 10px; border: 1px solid #dee2e6; text-align: right; font-weight: bold;">رمز المؤسسة</td>
                        <td style="padding: 10px; border: 1px solid #dee2e6; text-align: right;">{institution_code}</td>
                    </tr>
                    <tr style="background-color: #f8f9fa;">
                        <td style="padding: 10px; border: 1px solid #dee2e6; text-align: right; font-weight: bold;">رقم التسجيل</td>
                        <td style="padding: 10px; border: 1px solid #dee2e6; text-align: right;">{registration_code or 'غير متوفر'}</td>
                    </tr>
                </table>
                <p style="margin-top: 20px; font-style: italic; color: #6c757d;">لتغيير هذه المعلومات، استخدم نموذج بيانات المؤسسة</p>
            </div>
            """

            # عرض البيانات في رسالة جميلة
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("معلومات المؤسسة")
            msg_box.setText(info_html)
            msg_box.setStandardButtons(QMessageBox.Ok)

            # تطبيق النمط
            msg_box.setStyleSheet("""
                QMessageBox {
                    background-color: #ffffff;
                    min-width: 500px;
                }
                QLabel {
                    color: #000000;
                    font-family: 'Calibri';
                    font-size: 13pt;
                    min-width: 480px;
                }
                QPushButton {
                    background-color: #4B9CD3;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 6px 12px;
                    font-family: 'Calibri';
                    font-size: 13px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background-color: #3A7CA5;
                }
            """)

            # تغيير نص الزر إلى "موافق"
            ok_button = msg_box.button(QMessageBox.Ok)
            ok_button.setText("موافق")

            msg_box.exec_()

        except Exception as e:
            self.show_message_box("خطأ",
                           f"حدث خطأ أثناء استرجاع بيانات المؤسسة:\n{str(e)}",
                           QMessageBox.Critical)

# ✅ تشغيل النافذة
if __name__ == '__main__':
    from PyQt5.QtWidgets import QApplication
    import sys
    app = QApplication(sys.argv)
    window = SubWindow()
    window.show()
    sys.exit(app.exec_())