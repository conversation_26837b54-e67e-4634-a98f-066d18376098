# 📅 نافذة إدارة المهام والمواعيد

## 🎯 الوصف

نافذة `sub4_window.py` هي نافذة متخصصة لإدارة المهام والمواعيد وزيارة أولياء الأمور والاجتماعات في النظام التعليمي.

## 🎨 التصميم

- **تصميم جميل ومتسق** مع باقي نوافذ النظام
- **ألوان بنفسجية أنيقة** (#9b59b6, #8e44ad)
- **خطوط عربية واضحة** (Calibri)
- **واجهة مبسطة وسهلة الاستخدام**

## 📋 المميزات الرئيسية

### ✅ إدارة المهام
- إضافة مهام جديدة
- تعديل المهام الموجودة
- حذف المهام
- تصنيف حسب النوع والأولوية
- تتبع حالة المهام
- **ذاكرة الحقول** - يتذكر العنوان والوصف والملاحظات
- **طباعة المهمة المحددة** مع خيارات:
  - 🖨️ **طباعة حرارية** - مثل وصل الأداء تماماً
  - 📄 **طباعة PDF** - ملف PDF أنيق
  - **جدول معكوس** - القيمة ثم التسمية
  - **نفس حجم الورق** - 80mm للطابعة الحرارية

### 📅 إدارة المواعيد
- جدولة المواعيد
- تحديد التوقيت والمكان
- إدارة قائمة الحضور
- تذكيرات المواعيد

### 🖥️ التكامل مع النظام
- **تبويب مدمج** في النافذة الرئيسية `main_window.py`
- **نافذة مدمجة** تفتح داخل النظام الرئيسي
- **مدرج في التحزيم** `ultimate_pdf_build.spec`

### 👥 أنواع المهام المدعومة
- **مهمة عادية**
- **زيارة ولي أمر**
- **اجتماع**
- **موعد طبي**
- **مراجعة أكاديمية**
- **نشاط مدرسي**
- **اجتماع أولياء أمور**
- **تقييم طلاب**
- **أخرى**

### ⭐ مستويات الأولوية
- **عالية** (أحمر)
- **متوسطة** (برتقالي)
- **منخفضة** (أخضر)

### 📊 حالات المهام
- **مجدولة**
- **قيد التنفيذ**
- **مكتملة**
- **ملغاة**
- **مؤجلة**

## 🗂️ التبويبات

### 1️⃣ تبويب إضافة مهمة/موعد
- **العنوان**: عنوان المهمة أو الموعد
- **النوع**: تصنيف المهمة
- **الأولوية**: مستوى الأهمية
- **الحالة**: حالة التنفيذ
- **التواريخ**: تاريخ البداية والنهاية
- **الأوقات**: وقت البداية والنهاية
- **المكان**: مكان تنفيذ المهمة
- **الحضور**: قائمة المشاركين
- **الوصف**: تفاصيل المهمة
- **الملاحظات**: ملاحظات إضافية

### 2️⃣ تبويب إدارة المهام
- **جدول شامل** لجميع المهام
- **أزرار التحكم**: تحديث، تعديل، حذف
- **تلوين تلقائي** حسب الأولوية والحالة
- **ترتيب وفلترة** البيانات

### 3️⃣ تبويب التقارير
- **تقرير حسب النوع**
- **تقرير حسب الأولوية**
- **تقرير حسب الحالة**
- **تقرير شامل**

## 🗄️ قاعدة البيانات

### جدول `tasks_appointments`
```sql
CREATE TABLE tasks_appointments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    description TEXT,
    task_type TEXT NOT NULL,
    priority TEXT NOT NULL,
    status TEXT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE,
    start_time TIME,
    end_time TIME,
    location TEXT,
    attendees TEXT,
    notes TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 📊 التقارير

### 🎨 تصميم التقارير (مثل print111.py)
- **تصميم أنيق وجميل** مع ألوان متدرجة
- **الشعار** - شعار المؤسسة في أعلى التقرير
- **اسم المؤسسة** - يظهر تحت الشعار
- **عنوان التقرير** - في إطار ملون
- **الجداول الملونة** - خلفية زرقاء فاتحة للرؤوس
- **تلوين متناوب للصفوف** - أزرق فاتح وأبيض
- **توقيع المسؤول** - مساحة للتوقيع
- **تاريخ الطباعة** - في أسفل التقرير
- **خط Calibri 13 أسود غامق** للجداول
- **خط Calibri 14 أزرق غامق** للعناوين
- **دعم كامل للنصوص العربية**
- **حفظ على سطح المكتب** في مجلد `تقارير المهام والمواعيد/`
- **فتح تلقائي** للتقرير بعد الإنشاء

### 📋 أنواع التقارير

#### 1. تقرير المهام العام
- قائمة بجميع المهام
- العنوان، النوع، الأولوية، الحالة
- التواريخ والمكان

#### 2. تقرير حسب النوع
- إحصائيات المهام حسب النوع
- عدد المهام لكل نوع
- إجمالي عام

#### 3. تقرير حسب الأولوية
- توزيع المهام حسب الأولوية
- ترتيب من العالية للمنخفضة
- إحصائيات مفصلة

#### 4. تقرير حسب الحالة
- حالة تقدم المهام
- المكتملة، قيد التنفيذ، المؤجلة
- نسب الإنجاز

#### 5. التقرير الشامل
- تفاصيل كاملة لكل مهمة
- الوصف، الحضور، الملاحظات
- تقرير مفصل للمراجعة

## 🚀 كيفية الاستخدام

### 1. إضافة مهمة جديدة
1. انتقل لتبويب "إضافة مهمة/موعد"
2. املأ البيانات المطلوبة
3. اضغط "إضافة المهمة/الموعد"

### 2. تعديل مهمة موجودة
1. انتقل لتبويب "إدارة المهام"
2. حدد المهمة المطلوبة
3. اضغط "تعديل المحدد"
4. عدل البيانات واضغط "تحديث"

### 3. حذف مهمة
1. حدد المهمة في الجدول
2. اضغط "حذف المحدد"
3. أكد الحذف

### 4. إنشاء تقرير
1. انتقل لتبويب "التقارير"
2. اختر نوع التقرير المطلوب
3. سيتم إنشاء ملف PDF تلقائياً

## 🧪 الاختبار

```bash
python test_sub4_window.py
```

## 📁 الملفات المرتبطة

- `sub4_window.py` - النافذة الرئيسية
- `test_sub4_window.py` - ملف الاختبار
- `data.db` - قاعدة البيانات
- `reports/` - مجلد التقارير
- `fonts/` - الخطوط العربية

## 🔧 المتطلبات

- **PyQt5** - واجهة المستخدم
- **sqlite3** - قاعدة البيانات
- **FPDF** - إنشاء تقارير PDF
- **arabic_reshaper** - دعم النصوص العربية
- **bidi** - اتجاه النصوص

## 💡 نصائح الاستخدام

### 📅 للمواعيد
- حدد وقت البداية والنهاية بدقة
- أضف قائمة الحضور
- حدد المكان بوضوح

### 📋 للمهام
- استخدم أولوية مناسبة
- حدث الحالة باستمرار
- أضف ملاحظات مفيدة

### 📊 للتقارير
- أنشئ تقارير دورية
- راجع الإحصائيات
- تابع معدل الإنجاز

## 🎯 الفوائد

- **تنظيم أفضل** للمهام والمواعيد
- **تتبع دقيق** لحالة التقدم
- **تقارير شاملة** للمراجعة
- **واجهة سهلة** الاستخدام
- **دعم كامل** للنصوص العربية

---

**نافذة إدارة المهام والمواعيد - حل شامل لتنظيم العمل التعليمي** 📅✨
