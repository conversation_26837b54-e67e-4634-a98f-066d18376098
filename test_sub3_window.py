#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار ملف sub3_window.py
"""

import sys

def test_import():
    """اختبار استيراد الملف"""
    try:
        print("🧪 اختبار استيراد sub3_window...")
        
        # محاولة استيراد الملف
        import sub3_window
        print("✅ تم استيراد sub3_window بنجاح")
        
        # التحقق من وجود الكلاس الرئيسي
        if hasattr(sub3_window, 'PrinterSettingsWindow'):
            print("✅ كلاس PrinterSettingsWindow موجود")
        else:
            print("❌ كلاس PrinterSettingsWindow مفقود")
            return False
        
        # التحقق من وجود الدالة الرئيسية
        if hasattr(sub3_window, 'main'):
            print("✅ دالة main موجودة")
        else:
            print("❌ دالة main مفقودة")
            return False
        
        return True
        
    except SyntaxError as e:
        print(f"❌ خطأ في بناء الجملة: {e}")
        return False
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_class_creation():
    """اختبار إنشاء كائن من الكلاس"""
    try:
        print("\n🧪 اختبار إنشاء كائن من الكلاس...")
        
        # محاولة إنشاء تطبيق PyQt5
        try:
            from PyQt5.QtWidgets import QApplication
            app = QApplication(sys.argv)
            print("✅ تم إنشاء QApplication")
        except ImportError:
            print("⚠️ PyQt5 غير متوفر - تخطي اختبار الواجهة")
            return True
        
        # محاولة إنشاء كائن من الكلاس
        from sub3_window import PrinterSettingsWindow
        
        # إنشاء النافذة بدون عرضها
        window = PrinterSettingsWindow()
        print("✅ تم إنشاء PrinterSettingsWindow بنجاح")
        
        # التحقق من بعض الخصائص
        if hasattr(window, 'db_path'):
            print(f"✅ db_path موجود: {window.db_path}")
        else:
            print("❌ db_path مفقود")
            return False
        
        if hasattr(window, 'thermal_combo'):
            print("✅ thermal_combo موجود")
        else:
            print("❌ thermal_combo مفقود")
            return False
        
        if hasattr(window, 'default_combo'):
            print("✅ default_combo موجود")
        else:
            print("❌ default_combo مفقود")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الكائن: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔍 اختبار ملف sub3_window.py")
    print("=" * 50)
    
    tests = [
        ("استيراد الملف", test_import),
        ("إنشاء الكائن", test_class_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} - نجح")
        else:
            print(f"❌ {test_name} - فشل")
    
    print("\n" + "=" * 50)
    print("📊 ملخص الاختبارات:")
    print("=" * 50)
    print(f"✅ نجح: {passed}/{total}")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ ملف sub3_window.py يعمل بشكل صحيح")
    else:
        print("⚠️ بعض الاختبارات فشلت")
    
    print("\n🔧 المشكلة التي تم إصلاحها:")
    print("• إزالة السطر الخاطئ: from db_path import get_db_path")
    print("• السطر كان موجود داخل دالة print_to_printer بشكل خاطئ")
    print("• الآن الملف يستخدم self.db_path بشكل صحيح")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
    
    input("\nاضغط Enter للخروج...")
