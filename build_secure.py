#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تحزيم آمن لتجنب مشكلة اكتشاف الفيروسات الوهمية
يتضمن جميع الخطوات اللازمة لإنتاج ملف تنفيذي آمن
"""

import os
import sys
import subprocess
import shutil
import time
from pathlib import Path

def print_step(step_num, description):
    """طباعة خطوة بتنسيق جميل"""
    print(f"\n{'='*60}")
    print(f"الخطوة {step_num}: {description}")
    print(f"{'='*60}")

def run_command(command, description):
    """تشغيل أمر مع معالجة الأخطاء"""
    print(f"\n🔄 {description}...")
    print(f"الأمر: {command}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
        if result.returncode == 0:
            print(f"✅ {description} - تم بنجاح")
            if result.stdout:
                print(f"المخرجات: {result.stdout[:500]}...")
        else:
            print(f"❌ {description} - فشل")
            print(f"الخطأ: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ خطأ في تشغيل الأمر: {e}")
        return False
    
    return True

def clean_build_directories():
    """تنظيف مجلدات البناء السابقة"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✅ تم حذف مجلد {dir_name}")
            except Exception as e:
                print(f"⚠️ تعذر حذف مجلد {dir_name}: {e}")

def create_certificate():
    """إنشاء شهادة رقمية للتوقيع (اختياري)"""
    print("\n📜 إنشاء شهادة رقمية للتوقيع...")
    
    # هذا مثال - في الواقع تحتاج شهادة حقيقية من CA معتمد
    cert_info = """
    ملاحظة: للحصول على أفضل النتائج، يُنصح بالحصول على شهادة رقمية معتمدة من:
    - DigiCert
    - Comodo
    - GlobalSign
    - أو أي مزود شهادات معتمد آخر
    
    هذا سيقلل بشكل كبير من احتمالية اكتشاف البرنامج كفيروس وهمي.
    """
    print(cert_info)

def optimize_for_antivirus():
    """تحسينات إضافية لتجنب اكتشاف الفيروسات الوهمية"""
    print("\n🛡️ تطبيق تحسينات مكافحة الفيروسات...")
    
    # إنشاء ملف README للبرنامج
    readme_content = """
# نظام المعين في الحراسة العامة - إنتاج التقارير PDF

## معلومات البرنامج
- الاسم: نظام إدارة المدرسة
- الإصدار: 1.0.0
- المطور: فريق تطوير أنظمة التعليم
- الغرض: إدارة شؤون المدرسة وإنتاج التقارير

## الأمان
هذا البرنامج آمن تماماً ولا يحتوي على أي برمجيات ضارة.
تم تطويره باستخدام Python و PyQt5 لإدارة شؤون المدارس.

## في حالة اكتشاف برنامج مكافحة الفيروسات للبرنامج كتهديد
هذا ما يُسمى "إيجابية خاطئة" وهو أمر شائع مع البرامج المحزمة بـ PyInstaller.
يمكنك:
1. إضافة البرنامج لقائمة الاستثناءات في برنامج مكافحة الفيروسات
2. التحقق من البرنامج على موقع VirusTotal.com
3. التواصل مع المطور للحصول على نسخة موقعة رقمياً

## الدعم الفني
للدعم الفني أو الاستفسارات، يرجى التواصل مع فريق التطوير.
"""
    
    with open('README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ تم إنشاء ملف README.txt")

def main():
    """الدالة الرئيسية للتحزيم الآمن"""
    print("🚀 بدء عملية التحزيم الآمن لنظام المعين في الحراسة العامة")
    print("هذا السكريبت سيقوم بتحزيم البرنامج بطريقة تقلل من احتمالية اكتشافه كفيروس وهمي")
    
    # الخطوة 1: تنظيف المجلدات
    print_step(1, "تنظيف مجلدات البناء السابقة")
    clean_build_directories()
    
    # الخطوة 2: التحقق من الملفات المطلوبة
    print_step(2, "التحقق من الملفات المطلوبة")
    required_files = ['main_window.py', 'ultimate_pdf_build.spec', '01.ico', 'version_info.txt']
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} موجود")
        else:
            print(f"❌ {file} غير موجود - مطلوب للتحزيم")
            return False
    
    # الخطوة 3: تحسينات الأمان
    print_step(3, "تطبيق تحسينات الأمان")
    optimize_for_antivirus()
    create_certificate()
    
    # الخطوة 4: تحديث المكتبات
    print_step(4, "تحديث المكتبات المطلوبة")
    libraries = ['pyinstaller', 'PyQt5', 'fpdf', 'reportlab', 'arabic-reshaper', 'python-bidi']
    
    for lib in libraries:
        if not run_command(f"pip install --upgrade {lib}", f"تحديث {lib}"):
            print(f"⚠️ تعذر تحديث {lib} - المتابعة...")
    
    # الخطوة 5: التحزيم
    print_step(5, "بدء عملية التحزيم")
    
    # تعيين متغير البيئة للتشفير
    os.environ['PYINSTALLER_ENCRYPT'] = '1'
    
    build_command = "pyinstaller ultimate_pdf_build.spec --clean --noconfirm"
    
    if not run_command(build_command, "تحزيم البرنامج"):
        print("❌ فشل في التحزيم")
        return False
    
    # الخطوة 6: التحقق من النتيجة
    print_step(6, "التحقق من نتيجة التحزيم")
    
    dist_folder = Path("dist/SchoolGuardPDF")
    exe_file = dist_folder / "SchoolGuardPDF.exe"
    
    if exe_file.exists():
        file_size = exe_file.stat().st_size / (1024 * 1024)  # بالميجابايت
        print(f"✅ تم إنشاء الملف التنفيذي بنجاح")
        print(f"📁 المسار: {exe_file}")
        print(f"📊 الحجم: {file_size:.2f} ميجابايت")
        
        # نسخ ملف README إلى مجلد التوزيع
        shutil.copy2('README.txt', dist_folder)
        print("✅ تم نسخ ملف README إلى مجلد التوزيع")
        
    else:
        print("❌ لم يتم إنشاء الملف التنفيذي")
        return False
    
    # الخطوة 7: نصائح ما بعد التحزيم
    print_step(7, "نصائح مهمة لتجنب مشكلة اكتشاف الفيروسات الوهمية")
    
    tips = """
    🛡️ نصائح مهمة لتجنب اكتشاف الفيروسات الوهمية:
    
    1. 📝 التوقيع الرقمي:
       - احصل على شهادة رقمية معتمدة ووقع البرنامج
       - هذا أهم خطوة لتجنب الإيجابيات الخاطئة
    
    2. 🔍 الفحص المسبق:
       - ارفع البرنامج على VirusTotal.com للفحص
       - تأكد من عدم وجود إيجابيات خاطئة كثيرة
    
    3. 📤 التوزيع الآمن:
       - استخدم منصات توزيع معروفة
       - أضف معلومات واضحة عن البرنامج
    
    4. 🤝 التواصل مع مزودي مكافحة الفيروسات:
       - في حالة الإيجابيات الخاطئة، تواصل مع الشركات
       - قدم تقرير عن البرنامج كـ "false positive"
    
    5. 📋 الوثائق:
       - أضف ملفات README واضحة
       - اشرح وظيفة البرنامج بالتفصيل
    """
    
    print(tips)
    
    print(f"\n🎉 تم الانتهاء من التحزيم بنجاح!")
    print(f"📁 يمكنك العثور على البرنامج في: dist/SchoolGuardPDF/")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ العملية اكتملت بنجاح!")
    else:
        print("\n❌ حدث خطأ أثناء العملية!")
    
    input("\nاضغط Enter للخروج...")
