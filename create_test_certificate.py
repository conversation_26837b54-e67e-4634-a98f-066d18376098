#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء شهادة رقمية للاختبار (Self-Signed Certificate)
ملاحظة: هذه شهادة للاختبار فقط - للإنتاج احصل على شهادة معتمدة
"""

import subprocess
import os
import sys
from datetime import datetime, timedel<PERSON>

def check_openssl():
    """التحقق من وجود OpenSSL"""
    try:
        result = subprocess.run(['openssl', 'version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ OpenSSL متوفر: {result.stdout.strip()}")
            return True
        else:
            print("❌ OpenSSL غير متوفر")
            return False
    except FileNotFoundError:
        print("❌ OpenSSL غير مثبت على النظام")
        print("يمكنك تحميله من: https://slproweb.com/products/Win32OpenSSL.html")
        return False

def create_certificate():
    """إنشاء شهادة رقمية للاختبار"""
    
    if not check_openssl():
        return False
    
    print("\n🔐 إنشاء شهادة رقمية للاختبار...")
    
    # معلومات الشهادة
    cert_info = {
        'country': 'MA',  # المغرب
        'state': 'Casablanca',
        'city': 'Casablanca', 
        'organization': 'School Management System',
        'unit': 'Development Team',
        'common_name': 'SchoolGuardPDF',
        'email': '<EMAIL>'
    }
    
    # إنشاء المفتاح الخاص
    print("🔑 إنشاء المفتاح الخاص...")
    key_command = [
        'openssl', 'genrsa',
        '-out', 'test_certificate.key',
        '2048'
    ]
    
    try:
        result = subprocess.run(key_command, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ تم إنشاء المفتاح الخاص")
        else:
            print(f"❌ فشل في إنشاء المفتاح: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ خطأ في إنشاء المفتاح: {e}")
        return False
    
    # إنشاء طلب الشهادة
    print("📝 إنشاء طلب الشهادة...")
    
    subject = f"/C={cert_info['country']}/ST={cert_info['state']}/L={cert_info['city']}/O={cert_info['organization']}/OU={cert_info['unit']}/CN={cert_info['common_name']}/emailAddress={cert_info['email']}"
    
    csr_command = [
        'openssl', 'req',
        '-new',
        '-key', 'test_certificate.key',
        '-out', 'test_certificate.csr',
        '-subj', subject
    ]
    
    try:
        result = subprocess.run(csr_command, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ تم إنشاء طلب الشهادة")
        else:
            print(f"❌ فشل في إنشاء طلب الشهادة: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ خطأ في إنشاء طلب الشهادة: {e}")
        return False
    
    # إنشاء الشهادة الموقعة ذاتياً
    print("🏆 إنشاء الشهادة الموقعة ذاتياً...")
    
    cert_command = [
        'openssl', 'x509',
        '-req',
        '-days', '365',  # صالحة لسنة واحدة
        '-in', 'test_certificate.csr',
        '-signkey', 'test_certificate.key',
        '-out', 'test_certificate.crt'
    ]
    
    try:
        result = subprocess.run(cert_command, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ تم إنشاء الشهادة")
        else:
            print(f"❌ فشل في إنشاء الشهادة: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ خطأ في إنشاء الشهادة: {e}")
        return False
    
    # إنشاء ملف PKCS#12 للتوقيع
    print("📦 إنشاء ملف PKCS#12...")
    
    p12_command = [
        'openssl', 'pkcs12',
        '-export',
        '-out', 'test_certificate.p12',
        '-inkey', 'test_certificate.key',
        '-in', 'test_certificate.crt',
        '-passout', 'pass:test123'  # كلمة مرور للاختبار
    ]
    
    try:
        result = subprocess.run(p12_command, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ تم إنشاء ملف PKCS#12")
        else:
            print(f"❌ فشل في إنشاء ملف PKCS#12: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف PKCS#12: {e}")
        return False
    
    # تنظيف الملفات المؤقتة
    try:
        os.remove('test_certificate.csr')
        print("🧹 تم تنظيف الملفات المؤقتة")
    except:
        pass
    
    return True

def create_signing_script():
    """إنشاء سكريبت للتوقيع الرقمي"""
    
    script_content = '''@echo off
chcp 65001 >nul
title توقيع البرنامج رقمياً

echo.
echo ========================================
echo         توقيع البرنامج رقمياً
echo ========================================
echo.

if not exist "test_certificate.p12" (
    echo ❌ ملف الشهادة غير موجود
    echo يرجى تشغيل create_test_certificate.py أولاً
    pause
    exit /b 1
)

if not exist "dist\\SchoolGuardPDF\\SchoolGuardPDF.exe" (
    echo ❌ الملف التنفيذي غير موجود
    echo يرجى تحزيم البرنامج أولاً
    pause
    exit /b 1
)

echo 🔐 بدء عملية التوقيع الرقمي...
echo.

REM التحقق من وجود signtool
where signtool >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ signtool غير متوفر
    echo يرجى تثبيت Windows SDK
    pause
    exit /b 1
)

REM توقيع الملف
signtool sign /f test_certificate.p12 /p test123 /t http://timestamp.digicert.com "dist\\SchoolGuardPDF\\SchoolGuardPDF.exe"

if %errorlevel% equ 0 (
    echo ✅ تم توقيع البرنامج بنجاح
) else (
    echo ❌ فشل في توقيع البرنامج
)

echo.
pause
'''
    
    with open('sign_program.bat', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("✅ تم إنشاء سكريبت التوقيع: sign_program.bat")

def main():
    """الدالة الرئيسية"""
    print("🔐 إنشاء شهادة رقمية للاختبار")
    print("=" * 50)
    print()
    print("⚠️  تحذير مهم:")
    print("هذه شهادة للاختبار فقط (Self-Signed)")
    print("للإنتاج، احصل على شهادة معتمدة من CA معتمد")
    print()
    
    response = input("هل تريد المتابعة؟ (y/n): ")
    if response.lower() not in ['y', 'yes', 'نعم']:
        print("تم الإلغاء")
        return
    
    if create_certificate():
        print("\n🎉 تم إنشاء الشهادة بنجاح!")
        print("\nالملفات المنشأة:")
        print("- test_certificate.key (المفتاح الخاص)")
        print("- test_certificate.crt (الشهادة)")
        print("- test_certificate.p12 (ملف التوقيع)")
        print("\nكلمة مرور ملف P12: test123")
        
        create_signing_script()
        
        print("\n📋 الخطوات التالية:")
        print("1. قم بتحزيم البرنامج أولاً")
        print("2. شغل sign_program.bat لتوقيع البرنامج")
        print("3. اختبر البرنامج الموقع")
        
        print("\n⚠️  ملاحظة:")
        print("الشهادات الموقعة ذاتياً قد تظهر تحذيرات أمنية")
        print("للحصول على أفضل النتائج، احصل على شهادة معتمدة")
        
    else:
        print("\n❌ فشل في إنشاء الشهادة")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
