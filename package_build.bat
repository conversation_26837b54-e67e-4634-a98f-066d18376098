@echo off
chcp 65001 >nul
title تحزيم البرنامج - المعين في الحراسة العامة

echo ========================================
echo 🚀 تحزيم البرنامج
echo ========================================
echo.

echo 🔍 فحص ملف التحزيم...
if not exist "ultimate_pdf_build.spec" (
    echo ❌ ملف ultimate_pdf_build.spec غير موجود!
    pause
    exit /b 1
)
echo ✅ ملف التحزيم موجود

echo.
echo 🧹 تنظيف مجلدات البناء السابقة...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
echo ✅ تم التنظيف

echo.
echo 🔨 بدء عملية التحزيم...
echo ========================================
pyinstaller ultimate_pdf_build.spec

echo.
echo 📦 فحص النتائج...
if exist "dist\المعين_في_الحراسة_العامة_PDF_نهائي\المعين_في_الحراسة_العامة_PDF_نهائي.exe" (
    echo ✅ تم التحزيم بنجاح!
    echo 📁 المجلد: dist\المعين_في_الحراسة_العامة_PDF_نهائي\
    echo.
    echo 🎯 المميزات المضمنة:
    echo   • جميع نوافذ البرنامج
    echo   • مكتبات PDF والطباعة
    echo   • دعم النصوص العربية
    echo   • مكتبات pywin32
    echo   • قاعدة البيانات
    echo   • الخطوط العربية
    echo.
) else (
    echo ❌ فشل في التحزيم!
)

echo ========================================
pause
