
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pyimod02_importers - imported by C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed)
missing module named 'collections.abc' - imported by tracemalloc (top-level), traceback (top-level), typing (top-level), inspect (top-level), logging (top-level), importlib.resources.readers (top-level), selectors (top-level), sqlite3.dbapi2 (top-level), http.client (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named posix - imported by posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional), os (conditional, optional)
missing module named resource - imported by posix (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by getpass (optional)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional), netrc (delayed, conditional), getpass (delayed, optional)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional)
missing module named _posixsubprocess - imported by subprocess (conditional)
missing module named fcntl - imported by subprocess (optional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named pandas - imported by sub8_window (optional), C:\Users\<USER>\Desktop\taheri10\main_window.py (top-level)
missing module named numpy - imported by budget_planning_window (optional), cash_flow_window (optional), C:\Users\<USER>\Desktop\taheri10\main_window.py (top-level)
missing module named matplotlib - imported by budget_planning_window (optional), C:\Users\<USER>\Desktop\taheri10\main_window.py (top-level)
missing module named openpyxl - imported by sub8_window (delayed, conditional, optional), C:\Users\<USER>\Desktop\taheri10\main_window.py (top-level)
missing module named bidi - imported by print101 (optional), C:\Users\<USER>\Desktop\taheri10\main_window.py (top-level)
missing module named arabic_reshaper - imported by print101 (optional), print_section_monthly (optional), print_section_yearly (optional), print_registration_fees_monthly_style (optional), print111 (optional), daily_attendance_sheet_report (optional), attendance_sheet_report (optional), attendance_processing_window (delayed, optional), expense_management_window (delayed, optional), cash_flow_window (delayed, optional), print144 (optional), C:\Users\<USER>\Desktop\taheri10\main_window.py (top-level)
missing module named fpdf - imported by print101 (optional), print_section_monthly (optional), print_section_yearly (optional), print_registration_fees_monthly_style (optional), print111 (optional), daily_attendance_sheet_report (optional), attendance_sheet_report (optional), attendance_processing_window (delayed, optional), expense_management_window (delayed, optional), cash_flow_window (delayed, optional), print144 (optional), print_registration_fees (top-level), print_registration_fees_simple (top-level), C:\Users\<USER>\Desktop\taheri10\main_window.py (top-level)
missing module named 'bidi.algorithm' - imported by print101 (optional), print_section_monthly (optional), print_section_yearly (optional), print_registration_fees_monthly_style (optional), print111 (optional), daily_attendance_sheet_report (optional), attendance_sheet_report (optional), attendance_processing_window (delayed, optional), expense_management_window (delayed, optional), cash_flow_window (delayed, optional), print144 (optional)
missing module named db_path - imported by monthly_duties_window (optional), multi_section_duties_window (top-level)
missing module named 'matplotlib.figure' - imported by budget_planning_window (optional), cash_flow_window (optional)
missing module named 'matplotlib.backends' - imported by budget_planning_window (optional), cash_flow_window (optional)
missing module named 'matplotlib.pyplot' - imported by cash_flow_window (optional)
invalid module named sub3_window - imported by sub8_window (delayed, optional), C:\Users\<USER>\Desktop\taheri10\main_window.py (top-level)
missing module named 'fpdf.enums' - imported by attendance_processing_window (delayed, conditional)
missing module named readline - imported by code (delayed, conditional, optional), sqlite3.__main__ (delayed, conditional, optional)
