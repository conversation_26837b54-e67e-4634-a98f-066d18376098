#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت استعادة الملفات من النسخ الاحتياطية
"""

import os
import shutil
import glob

def restore_files():
    """استعادة جميع الملفات من النسخ الاحتياطية"""
    print("🔄 بدء استعادة الملفات من النسخ الاحتياطية...")
    
    # العثور على جميع ملفات النسخ الاحتياطية
    backup_files = glob.glob("*.backup")
    
    if not backup_files:
        print("❌ لم يتم العثور على ملفات نسخ احتياطية")
        return
    
    print(f"📁 تم العثور على {len(backup_files)} ملف نسخ احتياطي")
    
    restored_count = 0
    for backup_file in backup_files:
        try:
            # الحصول على اسم الملف الأصلي
            original_file = backup_file.replace('.backup', '')
            
            # نسخ الملف الاحتياطي إلى الملف الأصلي
            shutil.copy2(backup_file, original_file)
            
            print(f"✅ تم استعادة: {original_file}")
            restored_count += 1
            
        except Exception as e:
            print(f"❌ خطأ في استعادة {backup_file}: {e}")
    
    print(f"\n🎉 تم استعادة {restored_count} من {len(backup_files)} ملف")
    
    if restored_count > 0:
        print("\n💡 الخطوات التالية:")
        print("  1. اختبر البرنامج: python main_window.py")
        print("  2. إذا كان يعمل، احذف ملفات .backup")

if __name__ == "__main__":
    restore_files()
