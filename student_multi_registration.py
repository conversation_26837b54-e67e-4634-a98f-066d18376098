#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QLabel, QLineEdit, QPushButton, QTableWidget,
    QTableWidgetItem, QFrame, QMessageBox, QComboBox,
    QDateEdit, QFormLayout, QGroupBox, QGridLayout, QHeaderView,
    QTextEdit, QCheckBox, QDialog, QAbstractItemView
)
from PyQt5.QtGui import QFont, QIcon, QColor
from PyQt5.QtCore import Qt, QDate
from datetime import datetime
import json

class StudentMultiSectionRegistrationWindow(QMainWindow):
    """نافذة تسجيل تلميذ واحد في أقسام متعددة"""
    
    def __init__(self, parent=None, db_path="data.db", edit_mode=False, student_id=None):
        super().__init__(parent)
        self.db_path = db_path
        self.selected_sections = []  # قائمة الأقسام المختارة
        
        # متغيرات وضع التحرير
        self.edit_mode = edit_mode
        self.current_student_id = student_id
        self.creation_date = None  # تاريخ الإنشاء للربط بين السجلات

        self.setupUI()
        self.setup_database()

        # تأكد من تهيئة المتغيرات
        if not hasattr(self, 'selected_sections'):
            self.selected_sections = []

        self.load_available_sections()

    def setupUI(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🎓 تسجيل تلميذ في أقسام متعددة")
        self.setFixedSize(1350, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق الخط الافتراضي للنافذة الرئيسية
        self.setFont(QFont("Calibri", 14, QFont.Bold))
        
        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(5)
        
        # إنشاء التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setFont(QFont("Calibri", 15, QFont.Bold))
        
        # تطبيق أنماط جميلة ومحسنة للتبويبات
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #1565C0;
                background-color: #ffffff;
                border-radius: 8px;
                margin-top: 10px;
            }
            
            QTabBar::tab {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ecf0f1, stop: 1 #d5dbdb);
                color: #2c3e50;
                font-family: 'Calibri';
               
                font-weight: bold;
                padding: 12px 25px;
                margin: 2px;
                border: 1px solid #bdc3c7;
                border-radius: 6px;
                min-width: 120px;
                text-align: center;
            }
            
            QTabBar::tab:selected {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #3498db, stop: 1 #1565C0);
                color: white;
                border: 2px solid #1565C0;
                font-weight: bold;
                margin-bottom: -2px;
            }
            
            QTabBar::tab:hover:!selected {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e8f4fd, stop: 1 #d4ecf8);
                color: #1565C0;
                border: 1px solid #3498db;
            }
            
            QTabBar::tab:disabled {
                color: #7f8c8d;
                background: #ecf0f1;
                border: 1px solid #bdc3c7;
            }
            
            QTabWidget::tab-bar {
                alignment: center;
            }
        """)

        # إضافة التبويبات
        self.setup_student_info_tab()
        self.setup_contact_info_tab()
        self.setup_sections_selection_tab()
        self.setup_selected_sections_tab()
        self.setup_registration_summary_tab()
        self.setup_actions_tab()
        
        main_layout.addWidget(self.tab_widget)

    def setup_student_info_tab(self):
        """إعداد تبويب معلومات التلميذ"""
        student_tab = QWidget()
        layout = QVBoxLayout(student_tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إطار معلومات التلميذ الأساسية
        basic_info_frame = QGroupBox("📝 المعلومات الأساسية للتلميذ")
        basic_info_frame.setFont(QFont("Calibri", 15, QFont.Bold))
        basic_info_frame.setStyleSheet("""
            QGroupBox {
                color: #1565C0;
                border: 2px solid #e3f2fd;
                border-radius: 10px;
                padding-top: 15px;
                margin-top: 8px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 10px 0 10px;
                background-color: #ffffff;
                color: #1565C0;
                font-weight: bold;
            }
        """)

        basic_layout = QFormLayout(basic_info_frame)
        basic_layout.setSpacing(20)
        
        # إنشاء حقول معلومات التلميذ
        self.student_name_input = self.create_styled_input()
        self.student_name_input.setPlaceholderText("أدخل الاسم الكامل للتلميذ")
        self.student_name_input.textChanged.connect(self.auto_generate_student_code)

        # حقل رمز التلميذ - يتم إنشاؤه تلقائياً
        self.student_code_input = self.create_styled_input()
        self.student_code_input.setPlaceholderText("سيتم إنشاؤه تلقائياً")
        self.student_code_input.setReadOnly(True)

        # حقل النوع
        self.gender_combo = QComboBox()
        self.gender_combo.addItems(["ذكر", "أنثى"])
        self.gender_combo.setFont(QFont("Calibri", 13, QFont.Bold))
        self.gender_combo.setMinimumHeight(45)

        # حقل المؤسسة الأصلية
        institution_layout = QHBoxLayout()
        self.institution_combo = QComboBox()
        self.institution_combo.setEditable(True)
        self.institution_combo.addItems([
            "الثانوية الاعدادية آسية الوديع",
            "الثانوية الاعدادية أم البنين",
            "الثانوية الاعدادية أنوال",
            "الثانوية الاعدادية إدريس الثاني",
            "الثانوية الاعدادية ابن الهيثم",
            "الثانوية الاعدادية ابن رشد",
            "الثانوية الاعدادية ابن طفيل",
            "الثانوية الاعدادية أبو القاسم الشابي",
            "الثانوية الاعدادية أطلس",
            "الثانوية الاعدادية البحر",
            "الثانوية الاعدادية بدر",
            "الثانوية الاعدادية جابر بن حيان",
            "الثانوية الاعدادية خليج طنجة",
            "الثانوية الاعدادية الزهراء",
            "الثانوية الاعدادية الساحل الشمالي",
            "الثانوية الاعدادية سيدي اليماني",
            "الثانوية الاعدادية عبد الله ابن ياسين",
            "الثانوية الاعدادية عبد الله كنون",
            "الثانوية الاعدادية عبد العزيز مزيان بلفقيه",
            "الثانوية الاعدادية عمر بن عبد العزيز",
            "الثانوية الاعدادية فاطمة المرابط",
            "الثانوية الاعدادية القصبة",
            "الثانوية الاعدادية محمد الخامس",
            "الثانوية الاعدادية محمد السادس",
            "الثانوية الاعدادية محمد بن الحسن الوزاني",
            "الثانوية الاعدادية ماء العيني",
            "الثانوية الاعدادية مولاي عبد الرحمن",
            "الثانوية الاعدادية نهضة",
            "الثانوية الاعدادية طارق بن زياد",
            "الثانوية الاعدادية تورية الشاوي",
            "الثانوية التأهيلية الجامعي",
            "الثانوية التأهيلية الحنصالي",
            "الثانوية التأهيلية الخوارزمي",
            "الثانوية التأهيلية دار الشاوي",
            "الثانوية التأهيلية عبد الله الشفشاوني",
            "الثانوية التأهيلية علال الفاسي",
            "الثانوية التأهيلية محمد الفقيه الركراكي",
            "الثانوية التأهيلية محمد علي الصقلي",
            "الثانوية التأهيلية مولاي سليمان",
            "الثانوية التأهيلية مولاي علي الشرقي",
            "الثانوية التأهيلية أنوال",
            "الثانوية التأهيلية الكركرات",
            "ثانوية الملك فهد ابن عبد العزيز الاعدادية",
            "ثانوية الملك فهد ابن عبد العزيز التأهيلية"
        ])
        self.institution_combo.setFont(QFont("Calibri", 13, QFont.Bold))
        self.institution_combo.lineEdit().setFont(QFont("Calibri", 13, QFont.Bold))
        self.institution_combo.setMinimumHeight(45)

        add_institution_btn = self.create_styled_button("➕ إضافة مؤسسة", "#FF9800")
        add_institution_btn.clicked.connect(self.add_new_institution)
        add_institution_btn.setMinimumHeight(45)
        add_institution_btn.setMaximumWidth(150)

        institution_layout.addWidget(self.institution_combo, 3)
        institution_layout.addWidget(add_institution_btn, 1)

        # إضافة الحقول إلى النموذج
        basic_layout.addRow(self.create_styled_label("🧑‍🎓 اسم التلميذ:"), self.student_name_input)
        basic_layout.addRow(self.create_styled_label("🔢 رمز التلميذ:"), self.student_code_input)
        basic_layout.addRow(self.create_styled_label("👤 النوع:"), self.gender_combo)
        basic_layout.addRow(self.create_styled_label("🏫 المؤسسة الأصلية:"), institution_layout)
        
        layout.addWidget(basic_info_frame)
        layout.addStretch()

        self.tab_widget.addTab(student_tab, "معلومات التلميذ")

    def setup_contact_info_tab(self):
        """إعداد تبويب معلومات الاتصال"""
        contact_tab = QWidget()
        layout = QVBoxLayout(contact_tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إطار معلومات الاتصال
        contact_frame = QGroupBox("📞 معلومات الاتصال")
        contact_frame.setFont(QFont("Calibri", 15, QFont.Bold))
        contact_frame.setStyleSheet("""
            QGroupBox {
                color: #1565C0;
                border: 2px solid #e3f2fd;
                border-radius: 10px;
                padding-top: 15px;
                margin-top: 8px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 10px 0 10px;
                background-color: #ffffff;
                color: #1565C0;
                font-weight: bold;
            }
        """)

        contact_layout = QFormLayout(contact_frame)
        contact_layout.setSpacing(20)

        # إنشاء حقول معلومات الاتصال
        self.phone_input = self.create_styled_input()
        self.phone_input.setPlaceholderText("رقم الهاتف الأساسي")

        self.phone2_input = self.create_styled_input()
        self.phone2_input.setPlaceholderText("رقم هاتف إضافي (اختياري)")

        self.address_input = QTextEdit()
        self.address_input.setMinimumHeight(120)
        self.address_input.setMaximumHeight(200)
        self.address_input.setFont(QFont("Calibri", 13, QFont.Bold))
        self.address_input.setPlaceholderText("العنوان أو ملاحظات إضافية...")

        # إضافة الحقول إلى النموذج
        contact_layout.addRow(self.create_styled_label("📱 رقم الهاتف الأول:"), self.phone_input)
        contact_layout.addRow(self.create_styled_label("📞 رقم الهاتف الثاني:"), self.phone2_input)
        contact_layout.addRow(self.create_styled_label("🏠 العنوان/الملاحظات:"), self.address_input)

        layout.addWidget(contact_frame)
        layout.addStretch()

        self.tab_widget.addTab(contact_tab, "معلومات الاتصال")

    def setup_sections_selection_tab(self):
        """إعداد تبويب اختيار الأقسام"""
        sections_tab = QWidget()
        layout = QVBoxLayout(sections_tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # إطار الأقسام المتاحة
        available_sections_frame = QGroupBox("📚 الأقسام المتاحة للاختيار")
        available_sections_frame.setFont(QFont("Calibri", 15, QFont.Bold))
        available_sections_frame.setStyleSheet("QGroupBox { color: #1565C0; }")

        available_layout = QVBoxLayout(available_sections_frame)
        available_layout.setSpacing(15)

        # جدول الأقسام المتاحة
        self.available_sections_table = QTableWidget()
        self.available_sections_table.setColumnCount(5)
        self.available_sections_table.setHorizontalHeaderLabels(['اختيار', 'المادة', 'القسم', 'الأستاذ', 'المجموعة'])
        self.available_sections_table.setFont(QFont("Calibri", 13, QFont.Bold))
        self.available_sections_table.setAlternatingRowColors(True)
        self.available_sections_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.available_sections_table.horizontalHeader().setStretchLastSection(True)
        self.available_sections_table.verticalHeader().setVisible(False)
        self.available_sections_table.setMinimumHeight(400)

        # تطبيق أنماط جميلة لرأس الجدول
        self.available_sections_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
            QHeaderView::section {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #1565C0, stop: 1 #0d47a1);
                color: white;
                padding: 10px;
                border: none;
                border-right: 1px solid #0d47a1;
                font-family: 'Calibri';
                font-size: 15px;
                font-weight: bold;
                text-align: center;
            }
            QHeaderView::section:first {
                border-top-left-radius: 5px;
            }
            QHeaderView::section:last {
                border-top-right-radius: 5px;
                border-right: none;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #1976d2, stop: 1 #1565C0);
            }
        """)

        # تطبيق الخط على رأس الجدول
        self.available_sections_table.horizontalHeader().setFont(QFont("Calibri", 15, QFont.Bold))

        # تعيين عرض الأعمدة
        header = self.available_sections_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # عمود الاختيار
        header.resizeSection(0, 80)
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # المادة
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # القسم
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # الأستاذ

        available_layout.addWidget(self.available_sections_table)

        # أزرار التحكم في الاختيار
        selection_buttons_layout = QHBoxLayout()

        select_all_btn = self.create_styled_button("✅ اختيار الكل", "#4CAF50")
        select_all_btn.clicked.connect(self.select_all_sections)

        deselect_all_btn = self.create_styled_button("❌ إلغاء الكل", "#F44336")
        deselect_all_btn.clicked.connect(self.deselect_all_sections)

        refresh_btn = self.create_styled_button("🔄 تحديث القائمة", "#2196F3")
        refresh_btn.clicked.connect(self.load_available_sections)

        selection_buttons_layout.addWidget(select_all_btn)
        selection_buttons_layout.addWidget(deselect_all_btn)
        selection_buttons_layout.addWidget(refresh_btn)
        selection_buttons_layout.addStretch()

        available_layout.addLayout(selection_buttons_layout)

        layout.addWidget(available_sections_frame)
        layout.addStretch()

        self.tab_widget.addTab(sections_tab, "اختيار الأقسام")

    def setup_selected_sections_tab(self):
        """إعداد تبويب الأقسام المختارة للتسجيل"""
        selected_tab = QWidget()
        layout = QVBoxLayout(selected_tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إطار الأقسام المختارة
        selected_sections_frame = QGroupBox("✅ الأقسام المختارة للتسجيل")
        selected_sections_frame.setFont(QFont("Calibri", 15, QFont.Bold))
        selected_sections_frame.setStyleSheet("QGroupBox { color: #1565C0; }")

        selected_layout = QVBoxLayout(selected_sections_frame)
        selected_layout.setSpacing(15)

        # جدول الأقسام المختارة
        self.selected_sections_table = QTableWidget()
        self.selected_sections_table.setColumnCount(7)
        self.selected_sections_table.setHorizontalHeaderLabels([
            'المادة', 'القسم', 'الأستاذ', 'المجموعة', 'واجبات التسجيل', 'الواجبات الشهرية', 'إجراء'
        ])
        self.selected_sections_table.setFont(QFont("Calibri", 13, QFont.Bold))
        self.selected_sections_table.setAlternatingRowColors(True)
        self.selected_sections_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.selected_sections_table.horizontalHeader().setStretchLastSection(True)
        self.selected_sections_table.verticalHeader().setVisible(False)
        self.selected_sections_table.setMinimumHeight(350)

        # تطبيق أنماط جميلة لرأس الجدول المختار
        self.selected_sections_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e8f5e8;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
            QHeaderView::section {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #4CAF50, stop: 1 #388E3C);
                color: white;
                padding: 10px;
                border: none;
                border-right: 1px solid #2E7D32;
                font-family: 'Calibri';
                font-size: 15px;
                font-weight: bold;
                text-align: center;
            }
            QHeaderView::section:first {
                border-top-left-radius: 5px;
            }
            QHeaderView::section:last {
                border-top-right-radius: 5px;
                border-right: none;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #66BB6A, stop: 1 #4CAF50);
            }
        """)

        # تطبيق الخط على رأس الجدول
        self.selected_sections_table.horizontalHeader().setFont(QFont("Calibri", 15, QFont.Bold))

        # تعيين عرض الأعمدة
        header2 = self.selected_sections_table.horizontalHeader()
        header2.setSectionResizeMode(0, QHeaderView.Stretch)  # المادة
        header2.setSectionResizeMode(1, QHeaderView.Stretch)  # القسم
        header2.setSectionResizeMode(2, QHeaderView.Stretch)  # الأستاذ
        header2.setSectionResizeMode(3, QHeaderView.Fixed)    # واجبات التسجيل
        header2.resizeSection(3, 120)
        header2.setSectionResizeMode(4, QHeaderView.Fixed)    # الواجبات الشهرية
        header2.resizeSection(4, 120)
        header2.setSectionResizeMode(5, QHeaderView.Fixed)    # إجراء
        header2.resizeSection(5, 100)

        selected_layout.addWidget(self.selected_sections_table)

        # معلومات إحصائية
        stats_layout = QHBoxLayout()

        self.selected_count_label = QLabel("عدد الأقسام المختارة: 0")
        self.selected_count_label.setFont(QFont("Calibri", 15, QFont.Bold))
        self.selected_count_label.setStyleSheet("color: #1565C0;")

        stats_layout.addWidget(self.selected_count_label)
        stats_layout.addStretch()

        selected_layout.addLayout(stats_layout)

        layout.addWidget(selected_sections_frame)
        layout.addStretch()

        self.tab_widget.addTab(selected_tab, "الأقسام المختارة")
        


    def setup_registration_summary_tab(self):
        """إعداد تبويب ملخص التسجيل"""
        summary_tab = QWidget()
        layout = QVBoxLayout(summary_tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(20)

        # إطار ملخص بيانات التلميذ
        student_summary_frame = QGroupBox("📋 ملخص بيانات التلميذ")
        student_summary_frame.setFont(QFont("Calibri", 15, QFont.Bold))
        student_summary_frame.setStyleSheet("QGroupBox { color: #1565C0; }")

        student_summary_layout = QFormLayout(student_summary_frame)
        student_summary_layout.setSpacing(10)

        # عناصر عرض ملخص البيانات
        self.summary_name_label = QLabel("غير محدد")
        self.summary_name_label.setFont(QFont("Calibri", 13, QFont.Bold))

        self.summary_code_label = QLabel("غير محدد")
        self.summary_code_label.setFont(QFont("Calibri", 13, QFont.Bold))

        self.summary_gender_label = QLabel("غير محدد")
        self.summary_gender_label.setFont(QFont("Calibri", 13, QFont.Bold))

        self.summary_phone_label = QLabel("غير محدد")
        self.summary_phone_label.setFont(QFont("Calibri", 13, QFont.Bold))

        student_summary_layout.addRow(self.create_styled_label("الاسم:"), self.summary_name_label)
        student_summary_layout.addRow(self.create_styled_label("الرمز:"), self.summary_code_label)
        student_summary_layout.addRow(self.create_styled_label("النوع:"), self.summary_gender_label)
        student_summary_layout.addRow(self.create_styled_label("الهاتف:"), self.summary_phone_label)

        layout.addWidget(student_summary_frame)

        # إطار ملخص الأقسام المختارة
        sections_summary_frame = QGroupBox("📚 ملخص الأقسام المختارة")
        sections_summary_frame.setFont(QFont("Calibri", 15, QFont.Bold))
        sections_summary_frame.setStyleSheet("QGroupBox { color: #1565C0; }")

        sections_summary_layout = QVBoxLayout(sections_summary_frame)
        sections_summary_layout.setSpacing(15)

        # جدول ملخص الأقسام
        self.summary_sections_table = QTableWidget()
        self.summary_sections_table.setColumnCount(5)
        self.summary_sections_table.setHorizontalHeaderLabels([
            'المادة', 'القسم', 'الأستاذ', 'واجبات التسجيل', 'الواجبات الشهرية'
        ])
        self.summary_sections_table.setFont(QFont("Calibri", 13, QFont.Bold))
        self.summary_sections_table.setAlternatingRowColors(True)
        self.summary_sections_table.horizontalHeader().setStretchLastSection(True)
        self.summary_sections_table.verticalHeader().setVisible(False)
        self.summary_sections_table.setMinimumHeight(200)
        self.summary_sections_table.setEditTriggers(QAbstractItemView.NoEditTriggers)

        # تطبيق أنماط جميلة لرأس جدول الملخص
        self.summary_sections_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #fff3e0;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
            QHeaderView::section {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #FF9800, stop: 1 #F57C00);
                color: white;
                padding: 10px;
                border: none;
                border-right: 1px solid #E65100;
                font-family: 'Calibri';
                font-size: 15px;
                font-weight: bold;
                text-align: center;
            }
            QHeaderView::section:first {
                border-top-left-radius: 5px;
            }
            QHeaderView::section:last {
                border-top-right-radius: 5px;
                border-right: none;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #FFB74D, stop: 1 #FF9800);
            }
        """)

        # تطبيق الخط على رأس الجدول
        self.summary_sections_table.horizontalHeader().setFont(QFont("Calibri", 15, QFont.Bold))

        # تعيين عرض الأعمدة للملخص
        summary_header = self.summary_sections_table.horizontalHeader()
        summary_header.setSectionResizeMode(0, QHeaderView.Stretch)  # المادة
        summary_header.setSectionResizeMode(1, QHeaderView.Stretch)  # القسم
        summary_header.setSectionResizeMode(2, QHeaderView.Stretch)  # الأستاذ
        summary_header.setSectionResizeMode(3, QHeaderView.Fixed)    # واجبات التسجيل
        summary_header.resizeSection(3, 120)
        summary_header.setSectionResizeMode(4, QHeaderView.Fixed)    # الواجبات الشهرية
        summary_header.resizeSection(4, 120)

        sections_summary_layout.addWidget(self.summary_sections_table)

        # إحصائيات التسجيل
        stats_frame = QGroupBox("📊 إحصائيات التسجيل")
        stats_frame.setFont(QFont("Calibri", 15, QFont.Bold))
        stats_frame.setStyleSheet("QGroupBox { color: #1565C0; }")

        stats_layout = QFormLayout(stats_frame)
        stats_layout.setSpacing(10)

        self.total_sections_label = QLabel("0")
        self.total_sections_label.setFont(QFont("Calibri", 13, QFont.Bold))

        self.total_subjects_label = QLabel("0")
        self.total_subjects_label.setFont(QFont("Calibri", 13, QFont.Bold))

        self.total_registration_fees_label = QLabel("0.00 درهم")
        self.total_registration_fees_label.setFont(QFont("Calibri", 13, QFont.Bold))

        self.total_monthly_fees_label = QLabel("0.00 درهم")
        self.total_monthly_fees_label.setFont(QFont("Calibri", 13, QFont.Bold))

        stats_layout.addRow(self.create_styled_label("إجمالي الأقسام:"), self.total_sections_label)
        stats_layout.addRow(self.create_styled_label("عدد المواد:"), self.total_subjects_label)
        stats_layout.addRow(self.create_styled_label("إجمالي واجبات التسجيل:"), self.total_registration_fees_label)
        stats_layout.addRow(self.create_styled_label("إجمالي الواجبات الشهرية:"), self.total_monthly_fees_label)

        sections_summary_layout.addWidget(stats_frame)

        layout.addWidget(sections_summary_frame)

        layout.addStretch()

        self.tab_widget.addTab(summary_tab, "ملخص التسجيل")

    def setup_actions_tab(self):
        """إعداد تبويب الإجراءات"""
        actions_tab = QWidget()
        layout = QVBoxLayout(actions_tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(20)

        # إطار إجراءات التسجيل
        actions_frame = QGroupBox("⚡ إجراءات التسجيل")
        actions_frame.setFont(QFont("Calibri", 15, QFont.Bold))
        actions_frame.setStyleSheet("QGroupBox { color: #1565C0; }")

        actions_layout = QVBoxLayout(actions_frame)
        actions_layout.setSpacing(20)

        # أزرار الإجراءات الرئيسية
        main_buttons_layout = QGridLayout()
        main_buttons_layout.setSpacing(15)

        # زر تعديل التسجيل (بدلاً من حفظ التسجيل)
        save_registration_btn = self.create_styled_button("✏️ تعديل التسجيل", "#FF9800")
        save_registration_btn.clicked.connect(self.update_registration)
        save_registration_btn.setMinimumHeight(60)

        # زر تحديث الملخص
        update_summary_btn = self.create_styled_button("🔄 تحديث الملخص", "#2196F3")
        update_summary_btn.clicked.connect(self.update_summary)
        update_summary_btn.setMinimumHeight(60)

        # زر مسح جميع البيانات
        clear_all_btn = self.create_styled_button("🗑️ مسح جميع البيانات", "#F44336")
        clear_all_btn.clicked.connect(self.clear_all_data)
        clear_all_btn.setMinimumHeight(60)

        # زر طباعة التسجيل
        print_btn = self.create_styled_button("🖨️ طباعة التسجيل", "#9C27B0")
        print_btn.clicked.connect(self.print_registration)
        print_btn.setMinimumHeight(60)

        main_buttons_layout.addWidget(save_registration_btn, 0, 0)
        main_buttons_layout.addWidget(update_summary_btn, 0, 1)
        main_buttons_layout.addWidget(clear_all_btn, 1, 0)
        main_buttons_layout.addWidget(print_btn, 1, 1)

        actions_layout.addLayout(main_buttons_layout)

        # إطار حالة النظام
        status_frame = QGroupBox("📊 حالة النظام")
        status_frame.setFont(QFont("Calibri", 15, QFont.Bold))
        status_frame.setStyleSheet("QGroupBox { color: #1565C0; }")

        status_layout = QFormLayout(status_frame)
        status_layout.setSpacing(10)

        self.status_label = QLabel("جاهز للتسجيل")
        self.status_label.setFont(QFont("Calibri", 13, QFont.Bold))

        self.last_save_label = QLabel("لم يتم الحفظ بعد")
        self.last_save_label.setFont(QFont("Calibri", 13, QFont.Bold))

        status_layout.addRow(self.create_styled_label("الحالة:"), self.status_label)
        status_layout.addRow(self.create_styled_label("آخر حفظ:"), self.last_save_label)

        actions_layout.addWidget(status_frame)

        layout.addWidget(actions_frame)

        layout.addStretch()

        self.tab_widget.addTab(actions_tab, "الإجراءات")

    def create_styled_input(self):
        """إنشاء حقل إدخال منسق"""
        input_field = QLineEdit()
        input_field.setFont(QFont("Calibri", 13, QFont.Bold))
        input_field.setMinimumHeight(45)
        return input_field

    def create_styled_label(self, text):
        """إنشاء تسمية منسقة"""
        label = QLabel(text)
        label.setFont(QFont("Calibri", 15, QFont.Bold))
        label.setStyleSheet("color: #1565C0;")
        return label

    def create_styled_button(self, text, color="#2196F3"):
        """إنشاء زر منسق بتصميم احترافي"""
        button = QPushButton(text)
        button.setFont(QFont("Calibri", 13, QFont.Bold))
        button.setMinimumHeight(45)
        button.setMinimumWidth(180)
        button.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px;
            }}
        """)
        return button

    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # حذف الجدول القديم إذا كان موجوداً وإعادة إنشاؤه
            cursor.execute('DROP TABLE IF EXISTS تسجيل_التلاميذ_متعدد_الاقسام')
            
            # إنشاء الجدول الموحد لجميع البيانات
            cursor.execute('''
                CREATE TABLE تسجيل_التلاميذ_متعدد_الاقسام (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,

                    -- معلومات التلميذ
                    اسم_التلميذ TEXT NOT NULL,
                    رمز_التلميذ TEXT,
                    النوع TEXT,
                    المؤسسة_الاصلية TEXT,
                    رقم_الهاتف_الأول TEXT,
                    رقم_الهاتف_الثاني TEXT,
                    العنوان_الملاحظات TEXT,

                    -- معلومات القسم
                    المادة TEXT NOT NULL,
                    القسم TEXT NOT NULL,
                    اسم_الاستاذ TEXT,

                    -- الواجبات المالية
                    واجبات_التسجيل REAL DEFAULT 0.0,
                    الواجبات_الشهرية REAL DEFAULT 0.0,

                    -- تواريخ النظام
                    تاريخ_التسجيل DATETIME DEFAULT CURRENT_TIMESTAMP,
                    تاريخ_التحديث DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            
            print("تم إنشاء قاعدة البيانات بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إعداد قاعدة البيانات: {str(e)}")
            print(f"خطأ في قاعدة البيانات: {str(e)}")

    def generate_next_student_code(self):
        """إنشاء رمز التلميذ التالي تلقائياً"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # البحث عن جميع أرقام التلاميذ الموجودة في عمود رمز_التلميذ
            cursor.execute("""
                SELECT DISTINCT رمز_التلميذ FROM جدول_البيانات
                WHERE رمز_التلميذ IS NOT NULL AND رمز_التلميذ != ''
                ORDER BY رمز_التلميذ
            """)

            results = cursor.fetchall()
            conn.close()

            # استخراج جميع الأرقام الصحيحة
            numbers = []
            for result in results:
                code = result[0].strip()
                # محاولة استخراج الرقم من الرمز
                if code.startswith('R') and len(code) > 1:
                    try:
                        number = int(code[1:])
                        numbers.append(number)
                    except ValueError:
                        continue
                else:
                    # إذا كان الرمز رقم فقط
                    try:
                        number = int(code)
                        numbers.append(number)
                    except ValueError:
                        continue

            if numbers:
                # العثور على أعلى رقم وإضافة 1
                max_number = max(numbers)
                next_number = max_number + 1
                return f"R{next_number}"
            else:
                # لا توجد أرقام صحيحة، ابدأ من R10000
                return "R10000"

        except Exception as e:
            print(f"خطأ في إنشاء رمز التلميذ: {str(e)}")
            return "R10000"

    def auto_generate_student_code(self):
        """إنشاء رمز التلميذ تلقائياً عند إدخال الاسم"""
        if self.student_name_input.text().strip():
            new_code = self.generate_next_student_code()
            self.student_code_input.setText(new_code)
        else:
            self.student_code_input.clear()

    def add_new_institution(self):
        """إضافة مؤسسة جديدة"""
        from PyQt5.QtWidgets import QInputDialog

        text, ok = QInputDialog.getText(
            self,
            'إضافة مؤسسة جديدة',
            'اسم المؤسسة الجديدة:',
            QLineEdit.Normal,
            ''
        )

        if ok and text.strip():
            # التحقق من عدم وجود المؤسسة مسبقاً
            existing_items = [self.institution_combo.itemText(i) for i in range(self.institution_combo.count())]
            if text.strip() not in existing_items:
                self.institution_combo.addItem(text.strip())
                self.institution_combo.setCurrentText(text.strip())
                QMessageBox.information(self, "نجح", f"تم إضافة المؤسسة '{text.strip()}' بنجاح.")
            else:
                QMessageBox.warning(self, "تحذير", "هذه المؤسسة موجودة بالفعل.")

    def update_registration_fee(self, row_index, text):
        """تحديث واجبات التسجيل للقسم"""
        try:
            if row_index < len(self.selected_sections):
                fee = float(text) if text.strip() else 0.0
                self.selected_sections[row_index]['registration_fee'] = fee
                self.update_summary()
        except ValueError:
            pass  # تجاهل القيم غير الصحيحة

    def update_monthly_fee(self, row_index, text):
        """تحديث الواجبات الشهرية للقسم"""
        try:
            if row_index < len(self.selected_sections):
                fee = float(text) if text.strip() else 0.0
                self.selected_sections[row_index]['monthly_fee'] = fee
                self.update_summary()
        except ValueError:
            pass  # تجاهل القيم غير الصحيحة

    def load_available_sections(self):
        """تحميل الأقسام المتاحة من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # تحميل البيانات من جدول الاساتذة مع JOIN للحصول على الأسماء
            cursor.execute("""
                SELECT DISTINCT 
                    a.id as teacher_id,
                    m.اسم_المادة as المادة, 
                    q.اسم_القسم as القسم, 
                    a.اسم_الاستاذ, 
                    g.اسم_المجموعة as المجموعة
                FROM الاساتذة a
                LEFT JOIN المواد_الدراسية m ON a.مادة_id = m.id
                LEFT JOIN الاقسام q ON a.قسم_id = q.id
                LEFT JOIN المجموعات g ON a.مجموعة_id = g.id
                WHERE m.اسم_المادة IS NOT NULL AND q.اسم_القسم IS NOT NULL
                ORDER BY m.اسم_المادة, q.اسم_القسم
            """)

            sections_data = cursor.fetchall()
            conn.close()

            # مسح الجدول الحالي
            self.available_sections_table.setRowCount(0)

            # إضافة البيانات للجدول
            for row_index, (teacher_id, subject, section, teacher, group_name) in enumerate(sections_data):
                self.available_sections_table.insertRow(row_index)

                # عمود الاختيار - checkbox محسن
                checkbox = QCheckBox()
                checkbox.setFont(QFont("Calibri", 13, QFont.Bold))
                checkbox.setStyleSheet("""
                    QCheckBox {
                        spacing: 8px;
                        padding: 5px;
                    }
                    QCheckBox::indicator {
                        width: 22px;
                        height: 22px;
                        border: 2px solid #bdc3c7;
                        border-radius: 5px;
                        background-color: white;
                    }
                    QCheckBox::indicator:hover {
                        border: 2px solid #3498db;
                        background-color: #e3f2fd;
                        transform: scale(1.05);
                    }
                    QCheckBox::indicator:checked {
                        background-color: #1565C0;
                        border: 2px solid #1565C0;
                        color: white;
                    }
                    QCheckBox::indicator:checked:hover {
                        background-color: #1976d2;
                        border: 2px solid #1976d2;
                    }
                    QCheckBox::indicator:pressed {
                        background-color: #e3f2fd;
                        border: 2px solid #1565C0;
                    }
                    QCheckBox::indicator:checked:after {
                        content: "✓";
                        color: white;
                        font-weight: bold;
                        font-size: 16px;
                        text-align: center;
                    }
                """)
                
                # ربط إشارة التغيير بدالة مخصصة لكل مربع اختيار
                checkbox.stateChanged.connect(lambda state, cb=checkbox: self.on_single_section_changed(cb, state))
                
                # حفظ البيانات المهمة كخصائص في الـ checkbox
                checkbox.setProperty("teacher_id", teacher_id)  # المفتاح الأساسي
                checkbox.setProperty("group_name", group_name or "")
                checkbox.setProperty("row_index", row_index)  # حفظ فهرس الصف
                
                # إنشاء widget مركزي لمحاذاة مربع الاختيار في وسط الخلية
                checkbox_widget = QWidget()
                checkbox_layout = QHBoxLayout(checkbox_widget)
                checkbox_layout.addWidget(checkbox)
                checkbox_layout.setAlignment(Qt.AlignCenter)
                checkbox_layout.setContentsMargins(0, 0, 0, 0)
                
                self.available_sections_table.setCellWidget(row_index, 0, checkbox_widget)

                # باقي الأعمدة مع تطبيق الخط
                subject_item = QTableWidgetItem(subject or "")
                subject_item.setFont(QFont("Calibri", 13, QFont.Bold))
                self.available_sections_table.setItem(row_index, 1, subject_item)

                section_item = QTableWidgetItem(section or "")
                section_item.setFont(QFont("Calibri", 13, QFont.Bold))
                self.available_sections_table.setItem(row_index, 2, section_item)

                teacher_item = QTableWidgetItem(teacher or "")
                teacher_item.setFont(QFont("Calibri", 13, QFont.Bold))
                self.available_sections_table.setItem(row_index, 3, teacher_item)

                # إضافة عمود المجموعة
                group_item = QTableWidgetItem(group_name or "غير محدد")
                group_item.setFont(QFont("Calibri", 13, QFont.Bold))
                self.available_sections_table.setItem(row_index, 4, group_item)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل الأقسام المتاحة: {str(e)}")

    def on_section_selection_changed(self):
        """معالج تغيير اختيار الأقسام - للتوافق مع الكود القديم"""
        self.update_selected_sections()
        self.update_summary()

    def update_selected_sections(self):
        """تحديث جدول الأقسام المختارة"""
        try:
            # مسح الجدول الحالي
            self.selected_sections_table.setRowCount(0)
            self.selected_sections = []

            # البحث عن الأقسام المختارة
            for row in range(self.available_sections_table.rowCount()):
                checkbox_widget = self.available_sections_table.cellWidget(row, 0)
                if checkbox_widget:
                    # البحث عن مربع الاختيار داخل الـ widget
                    checkbox = checkbox_widget.findChild(QCheckBox)
                    if checkbox and checkbox.isChecked():
                        # التأكد من وجود العناصر قبل قراءة النص
                        subject_item = self.available_sections_table.item(row, 1)
                        section_item = self.available_sections_table.item(row, 2)
                        teacher_item = self.available_sections_table.item(row, 3)
                        
                        if not (subject_item and section_item and teacher_item):
                            continue
                            
                        subject = subject_item.text()
                        section = section_item.text()
                        teacher = teacher_item.text()
                    
                    # الحصول على اسم المجموعة من خاصية الـ checkbox
                    group_name = checkbox.property("group_name") or ""

                    # إضافة للقائمة
                    section_data = {
                        'subject': subject,
                        'section': section,
                        'teacher': teacher,
                        'group_name': group_name,
                        'registration_fee': 0.0,
                        'monthly_fee': 0.0
                    }
                    self.selected_sections.append(section_data)

                    # إضافة للجدول
                    row_index = self.selected_sections_table.rowCount()
                    self.selected_sections_table.insertRow(row_index)

                    # إضافة البيانات مع تطبيق الخط
                    subject_item = QTableWidgetItem(subject)
                    subject_item.setFont(QFont("Calibri", 13, QFont.Bold))
                    self.selected_sections_table.setItem(row_index, 0, subject_item)

                    section_item = QTableWidgetItem(section)
                    section_item.setFont(QFont("Calibri", 13, QFont.Bold))
                    self.selected_sections_table.setItem(row_index, 1, section_item)

                    teacher_item = QTableWidgetItem(teacher)
                    teacher_item.setFont(QFont("Calibri", 13, QFont.Bold))
                    self.selected_sections_table.setItem(row_index, 2, teacher_item)

                    # إضافة عمود المجموعة
                    group_item = QTableWidgetItem(group_name or "غير محدد")
                    group_item.setFont(QFont("Calibri", 13, QFont.Bold))
                    self.selected_sections_table.setItem(row_index, 3, group_item)

                    # حقل واجبات التسجيل
                    registration_fee_input = QLineEdit()
                    registration_fee_input.setPlaceholderText("0.00")
                    registration_fee_input.setFont(QFont("Calibri", 13, QFont.Bold))
                    registration_fee_input.setAlignment(Qt.AlignCenter)
                    registration_fee_input.textChanged.connect(lambda text, idx=row_index: self.update_registration_fee(idx, text))
                    self.selected_sections_table.setCellWidget(row_index, 4, registration_fee_input)

                    # حقل الواجبات الشهرية
                    monthly_fee_input = QLineEdit()
                    monthly_fee_input.setPlaceholderText("0.00")
                    monthly_fee_input.setFont(QFont("Calibri", 13, QFont.Bold))
                    monthly_fee_input.setAlignment(Qt.AlignCenter)
                    monthly_fee_input.textChanged.connect(lambda text, idx=row_index: self.update_monthly_fee(idx, text))
                    self.selected_sections_table.setCellWidget(row_index, 5, monthly_fee_input)

                    # زر الحذف
                    remove_btn = QPushButton("🗑️ حذف")
                    remove_btn.setFont(QFont("Calibri", 13, QFont.Bold))
                    remove_btn.clicked.connect(lambda: self.remove_selected_section(row_index))
                    self.selected_sections_table.setCellWidget(row_index, 6, remove_btn)

            # تحديث العداد
            self.selected_count_label.setText(f"عدد الأقسام المختارة: {len(self.selected_sections)}")
            
        except Exception as e:
            print(f"خطأ في تحديث الأقسام المختارة: {e}")
            # في حالة الخطأ، تأكد من تحديث العداد على الأقل
            self.selected_count_label.setText(f"عدد الأقسام المختارة: {len(self.selected_sections)}")

    def remove_selected_section(self, row):
        """حذف قسم من الأقسام المختارة"""
        try:
            if row < len(self.selected_sections):
                # الحصول على بيانات القسم المحذوف
                removed_section = self.selected_sections[row]

                # البحث عن القسم في الجدول المتاح وإلغاء تحديده باستخدام teacher_id
                removed_teacher_id = removed_section.get('teacher_id')
                if removed_teacher_id:
                    for available_row in range(self.available_sections_table.rowCount()):
                        checkbox_widget = self.available_sections_table.cellWidget(available_row, 0)
                        if checkbox_widget:
                            checkbox = checkbox_widget.findChild(QCheckBox)
                            if checkbox and checkbox.property("teacher_id") == removed_teacher_id:
                                # فصل الإشارة مؤقتاً لتجنب التحديث المضاعف
                                checkbox.blockSignals(True)
                                checkbox.setChecked(False)
                                checkbox.blockSignals(False)
                                break

                # إزالة من القائمة والجدول مباشرة
                self.selected_sections.pop(row)
                self.selected_sections_table.removeRow(row)
                
                # تحديث العداد
                self.selected_count_label.setText(f"عدد الأقسام المختارة: {len(self.selected_sections)}")
                
                # تحديث الملخص
                self.update_summary()
                
        except Exception as e:
            print(f"خطأ في حذف القسم المحدد: {e}")

    def select_all_sections(self):
        """اختيار جميع الأقسام"""
        try:
            for row in range(self.available_sections_table.rowCount()):
                checkbox_widget = self.available_sections_table.cellWidget(row, 0)
                if checkbox_widget:
                    checkbox = checkbox_widget.findChild(QCheckBox)
                    if checkbox and not checkbox.isChecked():
                        checkbox.setChecked(True)
        except Exception as e:
            print(f"خطأ في اختيار جميع الأقسام: {e}")

    def deselect_all_sections(self):
        """إلغاء اختيار جميع الأقسام"""
        for row in range(self.available_sections_table.rowCount()):
            checkbox_widget = self.available_sections_table.cellWidget(row, 0)
            if checkbox_widget:
                checkbox = checkbox_widget.findChild(QCheckBox)
                if checkbox:
                    checkbox.setChecked(False)

    def clear_all_sections(self):
        """إلغاء اختيار جميع الأقسام"""
        try:
            for row in range(self.available_sections_table.rowCount()):
                checkbox_widget = self.available_sections_table.cellWidget(row, 0)
                if checkbox_widget:
                    checkbox = checkbox_widget.findChild(QCheckBox)
                    if checkbox and checkbox.isChecked():
                        checkbox.setChecked(False)
        except Exception as e:
            print(f"خطأ في إلغاء اختيار جميع الأقسام: {e}")

    def update_summary(self):
        """تحديث ملخص التسجيل"""
        # تحديث معلومات التلميذ
        self.summary_name_label.setText(self.student_name_input.text() or "غير محدد")
        self.summary_code_label.setText(self.student_code_input.text() or "غير محدد")
        self.summary_gender_label.setText(self.gender_combo.currentText())
        self.summary_phone_label.setText(self.phone_input.text() or "غير محدد")

        # تحديث جدول الأقسام في الملخص
        self.summary_sections_table.setRowCount(0)

        total_registration_fees = 0.0
        total_monthly_fees = 0.0

        for row_index, section in enumerate(self.selected_sections):
            self.summary_sections_table.insertRow(row_index)
            # إضافة البيانات مع تطبيق الخط
            subject_item = QTableWidgetItem(section['subject'])
            subject_item.setFont(QFont("Calibri", 13, QFont.Bold))
            self.summary_sections_table.setItem(row_index, 0, subject_item)

            section_item = QTableWidgetItem(section['section'])
            section_item.setFont(QFont("Calibri", 13, QFont.Bold))
            self.summary_sections_table.setItem(row_index, 1, section_item)

            teacher_item = QTableWidgetItem(section['teacher'])
            teacher_item.setFont(QFont("Calibri", 13, QFont.Bold))
            self.summary_sections_table.setItem(row_index, 2, teacher_item)

            # عرض الواجبات مع تطبيق الخط
            reg_fee = section.get('registration_fee', 0.0)
            monthly_fee = section.get('monthly_fee', 0.0)

            reg_fee_item = QTableWidgetItem(f"{reg_fee:.2f} درهم")
            reg_fee_item.setFont(QFont("Calibri", 13, QFont.Bold))
            self.summary_sections_table.setItem(row_index, 3, reg_fee_item)

            monthly_fee_item = QTableWidgetItem(f"{monthly_fee:.2f} درهم")
            monthly_fee_item.setFont(QFont("Calibri", 13, QFont.Bold))
            self.summary_sections_table.setItem(row_index, 4, monthly_fee_item)

            # حساب الإجماليات
            total_registration_fees += reg_fee
            total_monthly_fees += monthly_fee

        # تحديث الإحصائيات
        total_sections = len(self.selected_sections)
        unique_subjects = len(set(section['subject'] for section in self.selected_sections))

        self.total_sections_label.setText(str(total_sections))
        self.total_subjects_label.setText(str(unique_subjects))
        self.total_registration_fees_label.setText(f"{total_registration_fees:.2f} درهم")
        self.total_monthly_fees_label.setText(f"{total_monthly_fees:.2f} درهم")

        # تحديث حالة النظام
        if total_sections > 0 and self.student_name_input.text().strip():
            self.status_label.setText("جاهز للحفظ")
        else:
            self.status_label.setText("يرجى إكمال البيانات")

    def save_registration(self):
        """حفظ تسجيل التلميذ"""
        # التحقق من صحة البيانات
        if not self.student_name_input.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم التلميذ.")
            return

        if not hasattr(self, 'selected_sections') or not self.selected_sections:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار قسم واحد على الأقل.")
            return

        # رسالة تشخيصية
        print(f"عدد الأقسام المختارة: {len(self.selected_sections)}")
        for section in self.selected_sections:
            print(f"قسم: {section}")
            print(f"اسم المجموعة: {section.get('group_name', 'غير محدد')}")

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # حفظ بيانات التلميذ لكل قسم مختار
            for section in self.selected_sections:
                cursor.execute('''
                    INSERT INTO جدول_البيانات (
                        اسم_التلميذ, رمز_التلميذ, النوع, المؤسسة_الاصلية,
                        رقم_الهاتف_الأول, رقم_الهاتف_الثاني, ملاحظات, اسم_المجموعة,
                        المادة, القسم, الاستاذ, مبلغ_التسجيل, الواجب_الشهري
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    self.student_name_input.text().strip(),
                    self.student_code_input.text().strip(),
                    self.gender_combo.currentText(),
                    self.institution_combo.currentText().strip(),
                    self.phone_input.text().strip(),
                    self.phone2_input.text().strip(),
                    self.address_input.toPlainText().strip(),
                    section.get('group_name', ''),
                    section['subject'],
                    section['section'],
                    section['teacher'],
                    section.get('registration_fee', 0.0),
                    section.get('monthly_fee', 0.0)
                ))

            conn.commit()
            conn.close()

            # تحديث حالة النظام
            self.status_label.setText("تم الحفظ بنجاح")

            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.last_save_label.setText(f"آخر حفظ: {current_time}")

            QMessageBox.information(self, "نجح",
                f"تم حفظ تسجيل التلميذ '{self.student_name_input.text()}' في {len(self.selected_sections)} قسم بنجاح.")

            # مسح البيانات للتسجيل التالي
            if QMessageBox.question(self, "تأكيد", "هل تريد مسح البيانات لتسجيل تلميذ جديد؟") == QMessageBox.Yes:
                self.clear_all_data()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ التسجيل: {str(e)}")

    def update_registration(self):
        """تعديل تسجيل التلميذ - دالة جديدة للتعديل"""
        try:
            # التحقق من صحة البيانات
            if not self.student_name_input.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم التلميذ.")
                return

            if not self.selected_sections:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار قسم واحد على الأقل.")
                return

            # تأكيد التعديل
            reply = QMessageBox.question(
                self, "تأكيد التعديل",
                f"هل أنت متأكد من تعديل بيانات التلميذ '{self.student_name_input.text()}'؟\n"
                f"سيتم تحديث جميع البيانات والأقسام المرتبطة.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # الاتصال بقاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # تحديث البيانات الأساسية للتلميذ
            student_name = self.student_name_input.text().strip()
            student_code = self.student_code_input.text().strip()
            gender = self.gender_combo.currentText()
            phone1 = self.phone_input.text().strip()
            phone2 = self.phone2_input.text().strip()
            institution = self.institution_combo.currentText()

            # تحديث جميع السجلات المرتبطة بالتلميذ
            for section_data in self.selected_sections:
                if section_data.get('is_existing', False):
                    # تحديث السجل الموجود
                    cursor.execute("""
                        UPDATE جدول_البيانات
                        SET اسم_التلميذ = ?, رمز_التلميذ = ?, النوع = ?,
                            رقم_الهاتف_الأول = ?, رقم_الهاتف_الثاني = ?, المؤسسة_الأصلية = ?,
                            مبلغ_التسجيل = ?, الواجب_الشهري = ?
                        WHERE id = ?
                    """, (
                        student_name, student_code, gender, phone1, phone2, institution,
                        section_data['registration_fee'], section_data['monthly_fee'],
                        section_data['id']
                    ))
                else:
                    # إضافة سجل جديد
                    cursor.execute("""
                        INSERT INTO جدول_البيانات (
                            القسم, اسم_المجموعة, مبلغ_التسجيل, الواجب_الشهري,
                            اسم_التلميذ, رمز_التلميذ, النوع, رقم_الهاتف_الأول,
                            رقم_الهاتف_الثاني, المؤسسة_الأصلية, تاريخ_الانشاء, حالة_الدفع
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        section_data['section'], section_data['group_name'],
                        section_data['registration_fee'], section_data['monthly_fee'],
                        student_name, student_code, gender, phone1, phone2, institution,
                        section_data['creation_date'], "غير مدفوع"
                    ))

            conn.commit()
            conn.close()

            # رسالة نجاح
            QMessageBox.information(
                self, "تم التعديل",
                f"تم تعديل بيانات التلميذ '{student_name}' بنجاح!\n"
                f"عدد الأقسام المحدثة: {len(self.selected_sections)}"
            )

            # تحديث الملخص
            self.update_summary()

            print(f"✅ تم تعديل تسجيل التلميذ: {student_name}")

        except Exception as e:
            print(f"❌ خطأ في تعديل التسجيل: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"فشل في تعديل التسجيل: {str(e)}")

    def clear_all_data(self):
        """مسح جميع البيانات"""
        reply = QMessageBox.question(
            self, "تأكيد",
            "هل أنت متأكد من مسح جميع البيانات؟\nهذا الإجراء لا يمكن التراجع عنه.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        if reply != QMessageBox.Yes:
            return

        # مسح معلومات التلميذ
        self.student_name_input.clear()
        self.student_code_input.clear()
        self.gender_combo.setCurrentIndex(0)
        self.institution_combo.setCurrentIndex(0)
        self.phone_input.clear()
        self.phone2_input.clear()
        self.address_input.clear()

        # إلغاء اختيار جميع الأقسام
        self.deselect_all_sections()

        # تحديث الملخص
        self.update_summary()

        # تحديث حالة النظام
        self.status_label.setText("تم مسح البيانات")

    def print_registration(self):
        """طباعة تسجيل التلميذ"""
        if not self.student_name_input.text().strip() or not self.selected_sections:
            QMessageBox.warning(self, "تحذير", "لا توجد بيانات للطباعة.")
            return

        # إنشاء تقرير بسيط
        total_registration_fees = sum(section.get('registration_fee', 0.0) for section in self.selected_sections)
        total_monthly_fees = sum(section.get('monthly_fee', 0.0) for section in self.selected_sections)

        report = f"""
تقرير تسجيل التلميذ في أقسام متعددة
====================================

معلومات التلميذ:
- الاسم: {self.student_name_input.text()}
- الرمز: {self.student_code_input.text() or 'غير محدد'}
- النوع: {self.gender_combo.currentText()}
- المؤسسة الأصلية: {self.institution_combo.currentText() or 'غير محدد'}
- الهاتف: {self.phone_input.text() or 'غير محدد'}

الأقسام المسجلة:
"""

        for i, section in enumerate(self.selected_sections, 1):
            reg_fee = section.get('registration_fee', 0.0)
            monthly_fee = section.get('monthly_fee', 0.0)
            report += f"{i}. {section['subject']} - {section['section']} - {section['teacher']}\n"
            report += f"   واجبات التسجيل: {reg_fee:.2f} درهم | الواجبات الشهرية: {monthly_fee:.2f} درهم\n\n"

        report += f"الإحصائيات:\n"
        report += f"- إجمالي الأقسام: {len(self.selected_sections)}\n"
        report += f"- عدد المواد المختلفة: {len(set(section['subject'] for section in self.selected_sections))}\n"
        report += f"- إجمالي واجبات التسجيل: {total_registration_fees:.2f} درهم\n"
        report += f"- إجمالي الواجبات الشهرية: {total_monthly_fees:.2f} درهم\n"
        report += f"- المجموع الكلي: {total_registration_fees + total_monthly_fees:.2f} درهم\n\n"
        report += f"تاريخ التسجيل: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        # عرض التقرير في نافذة
        dialog = QDialog(self)
        dialog.setWindowTitle("تقرير التسجيل")
        dialog.setFixedSize(600, 500)
        dialog.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(dialog)

        text_area = QTextEdit()
        text_area.setPlainText(report)
        text_area.setFont(QFont("Calibri", 12))
        text_area.setReadOnly(True)

        layout.addWidget(text_area)

        buttons_layout = QHBoxLayout()

        print_btn = QPushButton("🖨️ طباعة")
        print_btn.clicked.connect(lambda: QMessageBox.information(dialog, "طباعة", "تم إرسال التقرير للطابعة."))

        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(dialog.close)

        buttons_layout.addWidget(print_btn)
        buttons_layout.addWidget(close_btn)

        layout.addLayout(buttons_layout)

        dialog.exec_()

    def setup_edit_mode(self, student_id):
        """إعداد النافذة لوضع التحرير"""
        if not student_id:
            return False
            
        self.edit_mode = True
        self.current_student_id = student_id
        
        # تغيير عنوان النافذة
        self.setWindowTitle(f"🎓 تحرير بيانات التلميذ - ID: {student_id}")
        
        # تحميل بيانات الطالب
        return self.load_student_data()
    
    def load_student_data(self):
        """تحميل بيانات الطالب للتحرير"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # جلب جميع سجلات الطالب (قد يكون في أقسام متعددة)
            cursor.execute("""
                SELECT * FROM جدول_البيانات 
                WHERE id = ? OR تاريخ_الانشاء = (
                    SELECT تاريخ_الانشاء FROM جدول_البيانات WHERE id = ?
                )
                ORDER BY id
            """, (self.current_student_id, self.current_student_id))
            
            records = cursor.fetchall()
            conn.close()
            
            if not records:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على بيانات الطالب")
                return False
            
            # استخدام أول سجل للبيانات الأساسية
            main_record = records[0]
            self.creation_date = main_record[12]  # تاريخ الإنشاء (index 12)
            
            # تعبئة البيانات الأساسية
            self.fill_basic_data(main_record)
            
            # تعبئة بيانات الأقسام
            self.fill_sections_data(records)
            
            return True
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات الطالب: {str(e)}")
            return False
    
    def fill_basic_data(self, record):
        """تعبئة البيانات الأساسية للطالب"""
        try:
            # البحث عن الحقول وتعبئتها
            if hasattr(self, 'student_name_input'):
                self.student_name_input.setText(record[1] or "")  # اسم التلميذ
                
            if hasattr(self, 'student_code_input'):
                self.student_code_input.setText(record[2] or "")  # رمز التلميذ
                
            if hasattr(self, 'gender_combo'):
                gender = record[3] or ""
                index = self.gender_combo.findText(gender)
                if index >= 0:
                    self.gender_combo.setCurrentIndex(index)
                    
            if hasattr(self, 'phone1_input'):
                self.phone1_input.setText(record[4] or "")  # رقم الهاتف الأول
                
            if hasattr(self, 'phone2_input'):
                self.phone2_input.setText(record[5] or "")  # رقم الهاتف الثاني
                
            if hasattr(self, 'notes_input'):
                self.notes_input.setText(record[6] or "")  # ملاحظات
                
            if hasattr(self, 'institution_combo'):
                institution = record[9] or ""
                index = self.institution_combo.findText(institution)
                if index >= 0:
                    self.institution_combo.setCurrentIndex(index)
                    
        except Exception as e:
            print(f"خطأ في تعبئة البيانات الأساسية: {str(e)}")
    
    def fill_sections_data(self, records):
        """تعبئة بيانات الأقسام المتعددة"""
        try:
            # مسح البيانات الحالية
            self.selected_sections = []
            
            # تعبئة بيانات كل قسم
            for record in records:
                section_data = {
                    'section': record[8] or "",  # القسم
                    'registration_amount': record[10] or 0,  # مبلغ التسجيل
                    'monthly_duty': record[11] or 0,  # الواجب الشهري
                    'record_id': record[0]  # معرف السجل
                }
                self.selected_sections.append(section_data)
            
            # تحديث عرض الأقسام المختارة
            self.update_selected_sections_display()
            
        except Exception as e:
            print(f"خطأ في تعبئة بيانات الأقسام: {str(e)}")
    
    def update_selected_sections_display(self):
        """تحديث عرض الأقسام المختارة في الجدول"""
        try:
            if hasattr(self, 'summary_sections_table'):
                # مسح الجدول
                self.summary_sections_table.setRowCount(0)

                # إضافة الأقسام
                for i, section_data in enumerate(self.selected_sections):
                    self.summary_sections_table.insertRow(i)
                    self.summary_sections_table.setItem(i, 0, QTableWidgetItem(section_data['section']))
                    self.summary_sections_table.setItem(i, 1, QTableWidgetItem(str(section_data['registration_amount'])))
                    self.summary_sections_table.setItem(i, 2, QTableWidgetItem(str(section_data['monthly_duty'])))

        except Exception as e:
            print(f"خطأ في تحديث عرض الأقسام: {str(e)}")

    def load_multiple_sections_data(self, student_sections, creation_date):
        """تحميل بيانات الأقسام المتعددة للتلميذ في وضع التعديل"""
        try:
            print(f"✅ تحميل {len(student_sections)} قسم للتلميذ بتاريخ الإنشاء: {creation_date}")

            if not student_sections:
                print("⚠️ لا توجد أقسام لتحميلها")
                return

            # استخدام أول سجل للبيانات الأساسية
            first_record = student_sections[0]

            # تحميل البيانات الأساسية من أول سجل
            self.student_name_input.setText(first_record[5] or "")  # اسم التلميذ
            self.student_code_input.setText(first_record[6] or "")  # رمز التلميذ

            # النوع
            if first_record[7]:  # النوع
                gender_index = self.gender_combo.findText(first_record[7])
                if gender_index >= 0:
                    self.gender_combo.setCurrentIndex(gender_index)

            # أرقام الهواتف
            self.phone_input.setText(first_record[8] or "")  # رقم الهاتف الأول
            self.phone2_input.setText(first_record[9] or "")  # رقم الهاتف الثاني

            # المؤسسة الأصلية
            if first_record[10]:  # المؤسسة الأصلية
                institution_index = self.institution_combo.findText(first_record[10])
                if institution_index >= 0:
                    self.institution_combo.setCurrentIndex(institution_index)

            # تحميل جميع الأقسام في جدول الأقسام المختارة
            self.selected_sections = []

            for record in student_sections:
                section_data = {
                    'id': record[0],  # ID
                    'section': record[1],  # القسم
                    'group_name': record[2] or "",  # اسم المجموعة
                    'registration_fee': record[3] or 0.0,  # مبلغ التسجيل
                    'monthly_fee': record[4] or 0.0,  # الواجب الشهري
                    'creation_date': creation_date,
                    'is_existing': True  # علامة أن هذا قسم موجود (للقراءة فقط)
                }
                self.selected_sections.append(section_data)

            # تحديث عرض الأقسام المختارة
            self.update_selected_sections_for_edit_mode()

            # تعطيل الحقول الأساسية في وضع التعديل
            self.disable_basic_fields_for_edit()

            print(f"✅ تم تحميل جميع الأقسام بنجاح: {len(self.selected_sections)} قسم")

        except Exception as e:
            print(f"❌ خطأ في تحميل الأقسام المتعددة: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات الأقسام: {str(e)}")

    def update_selected_sections_for_edit_mode(self):
        """تحديث عرض الأقسام المختارة في وضع التعديل"""
        try:
            if hasattr(self, 'selected_sections_table'):
                # مسح الجدول الحالي
                self.selected_sections_table.setRowCount(0)

                # إضافة الأقسام الموجودة
                for i, section_data in enumerate(self.selected_sections):
                    self.selected_sections_table.insertRow(i)

                    # المادة (للقراءة فقط)
                    subject_item = QTableWidgetItem(section_data.get('subject', 'غير محدد'))
                    subject_item.setFlags(subject_item.flags() & ~Qt.ItemIsEditable)
                    subject_item.setBackground(QColor("#f0f0f0"))
                    subject_item.setFont(QFont("Calibri", 13, QFont.Bold))
                    self.selected_sections_table.setItem(i, 0, subject_item)

                    # القسم (للقراءة فقط)
                    section_item = QTableWidgetItem(section_data['section'])
                    section_item.setFlags(section_item.flags() & ~Qt.ItemIsEditable)
                    section_item.setBackground(QColor("#f0f0f0"))
                    section_item.setFont(QFont("Calibri", 13, QFont.Bold))
                    self.selected_sections_table.setItem(i, 1, section_item)

                    # الأستاذ (للقراءة فقط)
                    teacher_item = QTableWidgetItem(section_data.get('teacher', 'غير محدد'))
                    teacher_item.setFlags(teacher_item.flags() & ~Qt.ItemIsEditable)
                    teacher_item.setBackground(QColor("#f0f0f0"))
                    teacher_item.setFont(QFont("Calibri", 13, QFont.Bold))
                    self.selected_sections_table.setItem(i, 2, teacher_item)

                    # المجموعة (للقراءة فقط)
                    group_item = QTableWidgetItem(section_data['group_name'])
                    group_item.setFlags(group_item.flags() & ~Qt.ItemIsEditable)
                    group_item.setBackground(QColor("#f0f0f0"))
                    group_item.setFont(QFont("Calibri", 13, QFont.Bold))
                    self.selected_sections_table.setItem(i, 3, group_item)

                    # مبلغ التسجيل (قابل للتعديل)
                    reg_fee_input = QLineEdit(str(section_data['registration_fee']))
                    reg_fee_input.setFont(QFont("Calibri", 13, QFont.Bold))
                    reg_fee_input.setAlignment(Qt.AlignCenter)
                    reg_fee_input.textChanged.connect(lambda text, idx=i: self.update_registration_fee_edit_mode(idx, text))
                    self.selected_sections_table.setCellWidget(i, 4, reg_fee_input)

                    # الواجب الشهري (قابل للتعديل)
                    monthly_fee_input = QLineEdit(str(section_data['monthly_fee']))
                    monthly_fee_input.setFont(QFont("Calibri", 13, QFont.Bold))
                    monthly_fee_input.setAlignment(Qt.AlignCenter)
                    monthly_fee_input.textChanged.connect(lambda text, idx=i: self.update_monthly_fee_edit_mode(idx, text))
                    self.selected_sections_table.setCellWidget(i, 5, monthly_fee_input)

                    # إضافة زر حذف للأقسام الموجودة
                    delete_btn = QPushButton("🗑️ حذف")
                    delete_btn.setStyleSheet("""
                        QPushButton {
                            background-color: #dc3545;
                            color: white;
                            border: none;
                            padding: 5px 10px;
                            border-radius: 3px;
                            font-weight: bold;
                        }
                        QPushButton:hover {
                            background-color: #c82333;
                        }
                    """)
                    delete_btn.clicked.connect(lambda checked, idx=i: self.remove_existing_section(idx))
                    self.selected_sections_table.setCellWidget(i, 6, delete_btn)

                print(f"✅ تم عرض {len(self.selected_sections)} قسم في وضع التعديل")

        except Exception as e:
            print(f"❌ خطأ في تحديث عرض الأقسام للتعديل: {str(e)}")

    def disable_basic_fields_for_edit(self):
        """تفعيل جميع الحقول للتعديل - جميع البيانات قابلة للتعديل"""
        try:
            # تفعيل جميع الحقول الأساسية للتعديل
            self.student_name_input.setReadOnly(False)
            self.student_code_input.setReadOnly(False)  # السماح بتعديل رمز التلميذ
            self.gender_combo.setEnabled(True)
            self.phone_input.setReadOnly(False)
            self.phone2_input.setReadOnly(False)
            self.institution_combo.setEnabled(True)

            # تطبيق نمط للحقول المفعلة
            enabled_style = """
                QLineEdit {
                    background-color: white;
                    color: black;
                    border: 2px solid #3498db;
                    border-radius: 5px;
                    padding: 8px;
                }
                QComboBox {
                    background-color: white;
                    color: black;
                    border: 2px solid #3498db;
                    border-radius: 5px;
                    padding: 8px;
                }
            """
            self.setStyleSheet(self.styleSheet() + enabled_style)

            print("✅ تم تفعيل جميع الحقول للتعديل")

        except Exception as e:
            print(f"❌ خطأ في تفعيل الحقول: {str(e)}")

    def remove_existing_section(self, section_index):
        """حذف قسم موجود"""
        try:
            if 0 <= section_index < len(self.selected_sections):
                section_data = self.selected_sections[section_index]

                # تأكيد الحذف
                reply = QMessageBox.question(
                    self,
                    "تأكيد الحذف",
                    f"هل أنت متأكد من حذف القسم '{section_data['section']}'؟\n"
                    f"هذا الإجراء لا يمكن التراجع عنه.",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    # حذف من قاعدة البيانات
                    self.delete_section_from_database(section_data['id'])

                    # حذف من القائمة
                    del self.selected_sections[section_index]

                    # تحديث العرض
                    self.update_selected_sections_for_edit_mode()

                    QMessageBox.information(self, "تم الحذف", f"تم حذف القسم '{section_data['section']}' بنجاح.")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حذف القسم: {str(e)}")

    def delete_section_from_database(self, section_id):
        """حذف القسم من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("DELETE FROM جدول_البيانات WHERE id = ?", (section_id,))

            conn.commit()
            conn.close()

            print(f"✅ تم حذف القسم من قاعدة البيانات: ID {section_id}")

        except Exception as e:
            print(f"❌ خطأ في حذف القسم من قاعدة البيانات: {str(e)}")
            raise e

    def on_single_section_changed(self, checkbox, state):
        """معالج تغيير اختيار قسم واحد فقط"""
        try:
            row_index = checkbox.property("row_index")
            if row_index is None:
                return
                
            if state == 2:  # Qt.Checked
                # إضافة القسم المحدد
                self.add_selected_section(row_index, checkbox)
            else:  # Qt.Unchecked
                # إزالة القسم المحدد
                self.remove_selected_section_by_checkbox(checkbox)
                
            # تحديث الملخص فقط
            self.update_summary()
            
        except Exception as e:
            print(f"خطأ في معالجة تغيير الاختيار: {e}")

    def add_selected_section(self, row_index, checkbox):
        """إضافة قسم محدد إلى قائمة الأقسام المختارة"""
        try:
            # التأكد من وجود العناصر قبل قراءة النص
            subject_item = self.available_sections_table.item(row_index, 1)
            section_item = self.available_sections_table.item(row_index, 2)
            teacher_item = self.available_sections_table.item(row_index, 3)
            
            if not (subject_item and section_item and teacher_item):
                return
                
            subject = subject_item.text()
            section = section_item.text()
            teacher = teacher_item.text()
            
            # الحصول على البيانات من خصائص الـ checkbox
            teacher_id = checkbox.property("teacher_id")  # المفتاح الأساسي
            group_name = checkbox.property("group_name") or ""

            # التحقق من عدم وجود القسم مسبقاً باستخدام teacher_id
            for existing_section in self.selected_sections:
                if existing_section.get('teacher_id') == teacher_id:
                    return  # القسم موجود مسبقاً

            # إضافة للقائمة
            section_data = {
                'teacher_id': teacher_id,  # المفتاح الأساسي
                'subject': subject,
                'section': section,
                'teacher': teacher,
                'group_name': group_name,
                'registration_fee': 0.0,
                'monthly_fee': 0.0
            }
            self.selected_sections.append(section_data)

            # إضافة للجدول
            table_row_index = self.selected_sections_table.rowCount()
            self.selected_sections_table.insertRow(table_row_index)

            # إضافة البيانات مع تطبيق الخط
            subject_item = QTableWidgetItem(subject)
            subject_item.setFont(QFont("Calibri", 13, QFont.Bold))
            self.selected_sections_table.setItem(table_row_index, 0, subject_item)

            section_item = QTableWidgetItem(section)
            section_item.setFont(QFont("Calibri", 13, QFont.Bold))
            self.selected_sections_table.setItem(table_row_index, 1, section_item)

            teacher_item = QTableWidgetItem(teacher)
            teacher_item.setFont(QFont("Calibri", 13, QFont.Bold))
            self.selected_sections_table.setItem(table_row_index, 2, teacher_item)

            # إضافة عمود المجموعة
            group_item = QTableWidgetItem(group_name or "غير محدد")
            group_item.setFont(QFont("Calibri", 13, QFont.Bold))
            self.selected_sections_table.setItem(table_row_index, 3, group_item)

            # حقل واجبات التسجيل
            registration_fee_input = QLineEdit()
            registration_fee_input.setPlaceholderText("0.00")
            registration_fee_input.setFont(QFont("Calibri", 13, QFont.Bold))
            registration_fee_input.setAlignment(Qt.AlignCenter)
            registration_fee_input.textChanged.connect(lambda text, idx=table_row_index: self.update_registration_fee(idx, text))
            self.selected_sections_table.setCellWidget(table_row_index, 4, registration_fee_input)

            # حقل الواجبات الشهرية
            monthly_fee_input = QLineEdit()
            monthly_fee_input.setPlaceholderText("0.00")
            monthly_fee_input.setFont(QFont("Calibri", 13, QFont.Bold))
            monthly_fee_input.setAlignment(Qt.AlignCenter)
            monthly_fee_input.textChanged.connect(lambda text, idx=table_row_index: self.update_monthly_fee(idx, text))
            self.selected_sections_table.setCellWidget(table_row_index, 5, monthly_fee_input)

            # زر الحذف
            remove_btn = QPushButton("🗑️ حذف")
            remove_btn.setFont(QFont("Calibri", 13, QFont.Bold))
            remove_btn.clicked.connect(lambda: self.remove_selected_section(table_row_index))
            self.selected_sections_table.setCellWidget(table_row_index, 6, remove_btn)

            # تحديث العداد
            self.selected_count_label.setText(f"عدد الأقسام المختارة: {len(self.selected_sections)}")
            
        except Exception as e:
            print(f"خطأ في إضافة القسم المحدد: {e}")

    def remove_selected_section_by_checkbox(self, checkbox):
        """إزالة قسم محدد من قائمة الأقسام المختارة بناءً على الـ checkbox"""
        try:
            # الحصول على teacher_id من الـ checkbox
            teacher_id = checkbox.property("teacher_id")
            if teacher_id is None:
                return

            # البحث عن القسم في قائمة الأقسام المختارة وحذفه باستخدام teacher_id
            for i, selected_section in enumerate(self.selected_sections):
                if selected_section.get('teacher_id') == teacher_id:
                    
                    # إزالة من القائمة
                    self.selected_sections.pop(i)
                    
                    # إزالة من الجدول
                    self.selected_sections_table.removeRow(i)
                    
                    # تحديث العداد
                    self.selected_count_label.setText(f"عدد الأقسام المختارة: {len(self.selected_sections)}")
                    break
                    break
                    
        except Exception as e:
            print(f"خطأ في إزالة القسم المحدد: {e}")

    def update_registration_fee_edit_mode(self, section_index, text):
        """تحديث مبلغ التسجيل في وضع التعديل"""
        try:
            if 0 <= section_index < len(self.selected_sections):
                try:
                    fee = float(text) if text else 0.0
                    self.selected_sections[section_index]['registration_fee'] = fee
                    print(f"✅ تم تحديث مبلغ التسجيل للقسم {section_index}: {fee}")
                except ValueError:
                    print(f"⚠️ قيمة غير صحيحة لمبلغ التسجيل: {text}")
        except Exception as e:
            print(f"❌ خطأ في تحديث مبلغ التسجيل: {str(e)}")

    def update_monthly_fee_edit_mode(self, section_index, text):
        """تحديث الواجب الشهري في وضع التعديل"""
        try:
            if 0 <= section_index < len(self.selected_sections):
                try:
                    fee = float(text) if text else 0.0
                    self.selected_sections[section_index]['monthly_fee'] = fee
                    print(f"✅ تم تحديث الواجب الشهري للقسم {section_index}: {fee}")
                except ValueError:
                    print(f"⚠️ قيمة غير صحيحة للواجب الشهري: {text}")
        except Exception as e:
            print(f"❌ خطأ في تحديث الواجب الشهري: {str(e)}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    window = StudentMultiSectionRegistrationWindow()
    window.show()

    sys.exit(app.exec_())
