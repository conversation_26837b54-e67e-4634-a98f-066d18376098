#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QLabel, QLineEdit, QPushButton, QTableWidget,
    QTableWidgetItem, QFrame, QMessageBox, QComboBox,
    QDateEdit, QFormLayout, QGroupBox, QGridLayout, QHeaderView,
    QTextEdit, QCheckBox, QDialog, QAbstractItemView
)
from PyQt5.QtGui import QFont, QIcon, QColor
from PyQt5.QtCore import Qt, QDate
from datetime import datetime
import json

class StudentMultiSectionRegistrationWindow(QMainWindow):
    """نافذة تسجيل تلميذ واحد في أقسام متعددة"""
    
    def __init__(self, parent=None, db_path="data.db"):
        super().__init__(parent)
        self.db_path = db_path
        self.selected_sections = []  # قائمة الأقسام المختارة
        
        self.setupUI()
        self.setup_database()
        self.load_available_sections()

    def setupUI(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🎓 تسجيل تلميذ في أقسام متعددة")
        self.setFixedSize(1100, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق نمط احترافي للنافذة الرئيسية
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fc,
                    stop: 1 #e9ecef
                );
            }
        """)
        
        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(10)
        
        # العنوان الرئيسي
        title_label = QLabel("🎓 نظام تسجيل تلميذ في أقسام متعددة")
        title_label.setFont(QFont("Calibri", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #3498db,
                    stop: 0.5 #2980b9,
                    stop: 1 #3498db
                );
                color: white;
                padding: 20px;
                border-radius: 15px;
                font-weight: bold;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # إنشاء التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setFont(QFont("Calibri", 15, QFont.Bold))
        
        # تطبيق نمط احترافي للتبويبات
        self.tab_widget.setStyleSheet("""
            QTabWidget {
                background-color: transparent;
                border: none;
            }
            
            QTabWidget::pane {
                border: 3px solid #3498db;
                border-radius: 15px;
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ffffff,
                    stop: 1 #f8f9fa
                );
                padding: 10px;
                margin-top: 5px;
            }
            
            QTabBar::tab {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ecf0f1,
                    stop: 1 #d5dbdb
                );
                color: #2c3e50;
                padding: 15px 25px;
                margin: 2px;
                border-radius: 12px 12px 0px 0px;
                font-family: 'Calibri';
                font-size: 18px;
                font-weight: bold;
                min-width: 140px;
                border: 2px solid #bdc3c7;
                border-bottom: none;
            }
            
            QTabBar::tab:selected {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #3498db,
                    stop: 0.5 #2980b9,
                    stop: 1 #3498db
                );
                color: white;
                border: 2px solid #2980b9;
                border-bottom: none;
                margin-bottom: -2px;
                padding-bottom: 17px;
            }
            
            QTabBar::tab:hover:!selected {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e8f4fd,
                    stop: 1 #d6eaf8
                );
                color: #2980b9;
                border: 2px solid #85c1e9;
                border-bottom: none;
            }
        """)

        # إضافة التبويبات
        self.setup_student_info_tab()
        self.setup_sections_selection_tab()
        self.setup_registration_summary_tab()
        self.setup_actions_tab()
        
        main_layout.addWidget(self.tab_widget)

    def setup_student_info_tab(self):
        """إعداد تبويب معلومات التلميذ"""
        student_tab = QWidget()
        layout = QVBoxLayout(student_tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(20)
        
        # إطار معلومات التلميذ الأساسية
        basic_info_frame = QGroupBox("📝 المعلومات الأساسية للتلميذ")
        basic_info_frame.setFont(QFont("Calibri", 16, QFont.Bold))
        basic_info_frame.setStyleSheet(self.get_groupbox_style())
        
        basic_layout = QFormLayout(basic_info_frame)
        basic_layout.setSpacing(15)
        
        # إنشاء حقول معلومات التلميذ
        self.student_name_input = self.create_styled_input()
        self.student_name_input.setPlaceholderText("أدخل الاسم الكامل للتلميذ")
        
        self.student_code_input = self.create_styled_input()
        self.student_code_input.setPlaceholderText("رمز التلميذ (اختياري)")
        
        # حقل النوع
        self.gender_combo = QComboBox()
        self.gender_combo.addItems(["ذكر", "أنثى"])
        self.gender_combo.setFont(QFont("Calibri", 14, QFont.Bold))
        self.gender_combo.setMinimumHeight(40)
        
        # تاريخ الميلاد
        self.birth_date = QDateEdit()
        self.birth_date.setDate(QDate.currentDate().addYears(-10))
        self.birth_date.setFont(QFont("Calibri", 14, QFont.Bold))
        self.birth_date.setMinimumHeight(40)
        self.birth_date.setCalendarPopup(True)
        
        # إضافة الحقول إلى النموذج
        basic_layout.addRow(self.create_styled_label("🧑‍🎓 اسم التلميذ:"), self.student_name_input)
        basic_layout.addRow(self.create_styled_label("🔢 رمز التلميذ:"), self.student_code_input)
        basic_layout.addRow(self.create_styled_label("👤 النوع:"), self.gender_combo)
        basic_layout.addRow(self.create_styled_label("📅 تاريخ الميلاد:"), self.birth_date)
        
        layout.addWidget(basic_info_frame)
        
        # إطار معلومات الاتصال
        contact_frame = QGroupBox("📞 معلومات الاتصال")
        contact_frame.setFont(QFont("Calibri", 16, QFont.Bold))
        contact_frame.setStyleSheet(self.get_groupbox_style())
        
        contact_layout = QFormLayout(contact_frame)
        contact_layout.setSpacing(15)
        
        self.phone_input = self.create_styled_input()
        self.phone_input.setPlaceholderText("رقم الهاتف الأساسي")
        
        self.phone2_input = self.create_styled_input()
        self.phone2_input.setPlaceholderText("رقم هاتف إضافي (اختياري)")
        
        self.address_input = QTextEdit()
        self.address_input.setMinimumHeight(80)
        self.address_input.setMaximumHeight(120)
        self.address_input.setFont(QFont("Calibri", 14, QFont.Bold))
        self.address_input.setPlaceholderText("العنوان أو ملاحظات إضافية...")
        
        contact_layout.addRow(self.create_styled_label("📱 رقم الهاتف الأول:"), self.phone_input)
        contact_layout.addRow(self.create_styled_label("📞 رقم الهاتف الثاني:"), self.phone2_input)
        contact_layout.addRow(self.create_styled_label("🏠 العنوان/الملاحظات:"), self.address_input)
        
        layout.addWidget(contact_frame)
        
        layout.addStretch()
        
        self.tab_widget.addTab(student_tab, "معلومات التلميذ")

    def setup_sections_selection_tab(self):
        """إعداد تبويب اختيار الأقسام"""
        sections_tab = QWidget()
        layout = QVBoxLayout(sections_tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(20)
        
        # إطار الأقسام المتاحة
        available_sections_frame = QGroupBox("📚 الأقسام المتاحة")
        available_sections_frame.setFont(QFont("Calibri", 16, QFont.Bold))
        available_sections_frame.setStyleSheet(self.get_groupbox_style())
        
        available_layout = QVBoxLayout(available_sections_frame)
        available_layout.setSpacing(15)
        
        # جدول الأقسام المتاحة
        self.available_sections_table = QTableWidget()
        self.available_sections_table.setColumnCount(4)
        self.available_sections_table.setHorizontalHeaderLabels(['اختيار', 'المادة', 'القسم', 'الأستاذ'])
        self.available_sections_table.setFont(QFont("Calibri", 12))
        self.available_sections_table.setAlternatingRowColors(True)
        self.available_sections_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.available_sections_table.horizontalHeader().setStretchLastSection(True)
        self.available_sections_table.verticalHeader().setVisible(False)
        self.available_sections_table.setMinimumHeight(300)
        
        # تعيين عرض الأعمدة
        header = self.available_sections_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # عمود الاختيار
        header.resizeSection(0, 80)
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # المادة
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # القسم
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # الأستاذ
        
        available_layout.addWidget(self.available_sections_table)
        
        # أزرار التحكم في الاختيار
        selection_buttons_layout = QHBoxLayout()
        
        select_all_btn = self.create_styled_button("✅ اختيار الكل", "#4CAF50")
        select_all_btn.clicked.connect(self.select_all_sections)
        
        deselect_all_btn = self.create_styled_button("❌ إلغاء الكل", "#F44336")
        deselect_all_btn.clicked.connect(self.deselect_all_sections)
        
        refresh_btn = self.create_styled_button("🔄 تحديث القائمة", "#2196F3")
        refresh_btn.clicked.connect(self.load_available_sections)
        
        selection_buttons_layout.addWidget(select_all_btn)
        selection_buttons_layout.addWidget(deselect_all_btn)
        selection_buttons_layout.addWidget(refresh_btn)
        selection_buttons_layout.addStretch()
        
        available_layout.addLayout(selection_buttons_layout)
        
        layout.addWidget(available_sections_frame)
        
        # إطار الأقسام المختارة
        selected_sections_frame = QGroupBox("✅ الأقسام المختارة للتسجيل")
        selected_sections_frame.setFont(QFont("Calibri", 16, QFont.Bold))
        selected_sections_frame.setStyleSheet(self.get_groupbox_style())

        selected_layout = QVBoxLayout(selected_sections_frame)
        selected_layout.setSpacing(15)

        # جدول الأقسام المختارة
        self.selected_sections_table = QTableWidget()
        self.selected_sections_table.setColumnCount(4)
        self.selected_sections_table.setHorizontalHeaderLabels(['المادة', 'القسم', 'الأستاذ', 'إجراء'])
        self.selected_sections_table.setFont(QFont("Calibri", 12))
        self.selected_sections_table.setAlternatingRowColors(True)
        self.selected_sections_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.selected_sections_table.horizontalHeader().setStretchLastSection(True)
        self.selected_sections_table.verticalHeader().setVisible(False)
        self.selected_sections_table.setMinimumHeight(200)

        # تعيين عرض الأعمدة
        header2 = self.selected_sections_table.horizontalHeader()
        header2.setSectionResizeMode(0, QHeaderView.Stretch)  # المادة
        header2.setSectionResizeMode(1, QHeaderView.Stretch)  # القسم
        header2.setSectionResizeMode(2, QHeaderView.Stretch)  # الأستاذ
        header2.setSectionResizeMode(3, QHeaderView.Fixed)    # إجراء
        header2.resizeSection(3, 100)

        selected_layout.addWidget(self.selected_sections_table)

        # معلومات إحصائية
        stats_layout = QHBoxLayout()

        self.selected_count_label = QLabel("عدد الأقسام المختارة: 0")
        self.selected_count_label.setFont(QFont("Calibri", 14, QFont.Bold))
        self.selected_count_label.setStyleSheet("color: #2196F3; padding: 10px;")

        stats_layout.addWidget(self.selected_count_label)
        stats_layout.addStretch()

        selected_layout.addLayout(stats_layout)

        layout.addWidget(selected_sections_frame)

        layout.addStretch()

        self.tab_widget.addTab(sections_tab, "اختيار الأقسام")

    def setup_registration_summary_tab(self):
        """إعداد تبويب ملخص التسجيل"""
        summary_tab = QWidget()
        layout = QVBoxLayout(summary_tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(20)

        # إطار ملخص بيانات التلميذ
        student_summary_frame = QGroupBox("📋 ملخص بيانات التلميذ")
        student_summary_frame.setFont(QFont("Calibri", 16, QFont.Bold))
        student_summary_frame.setStyleSheet(self.get_groupbox_style())

        student_summary_layout = QFormLayout(student_summary_frame)
        student_summary_layout.setSpacing(10)

        # عناصر عرض ملخص البيانات
        self.summary_name_label = QLabel("غير محدد")
        self.summary_name_label.setFont(QFont("Calibri", 14, QFont.Bold))
        self.summary_name_label.setStyleSheet("color: #2c3e50; background: #ecf0f1; padding: 8px; border-radius: 5px;")

        self.summary_code_label = QLabel("غير محدد")
        self.summary_code_label.setFont(QFont("Calibri", 14, QFont.Bold))
        self.summary_code_label.setStyleSheet("color: #2c3e50; background: #ecf0f1; padding: 8px; border-radius: 5px;")

        self.summary_gender_label = QLabel("غير محدد")
        self.summary_gender_label.setFont(QFont("Calibri", 14, QFont.Bold))
        self.summary_gender_label.setStyleSheet("color: #2c3e50; background: #ecf0f1; padding: 8px; border-radius: 5px;")

        self.summary_phone_label = QLabel("غير محدد")
        self.summary_phone_label.setFont(QFont("Calibri", 14, QFont.Bold))
        self.summary_phone_label.setStyleSheet("color: #2c3e50; background: #ecf0f1; padding: 8px; border-radius: 5px;")

        student_summary_layout.addRow(self.create_styled_label("الاسم:"), self.summary_name_label)
        student_summary_layout.addRow(self.create_styled_label("الرمز:"), self.summary_code_label)
        student_summary_layout.addRow(self.create_styled_label("النوع:"), self.summary_gender_label)
        student_summary_layout.addRow(self.create_styled_label("الهاتف:"), self.summary_phone_label)

        layout.addWidget(student_summary_frame)

        # إطار ملخص الأقسام المختارة
        sections_summary_frame = QGroupBox("📚 ملخص الأقسام المختارة")
        sections_summary_frame.setFont(QFont("Calibri", 16, QFont.Bold))
        sections_summary_frame.setStyleSheet(self.get_groupbox_style())

        sections_summary_layout = QVBoxLayout(sections_summary_frame)
        sections_summary_layout.setSpacing(15)

        # جدول ملخص الأقسام
        self.summary_sections_table = QTableWidget()
        self.summary_sections_table.setColumnCount(3)
        self.summary_sections_table.setHorizontalHeaderLabels(['المادة', 'القسم', 'الأستاذ'])
        self.summary_sections_table.setFont(QFont("Calibri", 12))
        self.summary_sections_table.setAlternatingRowColors(True)
        self.summary_sections_table.horizontalHeader().setStretchLastSection(True)
        self.summary_sections_table.verticalHeader().setVisible(False)
        self.summary_sections_table.setMinimumHeight(200)
        self.summary_sections_table.setEditTriggers(QAbstractItemView.NoEditTriggers)

        sections_summary_layout.addWidget(self.summary_sections_table)

        # إحصائيات التسجيل
        stats_frame = QGroupBox("📊 إحصائيات التسجيل")
        stats_frame.setFont(QFont("Calibri", 16, QFont.Bold))
        stats_frame.setStyleSheet(self.get_groupbox_style())

        stats_layout = QFormLayout(stats_frame)
        stats_layout.setSpacing(10)

        self.total_sections_label = QLabel("0")
        self.total_sections_label.setFont(QFont("Calibri", 16, QFont.Bold))
        self.total_sections_label.setStyleSheet("color: #e74c3c; background: #fadbd8; padding: 10px; border-radius: 5px;")

        self.total_subjects_label = QLabel("0")
        self.total_subjects_label.setFont(QFont("Calibri", 16, QFont.Bold))
        self.total_subjects_label.setStyleSheet("color: #27ae60; background: #d5f4e6; padding: 10px; border-radius: 5px;")

        stats_layout.addRow(self.create_styled_label("إجمالي الأقسام:"), self.total_sections_label)
        stats_layout.addRow(self.create_styled_label("عدد المواد:"), self.total_subjects_label)

        sections_summary_layout.addWidget(stats_frame)

        layout.addWidget(sections_summary_frame)

        layout.addStretch()

        self.tab_widget.addTab(summary_tab, "ملخص التسجيل")

    def setup_actions_tab(self):
        """إعداد تبويب الإجراءات"""
        actions_tab = QWidget()
        layout = QVBoxLayout(actions_tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(20)

        # إطار إجراءات التسجيل
        actions_frame = QGroupBox("⚡ إجراءات التسجيل")
        actions_frame.setFont(QFont("Calibri", 16, QFont.Bold))
        actions_frame.setStyleSheet(self.get_groupbox_style())

        actions_layout = QVBoxLayout(actions_frame)
        actions_layout.setSpacing(20)

        # أزرار الإجراءات الرئيسية
        main_buttons_layout = QGridLayout()
        main_buttons_layout.setSpacing(15)

        # زر حفظ التسجيل
        save_registration_btn = self.create_styled_button("💾 حفظ التسجيل", "#4CAF50")
        save_registration_btn.clicked.connect(self.save_registration)
        save_registration_btn.setMinimumHeight(60)

        # زر تحديث الملخص
        update_summary_btn = self.create_styled_button("🔄 تحديث الملخص", "#2196F3")
        update_summary_btn.clicked.connect(self.update_summary)
        update_summary_btn.setMinimumHeight(60)

        # زر مسح جميع البيانات
        clear_all_btn = self.create_styled_button("🗑️ مسح جميع البيانات", "#F44336")
        clear_all_btn.clicked.connect(self.clear_all_data)
        clear_all_btn.setMinimumHeight(60)

        # زر طباعة التسجيل
        print_btn = self.create_styled_button("🖨️ طباعة التسجيل", "#9C27B0")
        print_btn.clicked.connect(self.print_registration)
        print_btn.setMinimumHeight(60)

        main_buttons_layout.addWidget(save_registration_btn, 0, 0)
        main_buttons_layout.addWidget(update_summary_btn, 0, 1)
        main_buttons_layout.addWidget(clear_all_btn, 1, 0)
        main_buttons_layout.addWidget(print_btn, 1, 1)

        actions_layout.addLayout(main_buttons_layout)

        # إطار حالة النظام
        status_frame = QGroupBox("📊 حالة النظام")
        status_frame.setFont(QFont("Calibri", 16, QFont.Bold))
        status_frame.setStyleSheet(self.get_groupbox_style())

        status_layout = QFormLayout(status_frame)
        status_layout.setSpacing(10)

        self.status_label = QLabel("جاهز للتسجيل")
        self.status_label.setFont(QFont("Calibri", 14, QFont.Bold))
        self.status_label.setStyleSheet("color: #27ae60; background: #d5f4e6; padding: 10px; border-radius: 5px;")

        self.last_save_label = QLabel("لم يتم الحفظ بعد")
        self.last_save_label.setFont(QFont("Calibri", 12))
        self.last_save_label.setStyleSheet("color: #7f8c8d; padding: 5px;")

        status_layout.addRow(self.create_styled_label("الحالة:"), self.status_label)
        status_layout.addRow(self.create_styled_label("آخر حفظ:"), self.last_save_label)

        actions_layout.addWidget(status_frame)

        layout.addWidget(actions_frame)

        layout.addStretch()

        self.tab_widget.addTab(actions_tab, "الإجراءات")

    def create_styled_input(self):
        """إنشاء حقل إدخال منسق"""
        input_field = QLineEdit()
        input_field.setFont(QFont("Calibri", 15, QFont.Bold))
        input_field.setMinimumHeight(40)
        input_field.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 8px 12px;
                background-color: white;
                selection-background-color: #3498db;
            }
            QLineEdit:focus {
                border: 2px solid #3498db;
                background-color: #f8f9fa;
            }
        """)
        return input_field

    def create_styled_label(self, text):
        """إنشاء تسمية منسقة"""
        label = QLabel(text)
        label.setFont(QFont("Calibri", 15, QFont.Bold))
        return label

    def create_styled_button(self, text, color="#2196F3"):
        """إنشاء زر منسق بتصميم احترافي"""
        button = QPushButton(text)
        button.setFont(QFont("Calibri", 15, QFont.Bold))
        button.setMinimumHeight(45)
        button.setMinimumWidth(200)
        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color},
                    stop: 1 {self.darken_color(color, 20)}
                );
                color: white;
                border: none;
                border-radius: 12px;
                padding: 12px 20px;
                font-weight: bold;
                text-align: center;
            }}
            QPushButton:hover {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {self.lighten_color(color, 10)},
                    stop: 1 {color}
                );
            }}
            QPushButton:pressed {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {self.darken_color(color, 30)},
                    stop: 1 {self.darken_color(color, 40)}
                );
            }}
        """)
        return button

    def get_groupbox_style(self):
        """الحصول على نمط المجموعات"""
        return """
            QGroupBox {
                color: #1976d2;
                border: 2px solid #1976d2;
                border-radius: 8px;
                padding-top: 20px;
                margin-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 15px 0 15px;
                background-color: white;
                font-size: 18px;
                font-weight: bold;
            }
        """

    def lighten_color(self, color, amount):
        """تفتيح اللون"""
        if color.startswith('#'):
            color = color[1:]
        
        r = min(255, int(color[0:2], 16) + amount)
        g = min(255, int(color[2:4], 16) + amount)
        b = min(255, int(color[4:6], 16) + amount)
        
        return f"#{r:02x}{g:02x}{b:02x}"

    def darken_color(self, color, amount):
        """تغميق اللون"""
        if color.startswith('#'):
            color = color[1:]
        
        r = max(0, int(color[0:2], 16) - amount)
        g = max(0, int(color[2:4], 16) - amount)
        b = max(0, int(color[4:6], 16) - amount)
        
        return f"#{r:02x}{g:02x}{b:02x}"

    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # إنشاء الجدول الموحد لجميع البيانات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS تسجيل_التلاميذ_متعدد_الاقسام (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    
                    -- معلومات التلميذ
                    اسم_التلميذ TEXT NOT NULL,
                    رمز_التلميذ TEXT,
                    النوع TEXT,
                    تاريخ_الميلاد DATE,
                    رقم_الهاتف_الأول TEXT,
                    رقم_الهاتف_الثاني TEXT,
                    العنوان_الملاحظات TEXT,
                    
                    -- معلومات القسم
                    المادة TEXT NOT NULL,
                    القسم TEXT NOT NULL,
                    اسم_الاستاذ TEXT,
                    
                    -- تواريخ النظام
                    تاريخ_التسجيل DATETIME DEFAULT CURRENT_TIMESTAMP,
                    تاريخ_التحديث DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إعداد قاعدة البيانات: {str(e)}")

    def load_available_sections(self):
        """تحميل الأقسام المتاحة من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # تحميل البيانات من جدول المواد والأقسام
            cursor.execute("""
                SELECT DISTINCT المادة, القسم, اسم_الاستاذ
                FROM جدول_المواد_والاقسام
                ORDER BY المادة, القسم
            """)

            sections_data = cursor.fetchall()
            conn.close()

            # مسح الجدول الحالي
            self.available_sections_table.setRowCount(0)

            # إضافة البيانات للجدول
            for row_index, (subject, section, teacher) in enumerate(sections_data):
                self.available_sections_table.insertRow(row_index)

                # عمود الاختيار - checkbox
                checkbox = QCheckBox()
                checkbox.stateChanged.connect(self.on_section_selection_changed)
                self.available_sections_table.setCellWidget(row_index, 0, checkbox)

                # باقي الأعمدة
                self.available_sections_table.setItem(row_index, 1, QTableWidgetItem(subject or ""))
                self.available_sections_table.setItem(row_index, 2, QTableWidgetItem(section or ""))
                self.available_sections_table.setItem(row_index, 3, QTableWidgetItem(teacher or ""))

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل الأقسام المتاحة: {str(e)}")

    def on_section_selection_changed(self):
        """معالج تغيير اختيار الأقسام"""
        self.update_selected_sections()
        self.update_summary()

    def update_selected_sections(self):
        """تحديث جدول الأقسام المختارة"""
        # مسح الجدول الحالي
        self.selected_sections_table.setRowCount(0)
        self.selected_sections = []

        # البحث عن الأقسام المختارة
        for row in range(self.available_sections_table.rowCount()):
            checkbox = self.available_sections_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                subject = self.available_sections_table.item(row, 1).text()
                section = self.available_sections_table.item(row, 2).text()
                teacher = self.available_sections_table.item(row, 3).text()

                # إضافة للقائمة
                section_data = {
                    'subject': subject,
                    'section': section,
                    'teacher': teacher
                }
                self.selected_sections.append(section_data)

                # إضافة للجدول
                row_index = self.selected_sections_table.rowCount()
                self.selected_sections_table.insertRow(row_index)

                self.selected_sections_table.setItem(row_index, 0, QTableWidgetItem(subject))
                self.selected_sections_table.setItem(row_index, 1, QTableWidgetItem(section))
                self.selected_sections_table.setItem(row_index, 2, QTableWidgetItem(teacher))

                # زر الحذف
                remove_btn = QPushButton("🗑️ حذف")
                remove_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #e74c3c;
                        color: white;
                        border: none;
                        border-radius: 5px;
                        padding: 5px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #c0392b;
                    }
                """)
                remove_btn.clicked.connect(lambda checked, r=row: self.remove_selected_section(r))
                self.selected_sections_table.setCellWidget(row_index, 3, remove_btn)

        # تحديث العداد
        self.selected_count_label.setText(f"عدد الأقسام المختارة: {len(self.selected_sections)}")

    def remove_selected_section(self, row):
        """حذف قسم من الأقسام المختارة"""
        if row < len(self.selected_sections):
            # الحصول على بيانات القسم المحذوف
            removed_section = self.selected_sections[row]

            # البحث عن القسم في الجدول المتاح وإلغاء تحديده
            for available_row in range(self.available_sections_table.rowCount()):
                subject = self.available_sections_table.item(available_row, 1).text()
                section = self.available_sections_table.item(available_row, 2).text()

                if (subject == removed_section['subject'] and
                    section == removed_section['section']):
                    checkbox = self.available_sections_table.cellWidget(available_row, 0)
                    if checkbox:
                        checkbox.setChecked(False)
                    break

            # تحديث الجداول
            self.update_selected_sections()
            self.update_summary()

    def select_all_sections(self):
        """اختيار جميع الأقسام"""
        for row in range(self.available_sections_table.rowCount()):
            checkbox = self.available_sections_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(True)

    def deselect_all_sections(self):
        """إلغاء اختيار جميع الأقسام"""
        for row in range(self.available_sections_table.rowCount()):
            checkbox = self.available_sections_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(False)

    def update_summary(self):
        """تحديث ملخص التسجيل"""
        # تحديث معلومات التلميذ
        self.summary_name_label.setText(self.student_name_input.text() or "غير محدد")
        self.summary_code_label.setText(self.student_code_input.text() or "غير محدد")
        self.summary_gender_label.setText(self.gender_combo.currentText())
        self.summary_phone_label.setText(self.phone_input.text() or "غير محدد")

        # تحديث جدول الأقسام في الملخص
        self.summary_sections_table.setRowCount(0)

        for row_index, section in enumerate(self.selected_sections):
            self.summary_sections_table.insertRow(row_index)
            self.summary_sections_table.setItem(row_index, 0, QTableWidgetItem(section['subject']))
            self.summary_sections_table.setItem(row_index, 1, QTableWidgetItem(section['section']))
            self.summary_sections_table.setItem(row_index, 2, QTableWidgetItem(section['teacher']))

        # تحديث الإحصائيات
        total_sections = len(self.selected_sections)
        unique_subjects = len(set(section['subject'] for section in self.selected_sections))

        self.total_sections_label.setText(str(total_sections))
        self.total_subjects_label.setText(str(unique_subjects))

        # تحديث حالة النظام
        if total_sections > 0 and self.student_name_input.text().strip():
            self.status_label.setText("جاهز للحفظ")
            self.status_label.setStyleSheet("color: #27ae60; background: #d5f4e6; padding: 10px; border-radius: 5px;")
        else:
            self.status_label.setText("يرجى إكمال البيانات")
            self.status_label.setStyleSheet("color: #e74c3c; background: #fadbd8; padding: 10px; border-radius: 5px;")

    def save_registration(self):
        """حفظ تسجيل التلميذ"""
        # التحقق من صحة البيانات
        if not self.student_name_input.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم التلميذ.")
            return

        if not self.selected_sections:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار قسم واحد على الأقل.")
            return

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # حفظ بيانات التلميذ لكل قسم مختار
            for section in self.selected_sections:
                cursor.execute('''
                    INSERT INTO تسجيل_التلاميذ_متعدد_الاقسام (
                        اسم_التلميذ, رمز_التلميذ, النوع, تاريخ_الميلاد,
                        رقم_الهاتف_الأول, رقم_الهاتف_الثاني, العنوان_الملاحظات,
                        المادة, القسم, اسم_الاستاذ, تاريخ_التسجيل
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (
                    self.student_name_input.text().strip(),
                    self.student_code_input.text().strip(),
                    self.gender_combo.currentText(),
                    self.birth_date.date().toString("yyyy-MM-dd"),
                    self.phone_input.text().strip(),
                    self.phone2_input.text().strip(),
                    self.address_input.toPlainText().strip(),
                    section['subject'],
                    section['section'],
                    section['teacher']
                ))

            conn.commit()
            conn.close()

            # تحديث حالة النظام
            self.status_label.setText("تم الحفظ بنجاح")
            self.status_label.setStyleSheet("color: #27ae60; background: #d5f4e6; padding: 10px; border-radius: 5px;")

            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.last_save_label.setText(f"آخر حفظ: {current_time}")

            QMessageBox.information(self, "نجح",
                f"تم حفظ تسجيل التلميذ '{self.student_name_input.text()}' في {len(self.selected_sections)} قسم بنجاح.")

            # مسح البيانات للتسجيل التالي
            if QMessageBox.question(self, "تأكيد", "هل تريد مسح البيانات لتسجيل تلميذ جديد؟") == QMessageBox.Yes:
                self.clear_all_data()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ التسجيل: {str(e)}")

    def clear_all_data(self):
        """مسح جميع البيانات"""
        reply = QMessageBox.question(
            self, "تأكيد",
            "هل أنت متأكد من مسح جميع البيانات؟\nهذا الإجراء لا يمكن التراجع عنه.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        if reply != QMessageBox.Yes:
            return

        # مسح معلومات التلميذ
        self.student_name_input.clear()
        self.student_code_input.clear()
        self.gender_combo.setCurrentIndex(0)
        self.birth_date.setDate(QDate.currentDate().addYears(-10))
        self.phone_input.clear()
        self.phone2_input.clear()
        self.address_input.clear()

        # إلغاء اختيار جميع الأقسام
        self.deselect_all_sections()

        # تحديث الملخص
        self.update_summary()

        # تحديث حالة النظام
        self.status_label.setText("تم مسح البيانات")
        self.status_label.setStyleSheet("color: #f39c12; background: #fef9e7; padding: 10px; border-radius: 5px;")

    def print_registration(self):
        """طباعة تسجيل التلميذ"""
        if not self.student_name_input.text().strip() or not self.selected_sections:
            QMessageBox.warning(self, "تحذير", "لا توجد بيانات للطباعة.")
            return

        # إنشاء تقرير بسيط
        report = f"""
تقرير تسجيل التلميذ
==================

معلومات التلميذ:
- الاسم: {self.student_name_input.text()}
- الرمز: {self.student_code_input.text() or 'غير محدد'}
- النوع: {self.gender_combo.currentText()}
- تاريخ الميلاد: {self.birth_date.date().toString("yyyy-MM-dd")}
- الهاتف: {self.phone_input.text() or 'غير محدد'}

الأقسام المسجلة:
"""

        for i, section in enumerate(self.selected_sections, 1):
            report += f"{i}. {section['subject']} - {section['section']} - {section['teacher']}\n"

        report += f"\nإجمالي الأقسام: {len(self.selected_sections)}"
        report += f"\nعدد المواد المختلفة: {len(set(section['subject'] for section in self.selected_sections))}"
        report += f"\nتاريخ التسجيل: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        # عرض التقرير في نافذة
        dialog = QDialog(self)
        dialog.setWindowTitle("تقرير التسجيل")
        dialog.setFixedSize(600, 500)
        dialog.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(dialog)

        text_area = QTextEdit()
        text_area.setPlainText(report)
        text_area.setFont(QFont("Calibri", 12))
        text_area.setReadOnly(True)

        layout.addWidget(text_area)

        buttons_layout = QHBoxLayout()

        print_btn = QPushButton("🖨️ طباعة")
        print_btn.clicked.connect(lambda: QMessageBox.information(dialog, "طباعة", "تم إرسال التقرير للطابعة."))

        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(dialog.close)

        buttons_layout.addWidget(print_btn)
        buttons_layout.addWidget(close_btn)

        layout.addLayout(buttons_layout)

        dialog.exec_()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    window = StudentMultiSectionRegistrationWindow()
    window.show()

    sys.exit(app.exec_())
