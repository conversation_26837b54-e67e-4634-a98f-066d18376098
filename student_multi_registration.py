#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QLabel, QLineEdit, QPushButton, QTableWidget,
    QTableWidgetItem, QFrame, QMessageBox, QComboBox,
    QDateEdit, QFormLayout, QGroupBox, QGridLayout, QHeaderView,
    QTextEdit, QCheckBox, QDialog, QAbstractItemView
)
from PyQt5.QtGui import QFont, QIcon, QColor
from PyQt5.QtCore import Qt, QDate
from datetime import datetime
import json

class StudentMultiSectionRegistrationWindow(QMainWindow):
    """نافذة تسجيل تلميذ واحد في أقسام متعددة"""
    
    def __init__(self, parent=None, db_path="data.db"):
        super().__init__(parent)
        self.db_path = db_path
        self.selected_sections = []  # قائمة الأقسام المختارة
        
        self.setupUI()
        self.setup_database()
        self.load_available_sections()

    def setupUI(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🎓 تسجيل تلميذ في أقسام متعددة")
        self.setFixedSize(1100, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق نمط احترافي للنافذة الرئيسية
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fc,
                    stop: 1 #e9ecef
                );
            }
        """)
        
        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(10)
        
        # العنوان الرئيسي
        title_label = QLabel("🎓 نظام تسجيل تلميذ في أقسام متعددة")
        title_label.setFont(QFont("Calibri", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #3498db,
                    stop: 0.5 #2980b9,
                    stop: 1 #3498db
                );
                color: white;
                padding: 20px;
                border-radius: 15px;
                font-weight: bold;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # إنشاء التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setFont(QFont("Calibri", 15, QFont.Bold))
        
        # تطبيق نمط احترافي للتبويبات
        self.tab_widget.setStyleSheet("""
            QTabWidget {
                background-color: transparent;
                border: none;
            }
            
            QTabWidget::pane {
                border: 3px solid #3498db;
                border-radius: 15px;
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ffffff,
                    stop: 1 #f8f9fa
                );
                padding: 10px;
                margin-top: 5px;
            }
            
            QTabBar::tab {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ecf0f1,
                    stop: 1 #d5dbdb
                );
                color: #2c3e50;
                padding: 15px 25px;
                margin: 2px;
                border-radius: 12px 12px 0px 0px;
                font-family: 'Calibri';
                font-size: 18px;
                font-weight: bold;
                min-width: 140px;
                border: 2px solid #bdc3c7;
                border-bottom: none;
            }
            
            QTabBar::tab:selected {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #3498db,
                    stop: 0.5 #2980b9,
                    stop: 1 #3498db
                );
                color: white;
                border: 2px solid #2980b9;
                border-bottom: none;
                margin-bottom: -2px;
                padding-bottom: 17px;
            }
            
            QTabBar::tab:hover:!selected {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e8f4fd,
                    stop: 1 #d6eaf8
                );
                color: #2980b9;
                border: 2px solid #85c1e9;
                border-bottom: none;
            }
        """)

        # إضافة التبويبات
        self.setup_student_info_tab()
        self.setup_sections_selection_tab()
        self.setup_registration_summary_tab()
        self.setup_actions_tab()
        
        main_layout.addWidget(self.tab_widget)

    def setup_student_info_tab(self):
        """إعداد تبويب معلومات التلميذ"""
        student_tab = QWidget()
        layout = QVBoxLayout(student_tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(20)
        
        # إطار معلومات التلميذ الأساسية
        basic_info_frame = QGroupBox("📝 المعلومات الأساسية للتلميذ")
        basic_info_frame.setFont(QFont("Calibri", 16, QFont.Bold))
        basic_info_frame.setStyleSheet(self.get_groupbox_style())
        
        basic_layout = QFormLayout(basic_info_frame)
        basic_layout.setSpacing(15)
        
        # إنشاء حقول معلومات التلميذ
        self.student_name_input = self.create_styled_input()
        self.student_name_input.setPlaceholderText("أدخل الاسم الكامل للتلميذ")
        self.student_name_input.textChanged.connect(self.auto_generate_student_code)

        # حقل رمز التلميذ - يتم إنشاؤه تلقائياً
        self.student_code_input = self.create_styled_input()
        self.student_code_input.setPlaceholderText("سيتم إنشاؤه تلقائياً")
        self.student_code_input.setReadOnly(True)
        self.student_code_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #95a5a6;
                border-radius: 8px;
                padding: 8px 12px;
                background-color: #ecf0f1;
                color: #2c3e50;
                font-weight: bold;
            }
        """)

        # حقل النوع
        self.gender_combo = QComboBox()
        self.gender_combo.addItems(["ذكر", "أنثى"])
        self.gender_combo.setFont(QFont("Calibri", 14, QFont.Bold))
        self.gender_combo.setMinimumHeight(40)

        # حقل المؤسسة الأصلية
        institution_layout = QHBoxLayout()
        self.institution_combo = QComboBox()
        self.institution_combo.setEditable(True)
        self.institution_combo.addItems([
            "الثانوية الاعدادية آسية الوديع",
            "الثانوية الاعدادية أم البنين",
            "الثانوية الاعدادية أنوال",
            "الثانوية الاعدادية إدريس الثاني",
            "الثانوية الاعدادية ابن الهيثم",
            "الثانوية الاعدادية ابن رشد",
            "الثانوية الاعدادية ابن طفيل",
            "الثانوية الاعدادية أبو القاسم الشابي",
            "الثانوية الاعدادية أطلس",
            "الثانوية الاعدادية البحر",
            "الثانوية الاعدادية بدر",
            "الثانوية الاعدادية جابر بن حيان",
            "الثانوية الاعدادية خليج طنجة",
            "الثانوية الاعدادية الزهراء",
            "الثانوية الاعدادية الساحل الشمالي",
            "الثانوية الاعدادية سيدي اليماني",
            "الثانوية الاعدادية عبد الله ابن ياسين",
            "الثانوية الاعدادية عبد الله كنون",
            "الثانوية الاعدادية عبد العزيز مزيان بلفقيه",
            "الثانوية الاعدادية عمر بن عبد العزيز",
            "الثانوية الاعدادية فاطمة المرابط",
            "الثانوية الاعدادية القصبة",
            "الثانوية الاعدادية محمد الخامس",
            "الثانوية الاعدادية محمد السادس",
            "الثانوية الاعدادية محمد بن الحسن الوزاني",
            "الثانوية الاعدادية ماء العيني",
            "الثانوية الاعدادية مولاي عبد الرحمن",
            "الثانوية الاعدادية نهضة",
            "الثانوية الاعدادية طارق بن زياد",
            "الثانوية الاعدادية تورية الشاوي",
            "الثانوية التأهيلية الجامعي",
            "الثانوية التأهيلية الحنصالي",
            "الثانوية التأهيلية الخوارزمي",
            "الثانوية التأهيلية دار الشاوي",
            "الثانوية التأهيلية عبد الله الشفشاوني",
            "الثانوية التأهيلية علال الفاسي",
            "الثانوية التأهيلية محمد الفقيه الركراكي",
            "الثانوية التأهيلية محمد علي الصقلي",
            "الثانوية التأهيلية مولاي سليمان",
            "الثانوية التأهيلية مولاي علي الشرقي",
            "الثانوية التأهيلية أنوال",
            "الثانوية التأهيلية الكركرات",
            "ثانوية الملك فهد ابن عبد العزيز الاعدادية",
            "ثانوية الملك فهد ابن عبد العزيز التأهيلية"
        ])
        self.institution_combo.setFont(QFont("Calibri", 14, QFont.Bold))
        self.institution_combo.lineEdit().setFont(QFont("Calibri", 14, QFont.Bold))
        self.institution_combo.setMinimumHeight(40)

        add_institution_btn = self.create_styled_button("➕ إضافة مؤسسة", "#FF9800")
        add_institution_btn.clicked.connect(self.add_new_institution)
        add_institution_btn.setMinimumHeight(40)
        add_institution_btn.setMaximumWidth(150)

        institution_layout.addWidget(self.institution_combo, 3)
        institution_layout.addWidget(add_institution_btn, 1)

        # إضافة الحقول إلى النموذج
        basic_layout.addRow(self.create_styled_label("🧑‍🎓 اسم التلميذ:"), self.student_name_input)
        basic_layout.addRow(self.create_styled_label("🔢 رمز التلميذ:"), self.student_code_input)
        basic_layout.addRow(self.create_styled_label("👤 النوع:"), self.gender_combo)
        basic_layout.addRow(self.create_styled_label("🏫 المؤسسة الأصلية:"), institution_layout)
        
        layout.addWidget(basic_info_frame)
        
        # إطار معلومات الاتصال
        contact_frame = QGroupBox("📞 معلومات الاتصال")
        contact_frame.setFont(QFont("Calibri", 16, QFont.Bold))
        contact_frame.setStyleSheet(self.get_groupbox_style())
        
        contact_layout = QFormLayout(contact_frame)
        contact_layout.setSpacing(15)
        
        self.phone_input = self.create_styled_input()
        self.phone_input.setPlaceholderText("رقم الهاتف الأساسي")
        
        self.phone2_input = self.create_styled_input()
        self.phone2_input.setPlaceholderText("رقم هاتف إضافي (اختياري)")
        
        self.address_input = QTextEdit()
        self.address_input.setMinimumHeight(80)
        self.address_input.setMaximumHeight(120)
        self.address_input.setFont(QFont("Calibri", 14, QFont.Bold))
        self.address_input.setPlaceholderText("العنوان أو ملاحظات إضافية...")
        
        contact_layout.addRow(self.create_styled_label("📱 رقم الهاتف الأول:"), self.phone_input)
        contact_layout.addRow(self.create_styled_label("📞 رقم الهاتف الثاني:"), self.phone2_input)
        contact_layout.addRow(self.create_styled_label("🏠 العنوان/الملاحظات:"), self.address_input)
        
        layout.addWidget(contact_frame)
        
        layout.addStretch()
        
        self.tab_widget.addTab(student_tab, "معلومات التلميذ")

    def setup_sections_selection_tab(self):
        """إعداد تبويب اختيار الأقسام"""
        sections_tab = QWidget()
        layout = QVBoxLayout(sections_tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(20)
        
        # إطار الأقسام المتاحة
        available_sections_frame = QGroupBox("📚 الأقسام المتاحة")
        available_sections_frame.setFont(QFont("Calibri", 16, QFont.Bold))
        available_sections_frame.setStyleSheet(self.get_groupbox_style())
        
        available_layout = QVBoxLayout(available_sections_frame)
        available_layout.setSpacing(15)
        
        # جدول الأقسام المتاحة
        self.available_sections_table = QTableWidget()
        self.available_sections_table.setColumnCount(4)
        self.available_sections_table.setHorizontalHeaderLabels(['اختيار', 'المادة', 'القسم', 'الأستاذ'])
        self.available_sections_table.setFont(QFont("Calibri", 12))
        self.available_sections_table.setAlternatingRowColors(True)
        self.available_sections_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.available_sections_table.horizontalHeader().setStretchLastSection(True)
        self.available_sections_table.verticalHeader().setVisible(False)
        self.available_sections_table.setMinimumHeight(300)
        
        # تعيين عرض الأعمدة
        header = self.available_sections_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # عمود الاختيار
        header.resizeSection(0, 80)
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # المادة
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # القسم
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # الأستاذ
        
        available_layout.addWidget(self.available_sections_table)
        
        # أزرار التحكم في الاختيار
        selection_buttons_layout = QHBoxLayout()
        
        select_all_btn = self.create_styled_button("✅ اختيار الكل", "#4CAF50")
        select_all_btn.clicked.connect(self.select_all_sections)
        
        deselect_all_btn = self.create_styled_button("❌ إلغاء الكل", "#F44336")
        deselect_all_btn.clicked.connect(self.deselect_all_sections)
        
        refresh_btn = self.create_styled_button("🔄 تحديث القائمة", "#2196F3")
        refresh_btn.clicked.connect(self.load_available_sections)
        
        selection_buttons_layout.addWidget(select_all_btn)
        selection_buttons_layout.addWidget(deselect_all_btn)
        selection_buttons_layout.addWidget(refresh_btn)
        selection_buttons_layout.addStretch()
        
        available_layout.addLayout(selection_buttons_layout)
        
        layout.addWidget(available_sections_frame)
        
        # إطار الأقسام المختارة
        selected_sections_frame = QGroupBox("✅ الأقسام المختارة للتسجيل")
        selected_sections_frame.setFont(QFont("Calibri", 16, QFont.Bold))
        selected_sections_frame.setStyleSheet(self.get_groupbox_style())

        selected_layout = QVBoxLayout(selected_sections_frame)
        selected_layout.setSpacing(15)

        # جدول الأقسام المختارة
        self.selected_sections_table = QTableWidget()
        self.selected_sections_table.setColumnCount(6)
        self.selected_sections_table.setHorizontalHeaderLabels([
            'المادة', 'القسم', 'الأستاذ', 'واجبات التسجيل', 'الواجبات الشهرية', 'إجراء'
        ])
        self.selected_sections_table.setFont(QFont("Calibri", 12))
        self.selected_sections_table.setAlternatingRowColors(True)
        self.selected_sections_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.selected_sections_table.horizontalHeader().setStretchLastSection(True)
        self.selected_sections_table.verticalHeader().setVisible(False)
        self.selected_sections_table.setMinimumHeight(250)

        # تعيين عرض الأعمدة
        header2 = self.selected_sections_table.horizontalHeader()
        header2.setSectionResizeMode(0, QHeaderView.Stretch)  # المادة
        header2.setSectionResizeMode(1, QHeaderView.Stretch)  # القسم
        header2.setSectionResizeMode(2, QHeaderView.Stretch)  # الأستاذ
        header2.setSectionResizeMode(3, QHeaderView.Fixed)    # واجبات التسجيل
        header2.resizeSection(3, 120)
        header2.setSectionResizeMode(4, QHeaderView.Fixed)    # الواجبات الشهرية
        header2.resizeSection(4, 120)
        header2.setSectionResizeMode(5, QHeaderView.Fixed)    # إجراء
        header2.resizeSection(5, 100)

        selected_layout.addWidget(self.selected_sections_table)

        # معلومات إحصائية
        stats_layout = QHBoxLayout()

        self.selected_count_label = QLabel("عدد الأقسام المختارة: 0")
        self.selected_count_label.setFont(QFont("Calibri", 14, QFont.Bold))
        self.selected_count_label.setStyleSheet("color: #2196F3; padding: 10px;")

        stats_layout.addWidget(self.selected_count_label)
        stats_layout.addStretch()

        selected_layout.addLayout(stats_layout)

        layout.addWidget(selected_sections_frame)

        layout.addStretch()

        self.tab_widget.addTab(sections_tab, "اختيار الأقسام")

    def setup_registration_summary_tab(self):
        """إعداد تبويب ملخص التسجيل"""
        summary_tab = QWidget()
        layout = QVBoxLayout(summary_tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(20)

        # إطار ملخص بيانات التلميذ
        student_summary_frame = QGroupBox("📋 ملخص بيانات التلميذ")
        student_summary_frame.setFont(QFont("Calibri", 16, QFont.Bold))
        student_summary_frame.setStyleSheet(self.get_groupbox_style())

        student_summary_layout = QFormLayout(student_summary_frame)
        student_summary_layout.setSpacing(10)

        # عناصر عرض ملخص البيانات
        self.summary_name_label = QLabel("غير محدد")
        self.summary_name_label.setFont(QFont("Calibri", 14, QFont.Bold))
        self.summary_name_label.setStyleSheet("color: #2c3e50; background: #ecf0f1; padding: 8px; border-radius: 5px;")

        self.summary_code_label = QLabel("غير محدد")
        self.summary_code_label.setFont(QFont("Calibri", 14, QFont.Bold))
        self.summary_code_label.setStyleSheet("color: #2c3e50; background: #ecf0f1; padding: 8px; border-radius: 5px;")

        self.summary_gender_label = QLabel("غير محدد")
        self.summary_gender_label.setFont(QFont("Calibri", 14, QFont.Bold))
        self.summary_gender_label.setStyleSheet("color: #2c3e50; background: #ecf0f1; padding: 8px; border-radius: 5px;")

        self.summary_phone_label = QLabel("غير محدد")
        self.summary_phone_label.setFont(QFont("Calibri", 14, QFont.Bold))
        self.summary_phone_label.setStyleSheet("color: #2c3e50; background: #ecf0f1; padding: 8px; border-radius: 5px;")

        student_summary_layout.addRow(self.create_styled_label("الاسم:"), self.summary_name_label)
        student_summary_layout.addRow(self.create_styled_label("الرمز:"), self.summary_code_label)
        student_summary_layout.addRow(self.create_styled_label("النوع:"), self.summary_gender_label)
        student_summary_layout.addRow(self.create_styled_label("الهاتف:"), self.summary_phone_label)

        layout.addWidget(student_summary_frame)

        # إطار ملخص الأقسام المختارة
        sections_summary_frame = QGroupBox("📚 ملخص الأقسام المختارة")
        sections_summary_frame.setFont(QFont("Calibri", 16, QFont.Bold))
        sections_summary_frame.setStyleSheet(self.get_groupbox_style())

        sections_summary_layout = QVBoxLayout(sections_summary_frame)
        sections_summary_layout.setSpacing(15)

        # جدول ملخص الأقسام
        self.summary_sections_table = QTableWidget()
        self.summary_sections_table.setColumnCount(5)
        self.summary_sections_table.setHorizontalHeaderLabels([
            'المادة', 'القسم', 'الأستاذ', 'واجبات التسجيل', 'الواجبات الشهرية'
        ])
        self.summary_sections_table.setFont(QFont("Calibri", 12))
        self.summary_sections_table.setAlternatingRowColors(True)
        self.summary_sections_table.horizontalHeader().setStretchLastSection(True)
        self.summary_sections_table.verticalHeader().setVisible(False)
        self.summary_sections_table.setMinimumHeight(200)
        self.summary_sections_table.setEditTriggers(QAbstractItemView.NoEditTriggers)

        # تعيين عرض الأعمدة للملخص
        summary_header = self.summary_sections_table.horizontalHeader()
        summary_header.setSectionResizeMode(0, QHeaderView.Stretch)  # المادة
        summary_header.setSectionResizeMode(1, QHeaderView.Stretch)  # القسم
        summary_header.setSectionResizeMode(2, QHeaderView.Stretch)  # الأستاذ
        summary_header.setSectionResizeMode(3, QHeaderView.Fixed)    # واجبات التسجيل
        summary_header.resizeSection(3, 120)
        summary_header.setSectionResizeMode(4, QHeaderView.Fixed)    # الواجبات الشهرية
        summary_header.resizeSection(4, 120)

        sections_summary_layout.addWidget(self.summary_sections_table)

        # إحصائيات التسجيل
        stats_frame = QGroupBox("📊 إحصائيات التسجيل")
        stats_frame.setFont(QFont("Calibri", 16, QFont.Bold))
        stats_frame.setStyleSheet(self.get_groupbox_style())

        stats_layout = QFormLayout(stats_frame)
        stats_layout.setSpacing(10)

        self.total_sections_label = QLabel("0")
        self.total_sections_label.setFont(QFont("Calibri", 16, QFont.Bold))
        self.total_sections_label.setStyleSheet("color: #e74c3c; background: #fadbd8; padding: 10px; border-radius: 5px;")

        self.total_subjects_label = QLabel("0")
        self.total_subjects_label.setFont(QFont("Calibri", 16, QFont.Bold))
        self.total_subjects_label.setStyleSheet("color: #27ae60; background: #d5f4e6; padding: 10px; border-radius: 5px;")

        self.total_registration_fees_label = QLabel("0.00 درهم")
        self.total_registration_fees_label.setFont(QFont("Calibri", 16, QFont.Bold))
        self.total_registration_fees_label.setStyleSheet("color: #e67e22; background: #fdeaa7; padding: 10px; border-radius: 5px;")

        self.total_monthly_fees_label = QLabel("0.00 درهم")
        self.total_monthly_fees_label.setFont(QFont("Calibri", 16, QFont.Bold))
        self.total_monthly_fees_label.setStyleSheet("color: #8e44ad; background: #e8daef; padding: 10px; border-radius: 5px;")

        stats_layout.addRow(self.create_styled_label("إجمالي الأقسام:"), self.total_sections_label)
        stats_layout.addRow(self.create_styled_label("عدد المواد:"), self.total_subjects_label)
        stats_layout.addRow(self.create_styled_label("إجمالي واجبات التسجيل:"), self.total_registration_fees_label)
        stats_layout.addRow(self.create_styled_label("إجمالي الواجبات الشهرية:"), self.total_monthly_fees_label)

        sections_summary_layout.addWidget(stats_frame)

        layout.addWidget(sections_summary_frame)

        layout.addStretch()

        self.tab_widget.addTab(summary_tab, "ملخص التسجيل")

    def setup_actions_tab(self):
        """إعداد تبويب الإجراءات"""
        actions_tab = QWidget()
        layout = QVBoxLayout(actions_tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(20)

        # إطار إجراءات التسجيل
        actions_frame = QGroupBox("⚡ إجراءات التسجيل")
        actions_frame.setFont(QFont("Calibri", 16, QFont.Bold))
        actions_frame.setStyleSheet(self.get_groupbox_style())

        actions_layout = QVBoxLayout(actions_frame)
        actions_layout.setSpacing(20)

        # أزرار الإجراءات الرئيسية
        main_buttons_layout = QGridLayout()
        main_buttons_layout.setSpacing(15)

        # زر حفظ التسجيل
        save_registration_btn = self.create_styled_button("💾 حفظ التسجيل", "#4CAF50")
        save_registration_btn.clicked.connect(self.save_registration)
        save_registration_btn.setMinimumHeight(60)

        # زر تحديث الملخص
        update_summary_btn = self.create_styled_button("🔄 تحديث الملخص", "#2196F3")
        update_summary_btn.clicked.connect(self.update_summary)
        update_summary_btn.setMinimumHeight(60)

        # زر مسح جميع البيانات
        clear_all_btn = self.create_styled_button("🗑️ مسح جميع البيانات", "#F44336")
        clear_all_btn.clicked.connect(self.clear_all_data)
        clear_all_btn.setMinimumHeight(60)

        # زر طباعة التسجيل
        print_btn = self.create_styled_button("🖨️ طباعة التسجيل", "#9C27B0")
        print_btn.clicked.connect(self.print_registration)
        print_btn.setMinimumHeight(60)

        main_buttons_layout.addWidget(save_registration_btn, 0, 0)
        main_buttons_layout.addWidget(update_summary_btn, 0, 1)
        main_buttons_layout.addWidget(clear_all_btn, 1, 0)
        main_buttons_layout.addWidget(print_btn, 1, 1)

        actions_layout.addLayout(main_buttons_layout)

        # إطار حالة النظام
        status_frame = QGroupBox("📊 حالة النظام")
        status_frame.setFont(QFont("Calibri", 16, QFont.Bold))
        status_frame.setStyleSheet(self.get_groupbox_style())

        status_layout = QFormLayout(status_frame)
        status_layout.setSpacing(10)

        self.status_label = QLabel("جاهز للتسجيل")
        self.status_label.setFont(QFont("Calibri", 14, QFont.Bold))
        self.status_label.setStyleSheet("color: #27ae60; background: #d5f4e6; padding: 10px; border-radius: 5px;")

        self.last_save_label = QLabel("لم يتم الحفظ بعد")
        self.last_save_label.setFont(QFont("Calibri", 12))
        self.last_save_label.setStyleSheet("color: #7f8c8d; padding: 5px;")

        status_layout.addRow(self.create_styled_label("الحالة:"), self.status_label)
        status_layout.addRow(self.create_styled_label("آخر حفظ:"), self.last_save_label)

        actions_layout.addWidget(status_frame)

        layout.addWidget(actions_frame)

        layout.addStretch()

        self.tab_widget.addTab(actions_tab, "الإجراءات")

    def create_styled_input(self):
        """إنشاء حقل إدخال منسق"""
        input_field = QLineEdit()
        input_field.setFont(QFont("Calibri", 15, QFont.Bold))
        input_field.setMinimumHeight(40)
        input_field.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 8px 12px;
                background-color: white;
                selection-background-color: #3498db;
            }
            QLineEdit:focus {
                border: 2px solid #3498db;
                background-color: #f8f9fa;
            }
        """)
        return input_field

    def create_styled_label(self, text):
        """إنشاء تسمية منسقة"""
        label = QLabel(text)
        label.setFont(QFont("Calibri", 15, QFont.Bold))
        return label

    def create_styled_button(self, text, color="#2196F3"):
        """إنشاء زر منسق بتصميم احترافي"""
        button = QPushButton(text)
        button.setFont(QFont("Calibri", 15, QFont.Bold))
        button.setMinimumHeight(45)
        button.setMinimumWidth(200)
        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color},
                    stop: 1 {self.darken_color(color, 20)}
                );
                color: white;
                border: none;
                border-radius: 12px;
                padding: 12px 20px;
                font-weight: bold;
                text-align: center;
            }}
            QPushButton:hover {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {self.lighten_color(color, 10)},
                    stop: 1 {color}
                );
            }}
            QPushButton:pressed {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {self.darken_color(color, 30)},
                    stop: 1 {self.darken_color(color, 40)}
                );
            }}
        """)
        return button

    def get_groupbox_style(self):
        """الحصول على نمط المجموعات"""
        return """
            QGroupBox {
                color: #1976d2;
                border: 2px solid #1976d2;
                border-radius: 8px;
                padding-top: 20px;
                margin-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 15px 0 15px;
                background-color: white;
                font-size: 18px;
                font-weight: bold;
            }
        """

    def lighten_color(self, color, amount):
        """تفتيح اللون"""
        if color.startswith('#'):
            color = color[1:]
        
        r = min(255, int(color[0:2], 16) + amount)
        g = min(255, int(color[2:4], 16) + amount)
        b = min(255, int(color[4:6], 16) + amount)
        
        return f"#{r:02x}{g:02x}{b:02x}"

    def darken_color(self, color, amount):
        """تغميق اللون"""
        if color.startswith('#'):
            color = color[1:]
        
        r = max(0, int(color[0:2], 16) - amount)
        g = max(0, int(color[2:4], 16) - amount)
        b = max(0, int(color[4:6], 16) - amount)
        
        return f"#{r:02x}{g:02x}{b:02x}"

    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # إنشاء الجدول الموحد لجميع البيانات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS تسجيل_التلاميذ_متعدد_الاقسام (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,

                    -- معلومات التلميذ
                    اسم_التلميذ TEXT NOT NULL,
                    رمز_التلميذ TEXT,
                    النوع TEXT,
                    المؤسسة_الاصلية TEXT,
                    رقم_الهاتف_الأول TEXT,
                    رقم_الهاتف_الثاني TEXT,
                    العنوان_الملاحظات TEXT,

                    -- معلومات القسم
                    المادة TEXT NOT NULL,
                    القسم TEXT NOT NULL,
                    اسم_الاستاذ TEXT,

                    -- الواجبات المالية
                    واجبات_التسجيل REAL DEFAULT 0.0,
                    الواجبات_الشهرية REAL DEFAULT 0.0,

                    -- تواريخ النظام
                    تاريخ_التسجيل DATETIME DEFAULT CURRENT_TIMESTAMP,
                    تاريخ_التحديث DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إعداد قاعدة البيانات: {str(e)}")

    def generate_next_student_code(self):
        """إنشاء رمز التلميذ التالي تلقائياً"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # البحث عن جميع أرقام التلاميذ الموجودة في عمود رمز_التلميذ
            cursor.execute("""
                SELECT رمز_التلميذ FROM تسجيل_التلاميذ_متعدد_الاقسام
                WHERE رمز_التلميذ IS NOT NULL AND رمز_التلميذ != ''
                ORDER BY رمز_التلميذ
            """)

            results = cursor.fetchall()
            conn.close()

            # استخراج جميع الأرقام الصحيحة
            numbers = []
            for result in results:
                code = result[0].strip()
                # محاولة استخراج الرقم من الرمز
                if code.startswith('R') and len(code) > 1:
                    try:
                        number = int(code[1:])
                        numbers.append(number)
                    except ValueError:
                        continue
                else:
                    # إذا كان الرمز رقم فقط
                    try:
                        number = int(code)
                        numbers.append(number)
                    except ValueError:
                        continue

            if numbers:
                # العثور على أعلى رقم وإضافة 1
                max_number = max(numbers)
                next_number = max_number + 1
                return f"R{next_number}"
            else:
                # لا توجد أرقام صحيحة، ابدأ من R10000
                return "R10000"

        except Exception as e:
            print(f"خطأ في إنشاء رمز التلميذ: {str(e)}")
            return "R10000"

    def auto_generate_student_code(self):
        """إنشاء رمز التلميذ تلقائياً عند إدخال الاسم"""
        if self.student_name_input.text().strip():
            new_code = self.generate_next_student_code()
            self.student_code_input.setText(new_code)
        else:
            self.student_code_input.clear()

    def add_new_institution(self):
        """إضافة مؤسسة جديدة"""
        from PyQt5.QtWidgets import QInputDialog

        text, ok = QInputDialog.getText(
            self,
            'إضافة مؤسسة جديدة',
            'اسم المؤسسة الجديدة:',
            QLineEdit.Normal,
            ''
        )

        if ok and text.strip():
            # التحقق من عدم وجود المؤسسة مسبقاً
            existing_items = [self.institution_combo.itemText(i) for i in range(self.institution_combo.count())]
            if text.strip() not in existing_items:
                self.institution_combo.addItem(text.strip())
                self.institution_combo.setCurrentText(text.strip())
                QMessageBox.information(self, "نجح", f"تم إضافة المؤسسة '{text.strip()}' بنجاح.")
            else:
                QMessageBox.warning(self, "تحذير", "هذه المؤسسة موجودة بالفعل.")

    def update_registration_fee(self, row_index, text):
        """تحديث واجبات التسجيل للقسم"""
        try:
            if row_index < len(self.selected_sections):
                fee = float(text) if text.strip() else 0.0
                self.selected_sections[row_index]['registration_fee'] = fee
                self.update_summary()
        except ValueError:
            pass  # تجاهل القيم غير الصحيحة

    def update_monthly_fee(self, row_index, text):
        """تحديث الواجبات الشهرية للقسم"""
        try:
            if row_index < len(self.selected_sections):
                fee = float(text) if text.strip() else 0.0
                self.selected_sections[row_index]['monthly_fee'] = fee
                self.update_summary()
        except ValueError:
            pass  # تجاهل القيم غير الصحيحة

    def load_available_sections(self):
        """تحميل الأقسام المتاحة من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # تحميل البيانات من جدول المواد والأقسام
            cursor.execute("""
                SELECT DISTINCT المادة, القسم, اسم_الاستاذ
                FROM جدول_المواد_والاقسام
                ORDER BY المادة, القسم
            """)

            sections_data = cursor.fetchall()
            conn.close()

            # مسح الجدول الحالي
            self.available_sections_table.setRowCount(0)

            # إضافة البيانات للجدول
            for row_index, (subject, section, teacher) in enumerate(sections_data):
                self.available_sections_table.insertRow(row_index)

                # عمود الاختيار - checkbox
                checkbox = QCheckBox()
                checkbox.stateChanged.connect(self.on_section_selection_changed)
                self.available_sections_table.setCellWidget(row_index, 0, checkbox)

                # باقي الأعمدة
                self.available_sections_table.setItem(row_index, 1, QTableWidgetItem(subject or ""))
                self.available_sections_table.setItem(row_index, 2, QTableWidgetItem(section or ""))
                self.available_sections_table.setItem(row_index, 3, QTableWidgetItem(teacher or ""))

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل الأقسام المتاحة: {str(e)}")

    def on_section_selection_changed(self):
        """معالج تغيير اختيار الأقسام"""
        self.update_selected_sections()
        self.update_summary()

    def update_selected_sections(self):
        """تحديث جدول الأقسام المختارة"""
        # مسح الجدول الحالي
        self.selected_sections_table.setRowCount(0)
        self.selected_sections = []

        # البحث عن الأقسام المختارة
        for row in range(self.available_sections_table.rowCount()):
            checkbox = self.available_sections_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                subject = self.available_sections_table.item(row, 1).text()
                section = self.available_sections_table.item(row, 2).text()
                teacher = self.available_sections_table.item(row, 3).text()

                # إضافة للقائمة
                section_data = {
                    'subject': subject,
                    'section': section,
                    'teacher': teacher,
                    'registration_fee': 0.0,
                    'monthly_fee': 0.0
                }
                self.selected_sections.append(section_data)

                # إضافة للجدول
                row_index = self.selected_sections_table.rowCount()
                self.selected_sections_table.insertRow(row_index)

                self.selected_sections_table.setItem(row_index, 0, QTableWidgetItem(subject))
                self.selected_sections_table.setItem(row_index, 1, QTableWidgetItem(section))
                self.selected_sections_table.setItem(row_index, 2, QTableWidgetItem(teacher))

                # حقل واجبات التسجيل
                registration_fee_input = QLineEdit()
                registration_fee_input.setPlaceholderText("0.00")
                registration_fee_input.setFont(QFont("Calibri", 12))
                registration_fee_input.setAlignment(Qt.AlignCenter)
                registration_fee_input.setStyleSheet("""
                    QLineEdit {
                        border: 1px solid #bdc3c7;
                        border-radius: 4px;
                        padding: 4px;
                        background-color: white;
                    }
                    QLineEdit:focus {
                        border: 2px solid #3498db;
                    }
                """)
                registration_fee_input.textChanged.connect(lambda text, idx=row_index: self.update_registration_fee(idx, text))
                self.selected_sections_table.setCellWidget(row_index, 3, registration_fee_input)

                # حقل الواجبات الشهرية
                monthly_fee_input = QLineEdit()
                monthly_fee_input.setPlaceholderText("0.00")
                monthly_fee_input.setFont(QFont("Calibri", 12))
                monthly_fee_input.setAlignment(Qt.AlignCenter)
                monthly_fee_input.setStyleSheet("""
                    QLineEdit {
                        border: 1px solid #bdc3c7;
                        border-radius: 4px;
                        padding: 4px;
                        background-color: white;
                    }
                    QLineEdit:focus {
                        border: 2px solid #3498db;
                    }
                """)
                monthly_fee_input.textChanged.connect(lambda text, idx=row_index: self.update_monthly_fee(idx, text))
                self.selected_sections_table.setCellWidget(row_index, 4, monthly_fee_input)

                # زر الحذف
                remove_btn = QPushButton("🗑️ حذف")
                remove_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #e74c3c;
                        color: white;
                        border: none;
                        border-radius: 5px;
                        padding: 5px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #c0392b;
                    }
                """)
                remove_btn.clicked.connect(lambda checked, r=row: self.remove_selected_section(r))
                self.selected_sections_table.setCellWidget(row_index, 5, remove_btn)

        # تحديث العداد
        self.selected_count_label.setText(f"عدد الأقسام المختارة: {len(self.selected_sections)}")

    def remove_selected_section(self, row):
        """حذف قسم من الأقسام المختارة"""
        if row < len(self.selected_sections):
            # الحصول على بيانات القسم المحذوف
            removed_section = self.selected_sections[row]

            # البحث عن القسم في الجدول المتاح وإلغاء تحديده
            for available_row in range(self.available_sections_table.rowCount()):
                subject = self.available_sections_table.item(available_row, 1).text()
                section = self.available_sections_table.item(available_row, 2).text()

                if (subject == removed_section['subject'] and
                    section == removed_section['section']):
                    checkbox = self.available_sections_table.cellWidget(available_row, 0)
                    if checkbox:
                        checkbox.setChecked(False)
                    break

            # تحديث الجداول
            self.update_selected_sections()
            self.update_summary()

    def select_all_sections(self):
        """اختيار جميع الأقسام"""
        for row in range(self.available_sections_table.rowCount()):
            checkbox = self.available_sections_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(True)

    def deselect_all_sections(self):
        """إلغاء اختيار جميع الأقسام"""
        for row in range(self.available_sections_table.rowCount()):
            checkbox = self.available_sections_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(False)

    def update_summary(self):
        """تحديث ملخص التسجيل"""
        # تحديث معلومات التلميذ
        self.summary_name_label.setText(self.student_name_input.text() or "غير محدد")
        self.summary_code_label.setText(self.student_code_input.text() or "غير محدد")
        self.summary_gender_label.setText(self.gender_combo.currentText())
        self.summary_phone_label.setText(self.phone_input.text() or "غير محدد")

        # تحديث جدول الأقسام في الملخص
        self.summary_sections_table.setRowCount(0)

        total_registration_fees = 0.0
        total_monthly_fees = 0.0

        for row_index, section in enumerate(self.selected_sections):
            self.summary_sections_table.insertRow(row_index)
            self.summary_sections_table.setItem(row_index, 0, QTableWidgetItem(section['subject']))
            self.summary_sections_table.setItem(row_index, 1, QTableWidgetItem(section['section']))
            self.summary_sections_table.setItem(row_index, 2, QTableWidgetItem(section['teacher']))

            # عرض الواجبات
            reg_fee = section.get('registration_fee', 0.0)
            monthly_fee = section.get('monthly_fee', 0.0)

            self.summary_sections_table.setItem(row_index, 3, QTableWidgetItem(f"{reg_fee:.2f} درهم"))
            self.summary_sections_table.setItem(row_index, 4, QTableWidgetItem(f"{monthly_fee:.2f} درهم"))

            # حساب الإجماليات
            total_registration_fees += reg_fee
            total_monthly_fees += monthly_fee

        # تحديث الإحصائيات
        total_sections = len(self.selected_sections)
        unique_subjects = len(set(section['subject'] for section in self.selected_sections))

        self.total_sections_label.setText(str(total_sections))
        self.total_subjects_label.setText(str(unique_subjects))
        self.total_registration_fees_label.setText(f"{total_registration_fees:.2f} درهم")
        self.total_monthly_fees_label.setText(f"{total_monthly_fees:.2f} درهم")

        # تحديث حالة النظام
        if total_sections > 0 and self.student_name_input.text().strip():
            self.status_label.setText("جاهز للحفظ")
            self.status_label.setStyleSheet("color: #27ae60; background: #d5f4e6; padding: 10px; border-radius: 5px;")
        else:
            self.status_label.setText("يرجى إكمال البيانات")
            self.status_label.setStyleSheet("color: #e74c3c; background: #fadbd8; padding: 10px; border-radius: 5px;")

    def save_registration(self):
        """حفظ تسجيل التلميذ"""
        # التحقق من صحة البيانات
        if not self.student_name_input.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم التلميذ.")
            return

        if not self.selected_sections:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار قسم واحد على الأقل.")
            return

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # حفظ بيانات التلميذ لكل قسم مختار
            for section in self.selected_sections:
                cursor.execute('''
                    INSERT INTO تسجيل_التلاميذ_متعدد_الاقسام (
                        اسم_التلميذ, رمز_التلميذ, النوع, المؤسسة_الاصلية,
                        رقم_الهاتف_الأول, رقم_الهاتف_الثاني, العنوان_الملاحظات,
                        المادة, القسم, اسم_الاستاذ, واجبات_التسجيل, الواجبات_الشهرية,
                        تاريخ_التسجيل
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (
                    self.student_name_input.text().strip(),
                    self.student_code_input.text().strip(),
                    self.gender_combo.currentText(),
                    self.institution_combo.currentText(),
                    self.phone_input.text().strip(),
                    self.phone2_input.text().strip(),
                    self.address_input.toPlainText().strip(),
                    section['subject'],
                    section['section'],
                    section['teacher'],
                    section.get('registration_fee', 0.0),
                    section.get('monthly_fee', 0.0)
                ))

            conn.commit()
            conn.close()

            # تحديث حالة النظام
            self.status_label.setText("تم الحفظ بنجاح")
            self.status_label.setStyleSheet("color: #27ae60; background: #d5f4e6; padding: 10px; border-radius: 5px;")

            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.last_save_label.setText(f"آخر حفظ: {current_time}")

            QMessageBox.information(self, "نجح",
                f"تم حفظ تسجيل التلميذ '{self.student_name_input.text()}' في {len(self.selected_sections)} قسم بنجاح.")

            # مسح البيانات للتسجيل التالي
            if QMessageBox.question(self, "تأكيد", "هل تريد مسح البيانات لتسجيل تلميذ جديد؟") == QMessageBox.Yes:
                self.clear_all_data()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ التسجيل: {str(e)}")

    def clear_all_data(self):
        """مسح جميع البيانات"""
        reply = QMessageBox.question(
            self, "تأكيد",
            "هل أنت متأكد من مسح جميع البيانات؟\nهذا الإجراء لا يمكن التراجع عنه.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        if reply != QMessageBox.Yes:
            return

        # مسح معلومات التلميذ
        self.student_name_input.clear()
        self.student_code_input.clear()
        self.gender_combo.setCurrentIndex(0)
        self.institution_combo.setCurrentIndex(0)
        self.phone_input.clear()
        self.phone2_input.clear()
        self.address_input.clear()

        # إلغاء اختيار جميع الأقسام
        self.deselect_all_sections()

        # تحديث الملخص
        self.update_summary()

        # تحديث حالة النظام
        self.status_label.setText("تم مسح البيانات")
        self.status_label.setStyleSheet("color: #f39c12; background: #fef9e7; padding: 10px; border-radius: 5px;")

    def print_registration(self):
        """طباعة تسجيل التلميذ"""
        if not self.student_name_input.text().strip() or not self.selected_sections:
            QMessageBox.warning(self, "تحذير", "لا توجد بيانات للطباعة.")
            return

        # إنشاء تقرير بسيط
        total_registration_fees = sum(section.get('registration_fee', 0.0) for section in self.selected_sections)
        total_monthly_fees = sum(section.get('monthly_fee', 0.0) for section in self.selected_sections)

        report = f"""
تقرير تسجيل التلميذ في أقسام متعددة
====================================

معلومات التلميذ:
- الاسم: {self.student_name_input.text()}
- الرمز: {self.student_code_input.text() or 'غير محدد'}
- النوع: {self.gender_combo.currentText()}
- المؤسسة الأصلية: {self.institution_combo.currentText() or 'غير محدد'}
- الهاتف: {self.phone_input.text() or 'غير محدد'}

الأقسام المسجلة:
"""

        for i, section in enumerate(self.selected_sections, 1):
            reg_fee = section.get('registration_fee', 0.0)
            monthly_fee = section.get('monthly_fee', 0.0)
            report += f"{i}. {section['subject']} - {section['section']} - {section['teacher']}\n"
            report += f"   واجبات التسجيل: {reg_fee:.2f} درهم | الواجبات الشهرية: {monthly_fee:.2f} درهم\n\n"

        report += f"الإحصائيات:\n"
        report += f"- إجمالي الأقسام: {len(self.selected_sections)}\n"
        report += f"- عدد المواد المختلفة: {len(set(section['subject'] for section in self.selected_sections))}\n"
        report += f"- إجمالي واجبات التسجيل: {total_registration_fees:.2f} درهم\n"
        report += f"- إجمالي الواجبات الشهرية: {total_monthly_fees:.2f} درهم\n"
        report += f"- المجموع الكلي: {total_registration_fees + total_monthly_fees:.2f} درهم\n\n"
        report += f"تاريخ التسجيل: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        # عرض التقرير في نافذة
        dialog = QDialog(self)
        dialog.setWindowTitle("تقرير التسجيل")
        dialog.setFixedSize(600, 500)
        dialog.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(dialog)

        text_area = QTextEdit()
        text_area.setPlainText(report)
        text_area.setFont(QFont("Calibri", 12))
        text_area.setReadOnly(True)

        layout.addWidget(text_area)

        buttons_layout = QHBoxLayout()

        print_btn = QPushButton("🖨️ طباعة")
        print_btn.clicked.connect(lambda: QMessageBox.information(dialog, "طباعة", "تم إرسال التقرير للطابعة."))

        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(dialog.close)

        buttons_layout.addWidget(print_btn)
        buttons_layout.addWidget(close_btn)

        layout.addLayout(buttons_layout)

        dialog.exec_()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    window = StudentMultiSectionRegistrationWindow()
    window.show()

    sys.exit(app.exec_())
