([('نظام_إدارة_المدرسة.exe',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\build\\main_window_fixed\\نظام_إدارة_المدرسة.exe',
   'EXECUTABLE'),
  ('python313.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\printsupport\\windowsprintersupport.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\printsupport\\windowsprintersupport.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('scipy.libs\\libscipy_openblas-f07f5a5d207a3a47104dca54d6d0c86a.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy.libs\\libscipy_openblas-f07f5a5d207a3a47104dca54d6d0c86a.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('Pythonwin\\win32ui.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32\\win32print.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\win32print.pyd',
   'EXTENSION'),
  ('PIL\\_imagingft.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\_imagingft.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\_imaging.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\_webp.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\_imagingtk.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\_imagingcms.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\_imagingmath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\_philox.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\_common.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_path.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\_path.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\ft2font.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\ft2font.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\backends\\_backend_agg.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\backends\\_backend_agg.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('contourpy\\_contourpy.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\contourpy\\_contourpy.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_image.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\_image.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_tri.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\_tri.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_qhull.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\_qhull.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('kiwisolver\\_cext.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\kiwisolver\\_cext.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_c_internal_utils.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\_c_internal_utils.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\etree.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\etree.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\_elementpath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\sax.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\sax.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\objectify.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\html\\diff.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\builder.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\builder.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('bidi\\bidi.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\bidi\\bidi.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('fontTools\\varLib\\iup.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\fontTools\\varLib\\iup.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('fontTools\\misc\\bezierTools.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\fontTools\\misc\\bezierTools.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtPrintSupport.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\QtPrintSupport.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\sip.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\writers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numexpr\\interpreter.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numexpr\\interpreter.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_ccallback_c.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\_lib\\_ccallback_c.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_tools.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_tools.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_reordering.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_reordering.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_matching.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_matching.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_flow.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_flow.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_min_spanning_tree.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_min_spanning_tree.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_traversal.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_traversal.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_shortest_path.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_shortest_path.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_fblas.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\linalg\\_fblas.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_flapack.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\linalg\\_flapack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_lapack.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\linalg\\cython_lapack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_blas.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\linalg\\cython_blas.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_update.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\linalg\\_decomp_update.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ellip_harm_2.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\special\\_ellip_harm_2.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highspy\\_core.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\optimize\\_highspy\\_core.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highspy\\_highs_options.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\optimize\\_highspy\\_highs_options.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_direct.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\optimize\\_direct.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rgi_cython.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\interpolate\\_rgi_cython.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_interpnd.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\interpolate\\_interpnd.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_bspl.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\interpolate\\_bspl.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rbfinterp_pythran.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\interpolate\\_rbfinterp_pythran.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_ppoly.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\interpolate\\_ppoly.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_dierckx.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\interpolate\\_dierckx.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_dfitpack.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\interpolate\\_dfitpack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_fitpack.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\interpolate\\_fitpack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\transform\\_rotation.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\spatial\\transform\\_rotation.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_pybind.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\spatial\\_distance_pybind.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_hausdorff.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\spatial\\_hausdorff.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_wrap.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\spatial\\_distance_wrap.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_voronoi.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\spatial\\_voronoi.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_qhull.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\spatial\\_qhull.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_ckdtree.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\spatial\\_ckdtree.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_pava_pybind.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\optimize\\_pava_pybind.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsq\\givens_elimination.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\optimize\\_lsq\\givens_elimination.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsap.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\optimize\\_lsap.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_interpolative.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\linalg\\_decomp_interpolative.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_bglu_dense.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\optimize\\_bglu_dense.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_cython_nnls.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\optimize\\_cython_nnls.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_slsqp.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\optimize\\_slsqp.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_zeros.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\optimize\\_zeros.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_minpack.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\optimize\\_minpack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_trlib\\_trlib.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\optimize\\_trlib\\_trlib.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_cobyla.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\optimize\\_cobyla.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_moduleTNC.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\optimize\\_moduleTNC.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lbfgsb.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\optimize\\_lbfgsb.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_group_columns.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\optimize\\_group_columns.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_lsoda.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\integrate\\_lsoda.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_dop.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\integrate\\_dop.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_vode.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\integrate\\_vode.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_quadpack.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\integrate\\_quadpack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_odepack.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\integrate\\_odepack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_gufuncs.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\special\\_gufuncs.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_special_ufuncs.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\special\\_special_ufuncs.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_comb.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\special\\_comb.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_specfun.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\special\\_specfun.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\special\\_ufuncs.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs_cxx.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\special\\_ufuncs_cxx.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_linalg_pythran.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\linalg\\_linalg_pythran.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_expm.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_expm.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_sqrtm_triu.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_sqrtm_triu.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_lu_cython.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\linalg\\_decomp_lu_cython.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\fft\\_pocketfft\\pypocketfft.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\pypocketfft.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_uarray\\_uarray.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\_lib\\_uarray\\_uarray.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_solve_toeplitz.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\linalg\\_solve_toeplitz.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_cythonized_array_utils.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\linalg\\_cythonized_array_utils.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_zpropack.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_zpropack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_cpropack.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_cpropack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_dpropack.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_dpropack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_spropack.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_spropack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_dsolve\\_superlu.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\_superlu.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_csparsetools.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\sparse\\_csparsetools.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_sparsetools.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\sparse\\_sparsetools.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_rank_filter_1d.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\ndimage\\_rank_filter_1d.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_nd_image.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\ndimage\\_nd_image.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_ni_label.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\ndimage\\_ni_label.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_qmc_cy.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\stats\\_qmc_cy.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_sobol.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\stats\\_sobol.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats_pythran.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\stats\\_stats_pythran.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_rcont\\rcont.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\stats\\_rcont\\rcont.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_mvn.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\stats\\_mvn.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_ansari_swilk_statistics.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\stats\\_ansari_swilk_statistics.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_levy_stable\\levyst.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\stats\\_levy_stable\\levyst.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_biasedurn.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\stats\\_biasedurn.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\stats\\_stats.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\cython_special.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\special\\cython_special.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_fpumode.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\_lib\\_fpumode.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\messagestream.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\_lib\\messagestream.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\window\\indexers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\tslib.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\testing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\sparse.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sas.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\sas.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\reshape.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\properties.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\parsers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_parser.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\pandas_parser.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_datetime.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\pandas_datetime.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\ops.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\missing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\lib.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\json.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\join.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\interval.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\internals.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\indexing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\index.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\hashtable.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\hashing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\groupby.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\byteswap.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\byteswap.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\arrays.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\algos.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\VCRUNTIME140.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5PrintSupport.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5PrintSupport.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes313.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pywin32_system32\\pywintypes313.dll',
   'BINARY'),
  ('Pythonwin\\mfc140u.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python3.dll',
   'BINARY'),
  ('scipy\\special\\libsf_error_state.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\scipy\\special\\libsf_error_state.dll',
   'BINARY'),
  ('pandas.libs\\msvcp140-0f2ea95580b32bcfc81c235d5751ce78.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas.libs\\msvcp140-0f2ea95580b32bcfc81c235d5751ce78.dll',
   'BINARY'),
  ('sqlite3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('01.ico', 'C:\\Users\\<USER>\\Desktop\\taheri10\\01.ico', 'DATA'),
  ('archived_accounts_manager.py',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\archived_accounts_manager.py',
   'DATA'),
  ('attendance_processing_window.py',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\attendance_processing_window.py',
   'DATA'),
  ('attendance_sheet_report.py',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\attendance_sheet_report.py',
   'DATA'),
  ('budget_planning_window.py',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\budget_planning_window.py',
   'DATA'),
  ('build_executable.py',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\build_executable.py',
   'DATA'),
  ('cash_flow_window.py',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\cash_flow_window.py',
   'DATA'),
  ('check_database.py',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\check_database.py',
   'DATA'),
  ('daily_attendance_sheet_report.py',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\daily_attendance_sheet_report.py',
   'DATA'),
  ('expense_management_window.py',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\expense_management_window.py',
   'DATA'),
  ('financial_system_launcher.py',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\financial_system_launcher.py',
   'DATA'),
  ('fonts\\Arial.ttf',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\fonts\\Arial.ttf',
   'DATA'),
  ('fonts\\arialbd.ttf',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\fonts\\arialbd.ttf',
   'DATA'),
  ('fonts\\calibri.ttf',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\fonts\\calibri.ttf',
   'DATA'),
  ('fonts\\calibrib.ttf',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\fonts\\calibrib.ttf',
   'DATA'),
  ('jaraco.context-6.0.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco.context-6.0.1.dist-info\\INSTALLER',
   'DATA'),
  ('jaraco.context-6.0.1.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco.context-6.0.1.dist-info\\LICENSE',
   'DATA'),
  ('jaraco.context-6.0.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco.context-6.0.1.dist-info\\METADATA',
   'DATA'),
  ('jaraco.context-6.0.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco.context-6.0.1.dist-info\\RECORD',
   'DATA'),
  ('jaraco.context-6.0.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco.context-6.0.1.dist-info\\WHEEL',
   'DATA'),
  ('jaraco.context-6.0.1.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco.context-6.0.1.dist-info\\top_level.txt',
   'DATA'),
  ('jaraco.functools-4.1.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco.functools-4.1.0.dist-info\\INSTALLER',
   'DATA'),
  ('jaraco.functools-4.1.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco.functools-4.1.0.dist-info\\LICENSE',
   'DATA'),
  ('jaraco.functools-4.1.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco.functools-4.1.0.dist-info\\METADATA',
   'DATA'),
  ('jaraco.functools-4.1.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco.functools-4.1.0.dist-info\\RECORD',
   'DATA'),
  ('jaraco.functools-4.1.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco.functools-4.1.0.dist-info\\WHEEL',
   'DATA'),
  ('jaraco.functools-4.1.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco.functools-4.1.0.dist-info\\top_level.txt',
   'DATA'),
  ('jaraco.text-4.0.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco.text-4.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('jaraco.text-4.0.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco.text-4.0.0.dist-info\\LICENSE',
   'DATA'),
  ('jaraco.text-4.0.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco.text-4.0.0.dist-info\\METADATA',
   'DATA'),
  ('jaraco.text-4.0.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco.text-4.0.0.dist-info\\RECORD',
   'DATA'),
  ('jaraco.text-4.0.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco.text-4.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('jaraco.text-4.0.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco.text-4.0.0.dist-info\\WHEEL',
   'DATA'),
  ('jaraco.text-4.0.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco.text-4.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('jaraco\\collections\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\collections\\__init__.py',
   'DATA'),
  ('jaraco\\collections\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\collections\\py.typed',
   'DATA'),
  ('jaraco\\context.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'DATA'),
  ('jaraco\\context\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco\\context\\__init__.py',
   'DATA'),
  ('jaraco\\context\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco\\context\\py.typed',
   'DATA'),
  ('jaraco\\functools\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco\\functools\\__init__.py',
   'DATA'),
  ('jaraco\\functools\\__init__.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco\\functools\\__init__.pyi',
   'DATA'),
  ('jaraco\\functools\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\py.typed',
   'DATA'),
  ('jaraco\\text\\Lorem ipsum.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('jaraco\\text\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco\\text\\__init__.py',
   'DATA'),
  ('jaraco\\text\\layouts.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\layouts.py',
   'DATA'),
  ('jaraco\\text\\show-newlines.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\show-newlines.py',
   'DATA'),
  ('jaraco\\text\\strip-prefix.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco\\text\\strip-prefix.py',
   'DATA'),
  ('jaraco\\text\\to-dvorak.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco\\text\\to-dvorak.py',
   'DATA'),
  ('jaraco\\text\\to-qwerty.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jaraco\\text\\to-qwerty.py',
   'DATA'),
  ('logs\\subjects_teachers_20250607.log',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\logs\\subjects_teachers_20250607.log',
   'DATA'),
  ('logs\\subjects_teachers_20250608.log',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\logs\\subjects_teachers_20250608.log',
   'DATA'),
  ('logs\\subjects_teachers_20250609.log',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\logs\\subjects_teachers_20250609.log',
   'DATA'),
  ('logs\\subjects_teachers_20250610.log',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\logs\\subjects_teachers_20250610.log',
   'DATA'),
  ('logs\\subjects_teachers_20250611.log',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\logs\\subjects_teachers_20250611.log',
   'DATA'),
  ('main_window.py',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\main_window.py',
   'DATA'),
  ('monthly_duties_window.py',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\monthly_duties_window.py',
   'DATA'),
  ('pkg_resources\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'DATA'),
  ('pkg_resources\\api_tests.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pkg_resources\\api_tests.txt',
   'DATA'),
  ('pkg_resources\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pkg_resources\\py.typed',
   'DATA'),
  ('pkg_resources\\tests\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pkg_resources\\tests\\__init__.py',
   'DATA'),
  ('pkg_resources\\tests\\data\\my-test-package-source\\setup.cfg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pkg_resources\\tests\\data\\my-test-package-source\\setup.cfg',
   'DATA'),
  ('pkg_resources\\tests\\data\\my-test-package-source\\setup.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pkg_resources\\tests\\data\\my-test-package-source\\setup.py',
   'DATA'),
  ('pkg_resources\\tests\\data\\my-test-package-zip\\my-test-package.zip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pkg_resources\\tests\\data\\my-test-package-zip\\my-test-package.zip',
   'DATA'),
  ('pkg_resources\\tests\\data\\my-test-package_unpacked-egg\\my_test_package-1.0-py3.7.egg\\EGG-INFO\\PKG-INFO',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pkg_resources\\tests\\data\\my-test-package_unpacked-egg\\my_test_package-1.0-py3.7.egg\\EGG-INFO\\PKG-INFO',
   'DATA'),
  ('pkg_resources\\tests\\data\\my-test-package_unpacked-egg\\my_test_package-1.0-py3.7.egg\\EGG-INFO\\SOURCES.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pkg_resources\\tests\\data\\my-test-package_unpacked-egg\\my_test_package-1.0-py3.7.egg\\EGG-INFO\\SOURCES.txt',
   'DATA'),
  ('pkg_resources\\tests\\data\\my-test-package_unpacked-egg\\my_test_package-1.0-py3.7.egg\\EGG-INFO\\dependency_links.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pkg_resources\\tests\\data\\my-test-package_unpacked-egg\\my_test_package-1.0-py3.7.egg\\EGG-INFO\\dependency_links.txt',
   'DATA'),
  ('pkg_resources\\tests\\data\\my-test-package_unpacked-egg\\my_test_package-1.0-py3.7.egg\\EGG-INFO\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pkg_resources\\tests\\data\\my-test-package_unpacked-egg\\my_test_package-1.0-py3.7.egg\\EGG-INFO\\top_level.txt',
   'DATA'),
  ('pkg_resources\\tests\\data\\my-test-package_unpacked-egg\\my_test_package-1.0-py3.7.egg\\EGG-INFO\\zip-safe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pkg_resources\\tests\\data\\my-test-package_unpacked-egg\\my_test_package-1.0-py3.7.egg\\EGG-INFO\\zip-safe',
   'DATA'),
  ('pkg_resources\\tests\\data\\my-test-package_zipped-egg\\my_test_package-1.0-py3.7.egg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pkg_resources\\tests\\data\\my-test-package_zipped-egg\\my_test_package-1.0-py3.7.egg',
   'DATA'),
  ('pkg_resources\\tests\\test_find_distributions.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pkg_resources\\tests\\test_find_distributions.py',
   'DATA'),
  ('pkg_resources\\tests\\test_integration_zope_interface.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pkg_resources\\tests\\test_integration_zope_interface.py',
   'DATA'),
  ('pkg_resources\\tests\\test_markers.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pkg_resources\\tests\\test_markers.py',
   'DATA'),
  ('pkg_resources\\tests\\test_pkg_resources.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pkg_resources\\tests\\test_pkg_resources.py',
   'DATA'),
  ('pkg_resources\\tests\\test_resources.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pkg_resources\\tests\\test_resources.py',
   'DATA'),
  ('pkg_resources\\tests\\test_working_set.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pkg_resources\\tests\\test_working_set.py',
   'DATA'),
  ('print101.py', 'C:\\Users\\<USER>\\Desktop\\taheri10\\print101.py', 'DATA'),
  ('print111.py', 'C:\\Users\\<USER>\\Desktop\\taheri10\\print111.py', 'DATA'),
  ('print144.py', 'C:\\Users\\<USER>\\Desktop\\taheri10\\print144.py', 'DATA'),
  ('print_registration_fees.py',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\print_registration_fees.py',
   'DATA'),
  ('print_registration_fees_monthly_style.py',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\print_registration_fees_monthly_style.py',
   'DATA'),
  ('print_registration_fees_simple.py',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\print_registration_fees_simple.py',
   'DATA'),
  ('print_section_monthly.py',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\print_section_monthly.py',
   'DATA'),
  ('print_section_yearly.py',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\print_section_yearly.py',
   'DATA'),
  ('reports\\ورقة_الغياب_اليومية_قسم___01_2025-06-08_3حصص.pdf',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\reports\\ورقة_الغياب_اليومية_قسم___01_2025-06-08_3حصص.pdf',
   'DATA'),
  ('reports\\ورقة_الغياب_اليومية_قسم___01_2025-06-08_4حصص.pdf',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\reports\\ورقة_الغياب_اليومية_قسم___01_2025-06-08_4حصص.pdf',
   'DATA'),
  ('reports\\ورقة_الغياب_اليومية_قسم___01_2025-06-08_5حصص.pdf',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\reports\\ورقة_الغياب_اليومية_قسم___01_2025-06-08_5حصص.pdf',
   'DATA'),
  ('reports\\ورقة_الغياب_اليومية_قسم___01_2025-06-08_6حصص.pdf',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\reports\\ورقة_الغياب_اليومية_قسم___01_2025-06-08_6حصص.pdf',
   'DATA'),
  ('reports\\ورقة_الغياب_اليومية_قسم___01_2025-06-10_4حصص.pdf',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\reports\\ورقة_الغياب_اليومية_قسم___01_2025-06-10_4حصص.pdf',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\entry_points.txt',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\jaraco.collections-5.1.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\jaraco.collections-5.1.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\jaraco.collections-5.1.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\jaraco.collections-5.1.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\jaraco.collections-5.1.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\jaraco.collections-5.1.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\jaraco.collections-5.1.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\jaraco.collections-5.1.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\jaraco.collections-5.1.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\jaraco.collections-5.1.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\jaraco.collections-5.1.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\jaraco.collections-5.1.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\jaraco.collections-5.1.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\jaraco.collections-5.1.0.dist-info\\top_level.txt',
   'DATA'),
  ('sub01_window.py',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\sub01_window.py',
   'DATA'),
  ('sub100_window.py',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\sub100_window.py',
   'DATA'),
  ('sub232_window.py',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\sub232_window.py',
   'DATA'),
  ('sub252_window.py',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\sub252_window.py',
   'DATA'),
  ('sub262_window.py',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\sub262_window.py',
   'DATA'),
  ('sub2_window.py',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\sub2_window.py',
   'DATA'),
  ('sub3_window.py',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\sub3_window.py',
   'DATA'),
  ('sub8_window.py',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\sub8_window.py',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\importlib_metadata-8.7.0.dist-info\\INSTALLER',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\importlib_metadata-8.7.0.dist-info\\METADATA',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\importlib_metadata-8.7.0.dist-info\\WHEEL',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\importlib_metadata-8.7.0.dist-info\\top_level.txt',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\importlib_metadata-8.7.0.dist-info\\REQUESTED',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\importlib_metadata-8.7.0.dist-info\\RECORD',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\importlib_metadata-8.7.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward_large.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\kpsewhich.lua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\kpsewhich.lua',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\msft.csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\msft.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help_large.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home_large.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-deep.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-deep.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.pdf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.pdf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\goog.npz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\goog.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.pdf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back_large.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-colorblind.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-colorblind.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-whitegrid.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-whitegrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\logo2.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\logo2.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Stocks.csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Stocks.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\matplotlibrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\matplotlibrc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-white.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-white.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-talk.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-talk.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-notebook.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-notebook.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-pastel.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-pastel.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots_large.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark-palette.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark-palette.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\README.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\README.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.pdf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-poster.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-poster.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-ticks.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-ticks.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave_large.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.pdf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move_large.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.pdf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-muted.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-muted.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-darkgrid.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-darkgrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.pdf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.pdf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\petroff10.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\petroff10.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-paper.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-paper.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-bright.mplstyle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-bright.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.png',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('setuptools\\_vendor\\typeguard-4.3.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\typeguard-4.3.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\typeguard-4.3.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.5.dist-info\\DELVEWHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy-2.2.5.dist-info\\DELVEWHEEL',
   'DATA'),
  ('setuptools\\_vendor\\typeguard-4.3.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.5.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy-2.2.5.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.2.5.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy-2.2.5.dist-info\\REQUESTED',
   'DATA'),
  ('numpy-2.2.5.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy-2.2.5.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\typeguard-4.3.0.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.5.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy-2.2.5.dist-info\\entry_points.txt',
   'DATA'),
  ('setuptools\\_vendor\\typeguard-4.3.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.5.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy-2.2.5.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\typeguard-4.3.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.5.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy-2.2.5.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.5.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy-2.2.5.dist-info\\INSTALLER',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\taheri10\\build\\main_window_fixed\\base_library.zip',
   'DATA')],)
