#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QLabel, QLineEdit, QPushButton, QTableWidget,
    QTableWidgetItem, QFrame, QMessageBox, QSpinBox, QComboBox,
    QDateEdit, QFormLayout, QGroupBox, QGridLayout, QHeaderView,
    QTextEdit, QDoubleSpinBox, QCheckBox, QDialog, QListWidget,
    QListWidgetItem, QInputDialog, QAbstractItemView
)
from PyQt5.QtGui import QFont, QIcon, QColor, QPixmap
from PyQt5.QtCore import Qt, QDate, QSize
from datetime import datetime
import json

class GroupManagementDialog(QDialog):
    """نافذة إدارة المجموعات الجميلة والمنسقة"""
    
    def __init__(self, parent=None, current_groups=None):
        super().__init__(parent)
        self.parent_combo = parent.group_combo if parent else None
        self.current_groups = current_groups or []
        self.modified_groups = []
        self.setupUI()
        self.load_groups()
        
    def setupUI(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إدارة المجموعات")
        self.setFixedSize(600, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق نمط احترافي للنافذة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fc,
                    stop: 1 #e9ecef
                );
            }
        """)
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(5)
        
        # العنوان الرئيسي
        title_label = QLabel("🎓 إدارة المجموعات التعليمية")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setMaximumHeight(100)
        title_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #667eea,
                    stop: 0.5 #764ba2,
                    stop: 1 #667eea
                );
                color: white;
                padding: 8px;
                border-radius: 10px;
                font-weight: bold;
            }
        """)
        main_layout.addWidget(title_label)
        
        # الإطار الرئيسي
        main_frame = QFrame()
        main_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #e3f2fd;
                border-radius: 10px;
                padding: 8px;
            }
        """)
        frame_layout = QVBoxLayout(main_frame)
        frame_layout.setSpacing(5)
        
        # شريط الإضافة
        add_frame = QFrame()
        add_frame.setMaximumHeight(120)
        add_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #e8f5e8,
                    stop: 1 #f0f8ff
                );
                border: 2px solid #4CAF50;
                border-radius: 8px;
                padding: 5px;
            }
        """)
        add_layout = QHBoxLayout(add_frame)
        add_layout.setSpacing(5)
        
        add_label = QLabel(" إضافة مجموعة جديدة:")
        add_label.setFont(QFont("Calibri", 13, QFont.Bold))
        add_label.setStyleSheet("color: #2e7d32; border: none;")
        
        self.new_group_input = QLineEdit()
        self.new_group_input.setFont(QFont("Calibri", 13, QFont.Bold))
        self.new_group_input.setPlaceholderText("أدخل اسم المجموعة الجديدة...")
        self.new_group_input.setMaximumHeight(32)
        self.new_group_input.setStyleSheet("""
            QLineEdit {
                background-color: white;
                border: 2px solid #81c784;
                border-radius: 6px;
                padding: 4px 8px;
                font-size: 11px;
            }
            QLineEdit:focus {
                border: 2px solid #4CAF50;
                background-color: #f8fff8;
            }
        """)
        self.new_group_input.returnPressed.connect(self.add_group)
        
        add_btn = QPushButton("✨ إضافة")
        add_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        add_btn.setMaximumHeight(32)
        add_btn.setMinimumWidth(80)
        add_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #4CAF50,
                    stop: 1 #388e3c
                );
                color: white;
                border: none;
                border-radius: 6px;
                padding: 4px 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #66bb6a,
                    stop: 1 #4CAF50
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #388e3c,
                    stop: 1 #2e7d32
                );
            }
        """)
        add_btn.clicked.connect(self.add_group)
        
        add_layout.addWidget(add_label)
        add_layout.addWidget(self.new_group_input, 2)
        add_layout.addWidget(add_btn)
        
        frame_layout.addWidget(add_frame)
        
        # قائمة المجموعات الموجودة
        groups_label = QLabel("📋 المجموعات الموجودة:")
        groups_label.setFont(QFont("Calibri", 13, QFont.Bold))
        groups_label.setMaximumHeight(100)
        groups_label.setStyleSheet("color: #1565c0; margin-top: 2px;")
        frame_layout.addWidget(groups_label)
        
        # قائمة المجموعات مع أزرار التحكم
        self.groups_list = QListWidget()
        self.groups_list.setFont(QFont("Calibri", 13))
        self.groups_list.setMinimumHeight(200)
        self.groups_list.setStyleSheet("""
            QListWidget {
                background-color: #fafafa;
                border: 2px solid #e1f5fe;
                border-radius: 8px;
                padding: 5px;
                selection-background-color: #bbdefb;
            }
            QListWidget::item {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 6px;
                padding: 6px;
                margin: 1px 0px;
                font-weight: bold;
                max-height: 30px;
            }
            QListWidget::item:selected {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #2196f3,
                    stop: 1 #1976d2
                );
                color: white;
                border: 1px solid #1976d2;
            }
            QListWidget::item:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #e3f2fd,
                    stop: 1 #bbdefb
                );
                border: 1px solid #2196f3;
            }
        """)
        frame_layout.addWidget(self.groups_list)
        
        # أزرار العمليات على المجموعات
        groups_buttons_layout = QHBoxLayout()
        groups_buttons_layout.setSpacing(2)
        
        edit_btn = self.create_action_button("✏️ تعديل", "#2196F3")
        edit_btn.clicked.connect(self.edit_selected_group)
        
        delete_btn = self.create_action_button("🗑️ حذف", "#F44336")
        delete_btn.clicked.connect(self.delete_selected_group)
        
        move_up_btn = self.create_action_button("⬆️ تحريك لأعلى", "#FF9800")
        move_up_btn.clicked.connect(self.move_group_up)
        
        move_down_btn = self.create_action_button("⬇️ تحريك لأسفل", "#9C27B0")
        move_down_btn.clicked.connect(self.move_group_down)
        
        groups_buttons_layout.addWidget(edit_btn)
        groups_buttons_layout.addWidget(delete_btn)
        groups_buttons_layout.addWidget(move_up_btn)
        groups_buttons_layout.addWidget(move_down_btn)
        groups_buttons_layout.addStretch()
        
        frame_layout.addLayout(groups_buttons_layout)
        
        main_layout.addWidget(main_frame)
        
        # أزرار التحكم الرئيسية
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(2)
        
        save_btn = QPushButton("💾 حفظ التغييرات")
        save_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        save_btn.setMaximumHeight(35)
        save_btn.setMinimumWidth(120)
        save_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #4CAF50,
                    stop: 1 #2e7d32
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #66bb6a,
                    stop: 1 #4CAF50
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #2e7d32,
                    stop: 1 #1b5e20
                );
            }
        """)
        save_btn.clicked.connect(self.save_and_close)
        
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        cancel_btn.setMaximumHeight(35)
        cancel_btn.setMinimumWidth(100)
        cancel_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #757575,
                    stop: 1 #424242
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #9e9e9e,
                    stop: 1 #757575
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #424242,
                    stop: 1 #212121
                );
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addStretch()
        
        main_layout.addWidget(buttons_frame)
        
    def create_action_button(self, text, color):
        """إنشاء زر عمليات منسق"""
        button = QPushButton(text)
        button.setFont(QFont("Calibri", 11, QFont.Bold))
        button.setMaximumHeight(32)
        button.setMinimumWidth(120)
        
        # تحويل لون hex إلى لون مظلم
        dark_color = self.darken_color(color, 30)
        light_color = self.lighten_color(color, 20)
        
        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color},
                    stop: 1 {dark_color}
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 6px 10px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {light_color},
                    stop: 1 {color}
                );
            }}
            QPushButton:pressed {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {dark_color},
                    stop: 1 {self.darken_color(dark_color, 20)}
                );
            }}
        """)
        return button
    
    def lighten_color(self, color, amount):
        """تفتيح اللون"""
        if color.startswith('#'):
            color = color[1:]
        
        r = min(255, int(color[0:2], 16) + amount)
        g = min(255, int(color[2:4], 16) + amount)
        b = min(255, int(color[4:6], 16) + amount)
        
        return f"#{r:02x}{g:02x}{b:02x}"

    def darken_color(self, color, amount):
        """تغميق اللون"""
        if color.startswith('#'):
            color = color[1:]
        
        r = max(0, int(color[0:2], 16) - amount)
        g = max(0, int(color[2:4], 16) - amount)
        b = max(0, int(color[4:6], 16) - amount)
        
        return f"#{r:02x}{g:02x}{b:02x}"
    
    def load_groups(self):
        """تحميل المجموعات الحالية"""
        self.modified_groups = self.current_groups.copy()
        self.update_groups_list()
    
    def update_groups_list(self):
        """تحديث قائمة المجموعات"""
        self.groups_list.clear()
        for group in self.modified_groups:
            item = QListWidgetItem(f"📚 {group}")
            item.setData(Qt.UserRole, group)
            self.groups_list.addItem(item)
    
    def add_group(self):
        """إضافة مجموعة جديدة"""
        group_name = self.new_group_input.text().strip()
        
        if not group_name:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المجموعة.")
            return
        
        if group_name in self.modified_groups:
            QMessageBox.warning(self, "تحذير", "هذه المجموعة موجودة بالفعل.")
            return
        
        self.modified_groups.append(group_name)
        self.update_groups_list()
        self.new_group_input.clear()
        
        # تحديد المجموعة المadded حديثاً
        for i in range(self.groups_list.count()):
            item = self.groups_list.item(i)
            if item.data(Qt.UserRole) == group_name:
                self.groups_list.setCurrentItem(item)
                break
        
        QMessageBox.information(self, "نجح", f"تم إضافة المجموعة '{group_name}' بنجاح.")
    
    def edit_selected_group(self):
        """تعديل المجموعة المحددة"""
        current_item = self.groups_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مجموعة للتعديل.")
            return
        
        old_name = current_item.data(Qt.UserRole)
        new_name, ok = QInputDialog.getText(
            self, 
            "تعديل المجموعة", 
            "اسم المجموعة الجديد:",
            QLineEdit.Normal,
            old_name
        )
        
        if ok and new_name.strip() and new_name.strip() != old_name:
            new_name = new_name.strip()
            
            if new_name in self.modified_groups:
                QMessageBox.warning(self, "تحذير", "هذا الاسم موجود بالفعل.")
                return
            
            # تحديث القائمة
            index = self.modified_groups.index(old_name)
            self.modified_groups[index] = new_name
            self.update_groups_list()
            
            # تحديد المجموعة المعدلة
            for i in range(self.groups_list.count()):
                item = self.groups_list.item(i)
                if item.data(Qt.UserRole) == new_name:
                    self.groups_list.setCurrentItem(item)
                    break
            
            QMessageBox.information(self, "نجح", f"تم تعديل المجموعة من '{old_name}' إلى '{new_name}'.")
    
    def delete_selected_group(self):
        """حذف المجموعة المحددة"""
        current_item = self.groups_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مجموعة للحذف.")
            return
        
        group_name = current_item.data(Qt.UserRole)
        
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف المجموعة '{group_name}'؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.modified_groups.remove(group_name)
            self.update_groups_list()
            QMessageBox.information(self, "نجح", f"تم حذف المجموعة '{group_name}' بنجاح.")
    
    def move_group_up(self):
        """تحريك المجموعة لأعلى"""
        current_row = self.groups_list.currentRow()
        if current_row <= 0:
            QMessageBox.information(self, "تنبيه", "المجموعة في أعلى القائمة بالفعل.")
            return
        
        # تبديل المواضع
        self.modified_groups[current_row], self.modified_groups[current_row - 1] = \
            self.modified_groups[current_row - 1], self.modified_groups[current_row]
        
        self.update_groups_list()
        self.groups_list.setCurrentRow(current_row - 1)
    
    def move_group_down(self):
        """تحريك المجموعة لأسفل"""
        current_row = self.groups_list.currentRow()
        if current_row >= len(self.modified_groups) - 1:
            QMessageBox.information(self, "تنبيه", "المجموعة في أسفل القائمة بالفعل.")
            return
        
        # تبديل المواضع
        self.modified_groups[current_row], self.modified_groups[current_row + 1] = \
            self.modified_groups[current_row + 1], self.modified_groups[current_row]
        
        self.update_groups_list()
        self.groups_list.setCurrentRow(current_row + 1)
    
    def save_and_close(self):
        """حفظ التغييرات وإغلاق النافذة"""
        if self.parent_combo:
            # حفظ المجموعة المحددة حالياً
            current_selection = self.parent_combo.currentText()
            
            # تحديث قائمة المجموعات في الواجهة الرئيسية
            self.parent_combo.clear()
            self.parent_combo.addItems(self.modified_groups)
            
            # استعادة التحديد إذا كان موجوداً
            if current_selection in self.modified_groups:
                index = self.parent_combo.findText(current_selection)
                if index >= 0:
                    self.parent_combo.setCurrentIndex(index)
            elif self.modified_groups:
                self.parent_combo.setCurrentIndex(0)
        
            QMessageBox.information(self, "نجح", "تم حفظ التغييرات بنجاح.")
            self.accept()
    
        def get_groups(self):
            """الحصول على قائمة المجموعات المعدلة"""
            return self.modified_groups

class MonthlyDutiesWindow(QMainWindow):
    """نافذة واجبات التسجيل والواجبات الشهرية"""
    
    def __init__(self, parent=None, db_path="data.db"):
        super().__init__(parent)
        self.db_path = db_path
        
        # إعدادات أحجام الخطوط المركزية - يمكنك تغيير الأرقام هنا للتحكم في جميع عناصر مجموعة الاتصال
        self.CONTACT_INPUT_FONT_SIZE = 14      # حجم خط حقول الإدخال النصية
        self.CONTACT_COMBO_FONT_SIZE = 14      # حجم خط القوائم المنسدلة
        self.CONTACT_TEXTAREA_FONT_SIZE = 14   # حجم خط منطقة النص (الملاحظات)
        
        # إعدادات أحجام الخطوط لمجموعة التمدرس - يمكنك تغيير الأرقام هنا للتحكم في جميع عناصر مجموعة التمدرس
        self.SCHOOLING_COMBO_FONT_SIZE = 14    # حجم خط القوائم المنسدلة في التمدرس (المجموعة)
        self.SCHOOLING_EDITABLE_COMBO_FONT_SIZE = 14  # حجم خط مربعات التحرير والسرد في التمدرس (القسم والمؤسسة)
        self.SCHOOLING_BUTTON_FONT_SIZE = 14   # حجم خط أزرار الإضافة في التمدرس
        self.SCHOOLING_COMBO_HEIGHT = 40       # ارتفاع القوائم المنسدلة في مجموعة التمدرس (بالبكسل)
        
        # إعدادات واجبات التسجيل
        self.REGISTRATION_INPUT_FONT_SIZE = 15  # حجم خط حقول واجبات التسجيل
        self.REGISTRATION_INPUT_HEIGHT = 40     # ارتفاع حقول واجبات التسجيل (بالبكسل)
        
        # إعدادات الواجبات الشهرية
        self.MONTHLY_INPUT_FONT_SIZE = 15       # حجم خط حقل الواجب الشهري
        self.MONTHLY_INPUT_HEIGHT = 40          # ارتفاع حقل الواجب الشهري (بالبكسل)

        self.setupUI()
        self.setup_database()
       # إزالة تحميل البيانات عند الفتح ليكون في وضع إضافة
        # self.load_data()
        
        # إعداد النافذة لوضع الإضافة
        self.setup_add_mode()

    def showEvent(self, event):
        """حدث عرض النافذة - تحديث قائمة الأقسام عند فتح النافذة"""
        super().showEvent(event)
        # تحديث قائمة الأقسام عند فتح النافذة
        self.refresh_sections_combo()

    def setupUI(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("واجبات التسجيل والواجبات الشهرية")
        self.setFixedSize(900, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق نمط احترافي للنافذة الرئيسية
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fc,
                    stop: 1 #e9ecef
                );
            }
        """)
        
        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(10)
        
        # العنوان الرئيسي - يمكنك تغيير حجم الخط هنا
        title_label = QLabel("واجبات التسجيل والواجبات الشهرية")
        title_label.setFont(QFont("Calibri", 15, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #3498db,
                    stop: 0.5 #2980b9,
                    stop: 1 #3498db
                );
                color: white;
                padding: 20px;
                border-radius: 15px;
                font-weight: bold;
                margin-bottom: 10px;
                box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
            }
        """)
        main_layout.addWidget(title_label)
        
        # إنشاء التبويبات - حجم خط أسماء التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setFont(QFont("Calibri", 15, QFont.Bold))  # هنا تتحكم في حجم خط أسماء التبويبات الأساسي
        
        # تطبيق نمط احترافي للتبويبات
        self.tab_widget.setStyleSheet("""
            QTabWidget {
                background-color: transparent;
                border: none;
            }
            
            QTabWidget::pane {
                border: 3px solid #3498db;
                border-radius: 15px;
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ffffff,
                    stop: 1 #f8f9fa
                );
                padding: 10px;
                margin-top: 5px;
            }
            
            QTabWidget::tab-bar {
                alignment: center;
                left: 10px;
            }
            
            QTabBar::tab {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ecf0f1,
                    stop: 1 #d5dbdb
                );
                color: #2c3e50;
                padding: 15px 25px;
                margin: 2px;
                border-radius: 12px 12px 0px 0px;
                font-family: 'Calibri';
                font-size: 18px;    /* هنا تتحكم في حجم خط التبويبات في CSS */
                font-weight: bold;
                min-width: 140px;
                border: 2px solid #bdc3c7;
                border-bottom: none;
            }
            
            QTabBar::tab:selected {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #3498db,
                    stop: 0.5 #2980b9,
                    stop: 1 #3498db
                );
                color: white;
                border: 2px solid #2980b9;
                border-bottom: none;
                margin-bottom: -2px;
                padding-bottom: 17px;
            }
            
            QTabBar::tab:hover:!selected {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e8f4fd,
                    stop: 1 #d6eaf8
                );
                color: #2980b9;
                border: 2px solid #85c1e9;
                border-bottom: none;
            }
            
            QTabBar::tab:first {
                margin-left: 10px;
            }
            
            QTabBar::tab:last {
                margin-right: 10px;
            }
            
            /* إضافة تأثير الظل للتبويبات */
            QTabBar::tab:selected {
                box-shadow: 0 -3px 10px rgba(52, 152, 219, 0.3);
            }
        """)

        # إضافة التبويبات
        self.setup_contact_tab()
        self.setup_schooling_tab()
        self.setup_registration_tab()
        self.setup_monthly_duties_tab()
        
        main_layout.addWidget(self.tab_widget)
        
    def setup_contact_tab(self):
        """إعداد تبويب معلومات الاتصال"""
        contact_tab = QWidget()
        layout = QVBoxLayout(contact_tab)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # إطار معلومات الاتصال - عنوان المجموعة
        contact_frame = QGroupBox("معلومات الاتصال")
        contact_frame.setFont(QFont("Calibri", 16, QFont.Bold))
        
        contact_layout = QFormLayout(contact_frame)
        contact_layout.setSpacing(5)
        
        # إنشاء حقول معلومات الاتصال
        self.student_name_input = self.create_contact_input()
        self.student_code_input = self.create_contact_input()
        self.phone_input = self.create_contact_input()
        self.phone2_input = self.create_contact_input()
        
        # حقل النوع
        self.gender_combo = QComboBox()
        self.gender_combo.addItems(["ذكر", "أنثى"])
        self.gender_combo.setFont(QFont("Calibri", self.CONTACT_COMBO_FONT_SIZE, QFont.Bold))
        
        self.address_input = QTextEdit()
        self.address_input.setMinimumHeight(50)
        self.address_input.setMaximumHeight(100)
        self.address_input.setFont(QFont("Calibri", self.CONTACT_TEXTAREA_FONT_SIZE, QFont.Bold))

        # إضافة الحقول إلى النموذج
        contact_layout.addRow(self.create_styled_label("اسم التلميذ:"), self.student_name_input)
        contact_layout.addRow(self.create_styled_label("رمز التلميذ:"), self.student_code_input)
        contact_layout.addRow(self.create_styled_label("النوع:"), self.gender_combo)
        contact_layout.addRow(self.create_styled_label("رقم الهاتف الأول:"), self.phone_input)
        contact_layout.addRow(self.create_styled_label("رقم الهاتف الثاني:"), self.phone2_input)
        contact_layout.addRow(self.create_styled_label("ملاحظات :"), self.address_input)
        
        layout.addWidget(contact_frame)
        
        # إزالة أزرار العمليات من تبويب معلومات الاتصال
        layout.addStretch()
        
        self.tab_widget.addTab(contact_tab, "معلومات الاتصال")
        
    def setup_schooling_tab(self):
        """إعداد تبويب معلومات التمدرس"""
        schooling_tab = QWidget()
        layout = QVBoxLayout(schooling_tab)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # إطار معلومات التمدرس - عنوان المجموعة
        schooling_frame = QGroupBox("معلومات التمدرس")
        schooling_frame.setFont(QFont("Calibri", 16, QFont.Bold))
        
        schooling_layout = QFormLayout(schooling_frame)
        schooling_layout.setSpacing(5)
        
        # حقل السلك التعليمي مع قائمة منسدلة
        self.cycle_combo = QComboBox()
        self.cycle_combo.addItems([
            "السلك الابتدائي",
            "السلك الثاني الاعدادي", 
            "السلك الثانوي التأهيلي"
        ])
        self.cycle_combo.setFont(QFont("Calibri", self.SCHOOLING_COMBO_FONT_SIZE, QFont.Bold))
        self.cycle_combo.setMinimumHeight(self.SCHOOLING_COMBO_HEIGHT)
        
        # حقل اسم المجموعة مع قائمة منسدلة
        self.group_combo = QComboBox()
        self.group_combo.addItems([
            "مجموعة مفتوحة",
            "مجموعة مغلقة", 
            "مجموعة المباريات",
            "مجموعة خاصة"
        ])
        self.group_combo.setFont(QFont("Calibri", self.SCHOOLING_COMBO_FONT_SIZE, QFont.Bold))
        self.group_combo.setMinimumHeight(self.SCHOOLING_COMBO_HEIGHT)
        
        # حقل المادة الدراسية مع قائمة منسدلة
        self.subject_combo = QComboBox()
        self.subject_combo.addItems([
            "التربية الإسلامية",
            "اللغة العربية",
            "اللغة الفرنسية",
            "اللغة الإنجليزية",
            "الرياضيات",
            "علوم الحياة والأرض",
            "الفيزياء والكيمياء",
            "التاريخ والجغرافيا",
            "التربية البدنية"
        ])
        self.subject_combo.setFont(QFont("Calibri", self.SCHOOLING_COMBO_FONT_SIZE, QFont.Bold))
        self.subject_combo.setMinimumHeight(self.SCHOOLING_COMBO_HEIGHT)
  
          # حقل القسم - عرض البيانات من جدول المواد والأقسام
        section_layout = QVBoxLayout()
        
        # إطار لعرض جدول المواد والأقسام
        sections_table_frame = QGroupBox("الأقسام المسجلة والأساتذة")
        sections_table_frame.setFont(QFont("Calibri", 14, QFont.Bold))
        sections_table_frame.setMaximumHeight(200)
        
        sections_table_layout = QVBoxLayout(sections_table_frame)
        
        # إنشاء جدول لعرض الأقسام والأساتذة والمواد
        self.sections_table = QTableWidget()
        self.sections_table.setColumnCount(3)
        self.sections_table.setHorizontalHeaderLabels(['اسم الأستاذ', 'القسم', 'المادة'])
        self.sections_table.setFont(QFont("Calibri", 12))
        self.sections_table.setAlternatingRowColors(True)
        self.sections_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.sections_table.horizontalHeader().setStretchLastSection(True)
        self.sections_table.verticalHeader().setVisible(False)
        self.sections_table.setMaximumHeight(150)
        
        # تعيين عرض الأعمدة
        header = self.sections_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # اسم الأستاذ
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # القسم
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # المادة
        
        sections_table_layout.addWidget(self.sections_table)
        
        # حقل اختيار القسم من البيانات المحملة
        section_selection_layout = QHBoxLayout()
        section_selection_label = QLabel("اختيار القسم:")
        section_selection_label.setFont(QFont("Calibri", 12, QFont.Bold))
        
        self.section_combo = QComboBox()
        self.section_combo.setEditable(True)
        self.section_combo.setFont(QFont("Calibri", self.SCHOOLING_EDITABLE_COMBO_FONT_SIZE, QFont.Bold))
        self.section_combo.lineEdit().setFont(QFont("Calibri", self.SCHOOLING_EDITABLE_COMBO_FONT_SIZE, QFont.Bold))
        self.section_combo.setMinimumHeight(self.SCHOOLING_COMBO_HEIGHT)
        
        # زر تحديث البيانات
        refresh_sections_btn = self.create_schooling_button("🔄 تحديث", "#4CAF50")
        refresh_sections_btn.clicked.connect(self.refresh_sections_combo)
        refresh_sections_btn.setToolTip("تحديث قائمة الأقسام من قاعدة البيانات")
        
        section_selection_layout.addWidget(section_selection_label)
        section_selection_layout.addWidget(self.section_combo, 3)
        section_selection_layout.addWidget(refresh_sections_btn, 1)
        
        section_layout.addWidget(sections_table_frame)
        section_layout.addLayout(section_selection_layout)
        
        # تحميل البيانات من قاعدة البيانات
        self.load_sections_from_database()
        
        # حقل المؤسسة الأصلية مع قائمة منسدلة وزر إضافة
        institution_layout = QHBoxLayout()
        self.institution_combo = QComboBox()
        self.institution_combo.setEditable(True)
        self.institution_combo.addItems([
    
    "الثانوية الاعدادية آسية الوديع",
    "الثانوية الاعدادية أم البنين",
    "الثانوية الاعدادية أنوال",
    "الثانوية الاعدادية إدريس الثاني",
    "الثانوية الاعدادية ابن الهيثم",
    "الثانوية الاعدادية ابن رشد",
    "الثانوية الاعدادية ابن طفيل",
    "الثانوية الاعدادية أبو القاسم الشابي",
    "الثانوية الاعدادية أطلس",
    "الثانوية الاعدادية البحر",
    "الثانوية الاعدادية بدر",
    "الثانوية الاعدادية جابر بن حيان",
    "الثانوية الاعدادية خليج طنجة",
    "الثانوية الاعدادية الزهراء",
    "الثانوية الاعدادية الساحل الشمالي",
    "الثانوية الاعدادية سيدي اليماني",
    "الثانوية الاعدادية عبد الله ابن ياسين",
    "الثانوية الاعدادية عبد الله كنون",
    "الثانوية الاعدادية عبد العزيز مزيان بلفقيه",
    "الثانوية الاعدادية عمر بن عبد العزيز",
    "الثانوية الاعدادية فاطمة المرابط",
    "الثانوية الاعدادية القصبة",
    "الثانوية الاعدادية محمد الخامس",
    "الثانوية الاعدادية محمد السادس",
    "الثانوية الاعدادية محمد بن الحسن الوزاني",
    "الثانوية الاعدادية ماء العيني",
    "الثانوية الاعدادية مولاي عبد الرحمن",
    "الثانوية الاعدادية نهضة",
    "الثانوية الاعدادية طارق بن زياد",
    "الثانوية الاعدادية تورية الشاوي",
    "الثانوية التأهيلية الجامعي",
    "الثانوية التأهيلية الحنصالي",
    "الثانوية التأهيلية الخوارزمي",
    "الثانوية التأهيلية دار الشاوي",
    "الثانوية التأهيلية عبد الله الشفشاوني",
    "الثانوية التأهيلية علال الفاسي",
    "الثانوية التأهيلية محمد الفقيه الركراكي",
    "الثانوية التأهيلية محمد علي الصقلي",
    "الثانوية التأهيلية مولاي سليمان",
    "الثانوية التأهيلية مولاي علي الشرقي",
    "الثانوية التأهيلية أنوال",
    "الثانوية التأهيلية الكركرات",
    "ثانوية الملك فهد ابن عبد العزيز الاعدادية",
    "ثانوية الملك فهد ابن عبد العزيز التأهيلية"
])

        self.institution_combo.setFont(QFont("Calibri", self.SCHOOLING_EDITABLE_COMBO_FONT_SIZE, QFont.Bold))
        # تطبيق الخط على حقل النص القابل للتحرير بداخل القائمة المنسدلة
        self.institution_combo.lineEdit().setFont(QFont("Calibri", self.SCHOOLING_EDITABLE_COMBO_FONT_SIZE, QFont.Bold))
        self.institution_combo.setMinimumHeight(self.SCHOOLING_COMBO_HEIGHT)

        add_institution_btn = self.create_schooling_button("➕ إضافة مؤسسة", "#FF9800")
        add_institution_btn.clicked.connect(self.add_new_institution)
        
        institution_layout.addWidget(self.institution_combo, 3)
        institution_layout.addWidget(add_institution_btn, 1)
          # إضافة الحقول إلى النموذج (بدون المادة الدراسية واسم المجموعة)
        schooling_layout.addRow(self.create_styled_label("السلك التعليمي:"), self.cycle_combo)
        schooling_layout.addRow(self.create_styled_label("القسم:"), section_layout)
        schooling_layout.addRow(self.create_styled_label("المؤسسة الأصلية:"), institution_layout)
        
        layout.addWidget(schooling_frame)
        
        # إزالة أزرار العمليات من تبويب معلومات التمدرس
        layout.addStretch()
        
        self.tab_widget.addTab(schooling_tab, "معلومات التمدرس")
        
    def setup_registration_tab(self):
        """إعداد تبويب واجبات التسجيل"""
        registration_tab = QWidget()
        layout = QVBoxLayout(registration_tab)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # إطار واجبات التسجيل - عنوان المجموعة
        registration_frame = QGroupBox("واجبات التسجيل")
        registration_frame.setFont(QFont("Calibri", 16, QFont.Bold))
        
        reg_layout = QFormLayout(registration_frame)
        reg_layout.setSpacing(5)
        
        # حقول واجبات التسجيل - حقل المبلغ الإجمالي فقط
        self.total_amount_input = QLineEdit()
        self.total_amount_input.setPlaceholderText("أدخل المبلغ الإجمالي بالدرهم")
        self.total_amount_input.setFont(QFont("Calibri", self.REGISTRATION_INPUT_FONT_SIZE, QFont.Bold))
        self.total_amount_input.setMinimumHeight(self.REGISTRATION_INPUT_HEIGHT)
        # تطبيق تنسيق للمبلغ أثناء الكتابة
        self.total_amount_input.textChanged.connect(self.format_currency_input)

        reg_layout.addRow(self.create_styled_label("إجمالي المبلغ:"), self.total_amount_input)
        
        layout.addWidget(registration_frame)
        
        # إزالة أزرار العمليات من تبويب واجبات التسجيل
        layout.addStretch()
        
        self.tab_widget.addTab(registration_tab, "واجبات التسجيل")
        
    def setup_monthly_duties_tab(self):
        """إعداد تبويب الواجبات الشهرية"""
        monthly_tab = QWidget()
        layout = QVBoxLayout(monthly_tab)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # إطار الواجبات الشهرية - عنوان المجموعة
        monthly_frame = QGroupBox("الواجبات الشهرية")
        monthly_frame.setFont(QFont("Calibri", 17, QFont.Bold))
        
        monthly_layout = QVBoxLayout(monthly_frame)
        monthly_layout.setSpacing(15)
        
        # معلومات الواجب الشهري - حقل عملة مباشر
        form_layout = QFormLayout()
        form_layout.setSpacing(20)

        self.monthly_duty_input = QLineEdit()
        self.monthly_duty_input.setPlaceholderText("أدخل مبلغ الواجب الشهري بالدرهم")
        self.monthly_duty_input.setFont(QFont("Calibri", self.MONTHLY_INPUT_FONT_SIZE, QFont.Bold))
        self.monthly_duty_input.setMinimumHeight(self.MONTHLY_INPUT_HEIGHT)
        self.monthly_duty_input.textChanged.connect(self.calculate_total_monthly)
        self.monthly_duty_input.textChanged.connect(self.format_monthly_currency_input)
        self.monthly_duty_input.textChanged.connect(self.auto_fill_monthly_amount)

        form_layout.addRow(self.create_styled_label("الواجب الشهري:"), self.monthly_duty_input)

        # حقل المبلغ المدفوع (يتم تعبئته تلقائ<|im_start|> من الواجب الشهري)


        monthly_layout.addLayout(form_layout)
        


        # إنشاء قاموس فارغ للأشهر للحفاظ على التوافق مع الكود الموجود
        self.month_checkboxes = {}
        
        # عرض المبلغ النهائي
        total_layout = QFormLayout()
        self.total_monthly_display = QLabel("0.00 درهم")
        self.total_monthly_display.setFont(QFont("Calibri", 18, QFont.Bold))
        self.total_monthly_display.setAlignment(Qt.AlignCenter)
        self.total_monthly_display.setMinimumHeight(self.MONTHLY_INPUT_HEIGHT)
        self.total_monthly_display.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 8px;
                color: #2c3e50;
            }
        """)
        
        total_layout.addRow(self.create_styled_label("المبلغ النهائي:"), self.total_monthly_display)
        monthly_layout.addLayout(total_layout)
        
        layout.addWidget(monthly_frame)
        
        # إزالة زر الحفظ من تبويب الواجبات الشهرية
        layout.addStretch()
        
        self.tab_widget.addTab(monthly_tab, "الواجبات الشهرية")
        
    def create_contact_input(self):
        """إنشاء حقل إدخال خاص بمجموعة الاتصال"""
        input_field = QLineEdit()
        input_field.setFont(QFont("Calibri", self.CONTACT_INPUT_FONT_SIZE, QFont.Bold))
        return input_field

    def create_styled_input(self):
        """إنشاء حقل إدخال منسق للمجموعات الأخرى"""
        input_field = QLineEdit()
        input_field.setFont(QFont("Calibri", 15, QFont.Bold))
        return input_field
        
    def create_styled_label(self, text):
        """إنشاء تسمية منسقة - هنا تتحكم في حجم خط جميع المسميات (Labels)"""
        label = QLabel(text)
        label.setFont(QFont("Calibri", 15, QFont.Bold))  # غير الرقم 16 لتغيير حجم خط جميع المسميات
        return label
        
    def create_styled_button(self, text, color="#2196F3"):
        """إنشاء زر منسق بتصميم احترافي"""
        button = QPushButton(text)
        button.setFont(QFont("Calibri", 15, QFont.Bold))
        button.setMinimumHeight(45)
        button.setMinimumWidth(200)
        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color},
                    stop: 1 {self.darken_color(color, 20)}
                );
                color: white;
                border: none;
                border-radius: 12px;
                padding: 12px 20px;
                font-weight: bold;
                text-align: center;
            }}
            QPushButton:hover {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {self.lighten_color(color, 10)},
                    stop: 1 {color}
                );
            }}
            QPushButton:pressed {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {self.darken_color(color, 30)},
                    stop: 1 {self.darken_color(color, 40)}
                );
            }}
            QPushButton:disabled {{
                background: #CCCCCC;
                color: #666666;
            }}
        """)
        return button

    def create_schooling_button(self, text, color):
        """إنشاء زر خاص بمجموعة التمدرس"""
        button = QPushButton(text)
        button.setFont(QFont("Calibri", self.SCHOOLING_BUTTON_FONT_SIZE, QFont.Bold))
        button.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 15px;
                font-weight: bold;
                min-height: 30px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color, 20)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 40)};
            }}
        """)
        return button

    def lighten_color(self, color, amount):
        """تفتيح اللون"""
        if color.startswith('#'):
            color = color[1:]
        
        r = min(255, int(color[0:2], 16) + amount)
        g = min(255, int(color[2:4], 16) + amount)
        b = min(255, int(color[4:6], 16) + amount)
        
        return f"#{r:02x}{g:02x}{b:02x}"

    def darken_color(self, color, amount):
        """تغميق اللون"""
        if color.startswith('#'):
            color = color[1:]
        
        r = max(0, int(color[0:2], 16) - amount)
        g = max(0, int(color[2:4], 16) - amount)
        b = max(0, int(color[4:6], 16) - amount)
        
        return f"#{r:02x}{g:02x}{b:02x}"

    def get_groupbox_style(self):
        """الحصول على نمط المجموعات"""
        return """
            QGroupBox {
                color: #1976d2;
                border: 2px solid #1976d2;
                border-radius: 8px;
                padding-top: 20px;
                margin-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 15px 0 15px;
                background-color: white;
                font-size: 18px;
                font-weight: bold;
            }
        """
        
    def adjust_color(self, color, amount):
        """تعديل لون للحصول على تدرج"""
        # تحويل لون hex إلى RGB وتعديله
        if color.startswith('#'):
            color = color[1:]
        
        r = max(0, min(255, int(color[0:2], 16) + amount))
        g = max(0, min(255, int(color[2:4], 16) + amount))
        b = max(0, min(255, int(color[4:6], 16) + amount))
        
        return f"#{r:02x}{g:02x}{b:02x}"
        
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # إنشاء الجدول الموحد لجميع البيانات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS جدول_البيانات (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    
                    -- معلومات الاتصال
                    اسم_التلميذ TEXT NOT NULL,
                    رمز_التلميذ TEXT UNIQUE,
                    النوع TEXT,
                    رقم_الهاتف_الأول TEXT,
                    رقم_الهاتف_الثاني TEXT,
                    ملاحظات TEXT,
                    
                    -- معلومات التمدرس
                    السلك_التعليمي TEXT,
                    المادة_الدراسية TEXT,
                    اسم_المجموعة TEXT,
                    القسم TEXT,
                    المؤسسة_الاصلية TEXT,
                    
                    -- واجبات التسجيل
                    اجمالي_مبلغ_التسجيل REAL,
                    عدد_الاقساط INTEGER,
                    مبلغ_القسط REAL,
                    
                    -- الواجبات الشهرية
                    الواجب_الشهري REAL,
                    الاشهر_المحددة TEXT,
                    المبلغ_النهائي_الشهري REAL,
                    
                    -- تواريخ النظام
                    تاريخ_الانشاء DATETIME DEFAULT CURRENT_TIMESTAMP,
                    تاريخ_التحديث DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إعداد قاعدة البيانات: {str(e)}")
            
    def load_data(self):
        """تحميل البيانات من الجدول الموحد"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # تحميل آخر سجل من جدول البيانات الموحد
            cursor.execute("SELECT * FROM جدول_البيانات ORDER BY id DESC LIMIT 1")
            record = cursor.fetchone()
            
            if record:
                # ملء حقول معلومات الاتصال
                self.student_name_input.setText(record[1] or "")  # اسم_التلميذ
                self.student_code_input.setText(record[2] or "")  # رمز_التلميذ
                
                # تعيين النوع
                if record[3]:  # النوع
                    gender_index = self.gender_combo.findText(record[3])
                    if gender_index >= 0:
                        self.gender_combo.setCurrentIndex(gender_index)
                
                self.phone_input.setText(record[4] or "")  # رقم_الهاتف_الأول
                self.phone2_input.setText(record[5] or "")  # رقم_الهاتف_الثاني
                self.address_input.setPlainText(record[6] or "")  # ملاحظات
                
                # ملء حقول معلومات التمدرس
                cycle_text = record[7] or ""  # السلك_التعليمي
                if cycle_text:
                    cycle_index = self.cycle_combo.findText(cycle_text)
                    if cycle_index >= 0:
                        self.cycle_combo.setCurrentIndex(cycle_index)
                
                subject_text = record[8] or ""  # المادة_الدراسية
                if subject_text:
                    self.subject_combo.setCurrentText(subject_text)
                
                group_text = record[9] or ""  # اسم_المجموعة
                if group_text:
                    group_index = self.group_combo.findText(group_text)
                    if group_index >= 0:
                        self.group_combo.setCurrentIndex(group_index)
                
                section_text = record[10] or ""  # القسم
                if section_text:
                    self.section_combo.setCurrentText(section_text)
                
                institution_text = record[11] or ""  # المؤسسة_الاصلية
                if institution_text:
                    self.institution_combo.setCurrentText(institution_text)
                
                # ملء حقول واجبات التسجيل - إجمالي المبلغ فقط
                self.total_amount_input.setText(str(record[12] or 0))  # اجمالي_مبلغ_التسجيل
                
                # ملء حقول الواجبات الشهرية
                self.monthly_duty_input.setText(str(record[15] or 0))  # الواجب_الشهري
                
                # تحميل الأشهر المحددة
                if record[16]:  # الاشهر_المحددة
                    try:
                        selected_months = json.loads(record[16])
                        for month, checkbox in self.month_checkboxes.items():
                            checkbox.setChecked(month in selected_months)
                    except:
                        pass
                        
            conn.close()
            
            # تحديث الحسابات
            self.calculate_total_monthly()
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {str(e)}")

    def format_currency_input(self):
        """تنسيق إدخال العملة"""
        sender = self.sender()
        text = sender.text()
        
        # إزالة كل شيء عدا الأرقام والنقطة العشرية
        import re
        cleaned_text = re.sub(r'[^0-9.]', '', text)
        
        # التأكد من وجود نقطة عشرية واحدة فقط
        parts = cleaned_text.split('.')
        if len(parts) > 2:
            cleaned_text = parts[0] + '.' + ''.join(parts[1:])
        
        # تحديد النص إذا تغير
        if cleaned_text != text:
            cursor_pos = sender.cursorPosition()
            sender.setText(cleaned_text)
            sender.setCursorPosition(min(cursor_pos, len(cleaned_text)))
    
    def calculate_installment_amount(self):
        """حساب مبلغ القسط الواحد"""
        try:
            # الحصول على المبلغ الإجمالي من حقل النص
            total_amount_text = self.total_amount_input.text().strip()
            if not total_amount_text:
                total_amount = 0.0
            else:
                total_amount = float(total_amount_text)
            
            installments = self.installments_input.value()
            
            if installments > 0 and total_amount > 0:
                installment_amount = total_amount / installments
                self.installment_amount_display.setText(f"{installment_amount:.2f} درهم")
            else:
                self.installment_amount_display.setText("0.00 درهم")
        except ValueError:
            self.installment_amount_display.setText("0.00 درهم")
            
    def format_monthly_currency_input(self):
        """تنسيق إدخال العملة للواجب الشهري"""
        sender = self.sender()
        text = sender.text()
        
        # إزالة كل شيء عدا الأرقام والنقطة العشرية
        import re
        cleaned_text = re.sub(r'[^0-9.]', '', text)
        
        # التأكد من وجود نقطة عشرية واحدة فقط
       
        parts = cleaned_text.split('.')
        if len(parts) > 2:
            cleaned_text = parts[0] + '.' + ''.join(parts[1:])
        
        # تحديد النص إذا تغير
        if cleaned_text != text:
            cursor_pos = sender.cursorPosition()
            sender.setText(cleaned_text)
            sender.setCursorPosition(min(cursor_pos, len(cleaned_text)))
            
    def calculate_total_monthly(self):
        """حساب المبلغ النهائي للواجبات الشهرية"""
        try:
            # الحصول على مبلغ الواجب الشهري من حقل النص
            monthly_duty_text = self.monthly_duty_input.text().strip()
            if not monthly_duty_text:
                monthly_duty = 0.0
            else:
                monthly_duty = float(monthly_duty_text)
            
            selected_months = self.get_selected_months()
            total_monthly = monthly_duty * len(selected_months)
            self.total_monthly_display.setText(f"{total_monthly:.2f} درهم")
        except ValueError:
            self.total_monthly_display.setText("0.00 درهم")
        
    def get_selected_months(self):
        """الحصول على الأشهر المحددة"""
        selected_months = []
        for month, checkbox in self.month_checkboxes.items():
            if checkbox.isChecked():
                selected_months.append(month)
        return selected_months

    def add_new_group(self):
        """إضافة مجموعة جديدة - فتح نافذة إدارة المجموعات"""
        # جمع المجموعات الحالية
        current_groups = []
        for i in range(self.group_combo.count()):
            current_groups.append(self.group_combo.itemText(i))
          # فتح نافذة إدارة المجموعات
        dialog = GroupManagementDialog(self, current_groups)
        
        if dialog.exec_() == QDialog.Accepted:
            # لا حاجة لعمل شيء هنا لأن النافذة تحدث القائمة المنسدلة تلقائياً
            pass

    def add_new_institution(self):
        """إضافة مؤسسة جديدة"""
        from PyQt5.QtWidgets import QInputDialog
        
        text, ok = QInputDialog.getText(
            self, 
            'إضافة مؤسسة جديدة', 
            'اسم المؤسسة الجديدة:',
            QLineEdit.Normal,
            ''
        )
        
        if ok and text.strip():
            # التحقق من عدم وجود المؤسسة مسبقاً
            existing_items = [self.institution_combo.itemText(i) for i in range(self.institution_combo.count())]
            if text.strip() not in existing_items:
                self.institution_combo.addItem(text.strip())
                self.institution_combo.setCurrentText(text.strip())
                QMessageBox.information(self, "نجح", f"تم إضافة المؤسسة '{text.strip()}' بنجاح.")
            else:
                QMessageBox.warning(self, "تحذير", "هذه المؤسسة موجودة بالفعل.")

    def clear_contact_info(self):
        """مسح معلومات الاتصال"""
        reply = QMessageBox.question(
            self, "تأكيد", "هل أنت متأكد من مسح جميع معلومات الاتصال؟",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.student_name_input.clear()

            # مسح حقل رمز التلميذ فقط إذا لم يكن معطلاً (تلقائ<|im_start|>)
            if self.student_code_input.isEnabled():
                self.student_code_input.clear()

            self.gender_combo.setCurrentIndex(0)
            self.phone_input.clear()
            self.phone2_input.clear()
            self.address_input.clear()
    
        def clear_schooling_info(self):
            """مسح معلومات التمدرس"""
            reply = QMessageBox.question(
                self, "تأكيد", "هل أنت متأكد من مسح جميع معلومات التمدرس؟",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.cycle_combo.setCurrentIndex(0)
                self.subject_combo.setCurrentIndex(0)
                self.group_combo.setCurrentIndex(0)
                self.section_combo.setCurrentIndex(0)
                self.institution_combo.setCurrentIndex(0)

    def refresh_sections_combo(self):
        """تحديث قائمة الأقسام في ComboBox فقط"""
        try:
            conn = sqlite3.connect('data.db')
            cursor = conn.cursor()

            # جلب الأقسام المحدثة
            cursor.execute("SELECT DISTINCT القسم FROM جدول_المواد_والاقسام ORDER BY القسم")
            sections = [row[0] for row in cursor.fetchall()]

            # حفظ القيمة المحددة حالياً
            current_section = self.section_combo.currentText()

            # تنظيف قائمة الأقسام وإضافة الأقسام الجديدة
            self.section_combo.clear()
            if sections:
                self.section_combo.addItems(sections)
                # محاولة إعادة تحديد القيمة السابقة
                index = self.section_combo.findText(current_section)
                if index >= 0:
                    self.section_combo.setCurrentIndex(index)
            else:
                # في حالة عدم وجود أقسام، إضافة قيم افتراضية
                default_sections = [f"قسم / {str(i).zfill(2)}" for i in range(1, 10)]
                self.section_combo.addItems(default_sections)

            conn.close()
            print("✅ تم تحديث قائمة الأقسام بنجاح")

        except sqlite3.Error as e:
            print(f"خطأ في تحديث قائمة الأقسام: {str(e)}")
        except Exception as e:
            print(f"خطأ غير متوقع في تحديث قائمة الأقسام: {str(e)}")

    def load_sections_from_database(self):
        """تحميل الأقسام والأساتذة والمواد من جدول جدول_المواد_والاقسام"""
        try:
            conn = sqlite3.connect('data.db')
            cursor = conn.cursor()
            
            # استعلام لجلب البيانات من جدول المواد والأقسام
            cursor.execute("""
                SELECT DISTINCT اسم_الاستاذ, القسم, المادة 
                FROM جدول_المواد_والاقسام 
                ORDER BY القسم, اسم_الاستاذ, المادة
            """)
            
            sections_data = cursor.fetchall()
            
            # تفريغ الجدول الحالي
            self.sections_table.setRowCount(0)
            
            # ملء الجدول بالبيانات
            if sections_data:
                self.sections_table.setRowCount(len(sections_data))
                
                for row_idx, (teacher_name, section, subject) in enumerate(sections_data):
                    # اسم الأستاذ
                    teacher_item = QTableWidgetItem(str(teacher_name))
                    teacher_item.setTextAlignment(Qt.AlignCenter)
                    teacher_item.setFont(QFont("Calibri", 12))
                    self.sections_table.setItem(row_idx, 0, teacher_item)
                    
                    # القسم
                    section_item = QTableWidgetItem(str(section))
                    section_item.setTextAlignment(Qt.AlignCenter)
                    section_item.setFont(QFont("Calibri", 12, QFont.Bold))
                    self.sections_table.setItem(row_idx, 1, section_item)
                    
                    # المادة
                    subject_item = QTableWidgetItem(str(subject))
                    subject_item.setTextAlignment(Qt.AlignCenter)
                    subject_item.setFont(QFont("Calibri", 12))
                    self.sections_table.setItem(row_idx, 2, subject_item)
            
            # تحديث قائمة الأقسام في الـ ComboBox
            cursor.execute("SELECT DISTINCT القسم FROM جدول_المواد_والاقسام ORDER BY القسم")
            sections = [row[0] for row in cursor.fetchall()]
              # تنظيف قائمة الأقسام وإضافة الأقسام الجديدة
            self.section_combo.clear()
            if sections:
                self.section_combo.addItems(sections)
            else:
                # في حالة عدم وجود أقسام، إضافة قيم افتراضية
                default_sections = [f"قسم / {str(i).zfill(2)}" for i in range(1, 10)]
                self.section_combo.addItems(default_sections)
            
            conn.close()
            
            # تم تحميل البيانات بنجاح بدون إظهار رسالة
            
        except sqlite3.Error as e:
            QMessageBox.critical(self, "خطأ في قاعدة البيانات", 
                               f"فشل في تحميل البيانات من قاعدة البيانات:\n{str(e)}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع:\n{str(e)}")

    def print_registration_receipt(self):
        """طباعة توصيل واجبات التسجيل"""
        QMessageBox.information(self, "طباعة", "سيتم تطوير وظيفة طباعة توصيل واجبات التسجيل قريباً.")
        
    def print_monthly_receipt(self):
        """طباعة توصيل الواجبات الشهرية"""
        QMessageBox.information(self, "طباعة", "سيتم تطوير وظيفة طباعة توصيل الواجبات الشهرية قريباً.")
        
    def setup_add_mode(self):
        """إعداد النافذة لوضع الإضافة"""
        # مسح جميع الحقول
        self.student_name_input.clear()

        # مسح حقل رمز التلميذ فقط إذا لم يكن معطلاً (تلقائ<|im_start|>)
        if self.student_code_input.isEnabled():
            self.student_code_input.clear()

        self.gender_combo.setCurrentIndex(0)
        self.phone_input.clear()
        self.phone2_input.clear()
        self.address_input.clear()
          # إعداد معلومات التمدرس بالقيم الافتراضية
        self.cycle_combo.setCurrentIndex(0)
        self.section_combo.setCurrentIndex(0)
        self.institution_combo.setCurrentIndex(0)
        
        # مسح واجبات التسجيل - إجمالي المبلغ فقط
        self.total_amount_input.clear()
        
        # مسح الواجبات الشهرية
        self.monthly_duty_input.clear()
        self.total_monthly_display.setText("0.00 درهم")
        
        # إلغاء تحديد جميع الأشهر أولاً
        for checkbox in self.month_checkboxes.values():
            checkbox.setChecked(False)

        # تحديد الشهر الموافق للتاريخ الحالي تلقائ<|im_start|>
        self.select_current_month()
        
        # وضع التركيز على حقل اسم التلميذ
        self.student_name_input.setFocus()

    def select_current_month(self):
        """تحديد الشهر الموافق للتاريخ الحالي تلقائ<|im_start|>"""
        try:
            from datetime import datetime

            # الحصول على الشهر الحالي
            current_month = datetime.now().month

            # قائمة الأشهر بالعربية مطابقة لما في النافذة
            months_arabic = {
                1: "يناير", 2: "فبراير", 3: "مارس", 4: "أبريل",
                5: "مايو", 6: "يونيو", 7: "يوليو", 8: "أغسطس",
                9: "سبتمبر", 10: "أكتوبر", 11: "نوفمبر", 12: "ديسمبر"
            }

            # تحديد الشهر الحالي
            current_month_name = months_arabic.get(current_month)
            if current_month_name and current_month_name in self.month_checkboxes:
                self.month_checkboxes[current_month_name].setChecked(True)
                print(f"تم تحديد الشهر الحالي تلقائ<|im_start|>: {current_month_name}")

                # تحديث المبلغ النهائي
                self.update_total_monthly()

        except Exception as e:
            print(f"خطأ في تحديد الشهر الحالي: {str(e)}")

    def auto_fill_monthly_amount(self):
        """تعبئة المبلغ المدفوع تلقائ<|im_start|> من الواجب الشهري"""
        try:
            monthly_duty = self.monthly_duty_input.text().strip()
            if monthly_duty and float(monthly_duty) > 0:
                # تعبئة المبلغ المدفوع بنفس قيمة الواجب الشهري إذا كان فارغاً
                if hasattr(self, 'paid_amount_input'):
                    current_paid = self.paid_amount_input.text().strip()
                    if not current_paid:  # فقط إذا كان الحقل فارغاً
                        self.paid_amount_input.setText(monthly_duty)
                        print(f"تم تعبئة المبلغ المدفوع تلقائ<|im_start|>: {monthly_duty}")
            elif not monthly_duty:
                # مسح المبلغ المدفوع إذا تم مسح الواجب الشهري
                if hasattr(self, 'paid_amount_input'):
                    self.paid_amount_input.clear()
        except Exception as e:
            print(f"خطأ في تعبئة المبلغ المدفوع تلقائ<|im_start|>: {str(e)}")

# تشغيل التطبيق للاختبار
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    window = MonthlyDutiesWindow()
    window.show()

    sys.exit(app.exec_())
