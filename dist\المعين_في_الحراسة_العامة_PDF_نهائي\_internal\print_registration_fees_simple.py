#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تقرير واجبات التسجيل - نسخة مبسطة بدون مشاكل الخط العربي
"""

import sqlite3
import os
from datetime import datetime
from fpdf import FPDF
import subprocess
import sys

# إعدادات التقرير
ROW_HEIGHT_HEADER = 8
ROW_HEIGHT_TABLE1 = 8
ROW_HEIGHT_TABLE2 = 8
COL_WIDTHS_TABLE1 = [40, 50, 40, 60]  # عرض أعمدة الجدول الأول
COL_WIDTHS_TABLE2 = [15, 50, 40, 30, 30, 25]  # عرض أعمدة الجدول الثاني
COL_WIDTHS_TABLE3 = [50, 50, 50, 40]  # عرض أعمدة الجدول الثالث
TABLE2_HEADERS = ['رقم', 'اسم التلميذ', 'نوع الدفع', 'المبلغ المدفوع', 'تاريخ الدفع', 'طريقة الدفع']
TABLE3_HEADERS = ['إجمالي المدفوع', 'عدد الدفعات', 'حصة الأستاذ(ة)', 'نسبة الحصة']

class SimplePDF(FPDF):
    """فئة PDF مبسطة بدون أحرف عربية"""
    
    def __init__(self):
        super().__init__()
        self.set_auto_page_break(auto=True, margin=15)
        
    def safe_text(self, text):
        """تحويل النص العربي إلى نص آمن للـ PDF"""
        if not text:
            return ""

        text_str = str(text)

        # قاموس تحويل الأحرف العربية إلى أحرف لاتينية
        arabic_to_latin = {
            # الأحرف العربية الأساسية
            'ا': 'a', 'أ': 'a', 'إ': 'i', 'آ': 'aa',
            'ب': 'b', 'ت': 't', 'ث': 'th', 'ج': 'j', 'ح': 'h', 'خ': 'kh',
            'د': 'd', 'ذ': 'dh', 'ر': 'r', 'ز': 'z', 'س': 's', 'ش': 'sh',
            'ص': 's', 'ض': 'd', 'ط': 't', 'ظ': 'z', 'ع': 'a', 'غ': 'gh',
            'ف': 'f', 'ق': 'q', 'ك': 'k', 'ل': 'l', 'م': 'm', 'ن': 'n',
            'ه': 'h', 'و': 'w', 'ي': 'y', 'ى': 'a', 'ة': 'h', 'ء': 'a',
            'ؤ': 'o', 'ئ': 'e',
            # الأرقام العربية
            '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
            '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9',
            # علامات الترقيم
            '،': ',', '؛': ';', '؟': '?', '٪': '%',
            # مسافات وأحرف خاصة
            ' ': ' ', '/': '/', '-': '-', '_': '_', '.': '.', ':': ':'
        }

        safe_text = ""
        for char in text_str:
            if char in arabic_to_latin:
                safe_text += arabic_to_latin[char]
            elif ord(char) < 128:  # ASCII characters
                safe_text += char
            else:
                safe_text += char  # Keep other characters as is

        return safe_text
    
    def set_title_font(self, size=15):
        """تعيين خط العناوين"""
        self.set_font('Arial', 'B', size)
    
    def set_detail_font(self, size=13):
        """تعيين خط التفاصيل"""
        self.set_font('Arial', 'B', size)
    
    def set_normal_font(self, size=10):
        """تعيين الخط العادي"""
        self.set_font('Arial', '', size)

def get_section_info_for_registration(db_path, section):
    """جلب معلومات القسم لتقرير واجبات التسجيل"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # جلب معلومات القسم من جدول registration_fees مباشرة
        cursor.execute("""
            SELECT DISTINCT
                COALESCE(rf.اسم_الاستاذ, mat.اسم_الاستاذ, 'غير محدد') as teacher_name,
                COALESCE(rf.المادة, mat.المادة, 'غير محدد') as subject_name,
                COALESCE(mat.المجموعة, 'غير محدد') as group_name,
                COALESCE(mat.نسبة_الواجبات, 100) as duties_percentage
            FROM registration_fees rf
            LEFT JOIN جدول_المواد_والاقسام mat ON COALESCE(rf.القسم, '') = mat.القسم
            WHERE COALESCE(rf.القسم, '') = ?
            ORDER BY subject_name
            LIMIT 1
        """, (section,))
        
        section_subjects_from_fees = cursor.fetchall()
        
        # إذا لم توجد بيانات في registration_fees، استخدم جدول المواد والأقسام كبديل
        if not section_subjects_from_fees:
            cursor.execute("""
                SELECT اسم_الاستاذ, المادة, المجموعة, نسبة_الواجبات
                FROM جدول_المواد_والاقسام
                WHERE القسم = ?
                ORDER BY المادة
                LIMIT 1
            """, (section,))
            section_subjects = cursor.fetchall()
        else:
            section_subjects = section_subjects_from_fees

        # جلب إحصائيات التلاميذ من جدول registration_fees مباشرة
        print(f"Getting student statistics from registration_fees for {section}")

        cursor.execute("""
            SELECT COUNT(DISTINCT rf.student_id) as total_students,
                   COUNT(DISTINCT CASE WHEN COALESCE(rf.النوع, jb.النوع) = 'ذكر' THEN rf.student_id END) as male_count,
                   COUNT(DISTINCT CASE WHEN COALESCE(rf.النوع, jb.النوع) = 'أنثى' THEN rf.student_id END) as female_count
            FROM registration_fees rf
            LEFT JOIN جدول_البيانات jb ON rf.student_id = jb.id
            WHERE COALESCE(rf.القسم, jb.القسم) = ?
        """, (section,))

        fees_stats = cursor.fetchone()

        if fees_stats and fees_stats[0] > 0:
            print(f"Got statistics from registration_fees: {fees_stats[0]} students")
            student_stats = fees_stats
        else:
            print(f"No statistics in registration_fees, using live data")
            cursor.execute("""
                SELECT COUNT(*) as total_students,
                       COUNT(CASE WHEN النوع = 'ذكر' THEN 1 END) as male_count,
                       COUNT(CASE WHEN النوع = 'أنثى' THEN 1 END) as female_count
                FROM جدول_البيانات
                WHERE القسم = ?
            """, (section,))

            student_stats = cursor.fetchone()

        conn.close()

        return {
            'section_subjects': section_subjects,
            'student_stats': student_stats
        }

    except Exception as e:
        print(f"Error getting section info: {str(e)}")
        return None

def get_registration_fees_by_section(db_path, section):
    """جلب جميع واجبات التسجيل للقسم من جدول registration_fees مباشرة"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        print(f"Getting all registration fees from registration_fees for {section}")

        cursor.execute("""
            SELECT
                jb.اسم_التلميذ,
                jb.رمز_التلميذ,
                rf.payment_type,
                rf.amount_paid,
                rf.payment_date,
                rf.payment_method,
                rf.notes,
                COALESCE(rf.اسم_الاستاذ, 'Not Specified') as teacher_name
            FROM registration_fees rf
            JOIN جدول_البيانات jb ON rf.student_id = jb.id
            WHERE COALESCE(rf.القسم, jb.القسم) = ?
            ORDER BY jb.اسم_التلميذ, rf.payment_date DESC
        """, (section,))

        registration_fees = cursor.fetchall()
        conn.close()

        print(f"Retrieved {len(registration_fees)} records from registration_fees")
        return registration_fees

    except Exception as e:
        print(f"Error getting registration fees: {str(e)}")
        return []

def generate_registration_fees_report(logo_path, section_info, registration_fees, section, output_path):
    """إنشاء تقرير واجبات التسجيل"""
    pdf = SimplePDF()
    margin = 10
    usable_w = pdf.w - 2 * margin

    pdf.add_page()
    y = pdf.get_y()

    # إضافة الشعار إذا كان متوفراً
    if logo_path and os.path.exists(logo_path):
        logo_w, logo_h = 60, 30
        x_logo = (pdf.w - logo_w) / 2
        pdf.image(logo_path, x=x_logo, y=y, w=logo_w, h=logo_h)
        y += logo_h + 5

    # عنوان التقرير
    pdf.set_draw_color(0, 51, 102)
    pdf.set_line_width(0.5)
    FIXED_BOX_HEIGHT = 12

    current_year = datetime.now().year
    title_text = f"تقرير واجبات التسجيل - القسم: {section} - السنة: {current_year}"

    pdf.set_text_color(0, 51, 102)
    pdf.set_xy(margin, y)
    pdf.set_title_font(15)
    pdf.cell(usable_w, FIXED_BOX_HEIGHT, pdf.safe_text(title_text), border=1, align='C')

    pdf.set_text_color(0, 0, 0)
    y += FIXED_BOX_HEIGHT + 5

    # الجدول الأول: معلومات القسم
    pdf.set_title_font(14)
    pdf.set_text_color(0, 100, 0)
    pdf.set_xy(margin, y)
    pdf.cell(usable_w, 8, pdf.safe_text('معلومات القسم والمواد'), border=0, align='C')
    pdf.set_text_color(0, 0, 0)
    y += 10

    cols1 = COL_WIDTHS_TABLE1

    # معلومات إحصائية عن القسم
    if section_info and section_info['student_stats']:
        stats = section_info['student_stats']

        if section_info['section_subjects']:
            first_subject = section_info['section_subjects'][0]
            teacher_name = first_subject[0] or 'غير محدد'
            subject_name = first_subject[1] or 'غير محدد'
            group_name = first_subject[2] or 'غير محدد'
            duties_percent = str(first_subject[3]) + '%' if first_subject[3] else '100%'

            section_info_rows = [
                [subject_name, 'المادة', str(stats[0]), 'إجمالي التلاميذ'],
                [teacher_name, 'الأستاذ(ة)', str(stats[1]), 'عدد الذكور'],
                [current_year, 'السنة المحددة', str(stats[2]), 'عدد الإناث'],
                [duties_percent, 'حصة الأستاذ(ة) من المبلغ', group_name, 'المجموعة']
            ]
        else:
            section_info_rows = [
                ['غير محدد', 'المادة', str(stats[0]), 'إجمالي التلاميذ'],
                ['غير محدد', 'الأستاذ(ة)', str(stats[1]), 'عدد الذكور'],
                [current_year, 'السنة المحددة', str(stats[2]), 'عدد الإناث'],
                ['100%', 'حصة الأستاذ(ة) من المبلغ', 'غير محدد', 'المجموعة']
            ]
    else:
        section_info_rows = [
            ['غير محدد', 'المادة', '0', 'إجمالي التلاميذ'],
            ['غير محدد', 'الأستاذ(ة)', '0', 'عدد الذكور'],
            [current_year, 'السنة المحددة', '0', 'عدد الإناث'],
            ['100%', 'حصة الأستاذ(ة) من المبلغ', 'غير محدد', 'المجموعة']
        ]

    pdf.set_detail_font(13)
    pdf.set_fill_color(230, 240, 255)

    # رسم صفوف معلومات القسم
    for row in section_info_rows:
        x = margin
        for i, cell in enumerate(row):
            pdf.set_xy(x, y)
            fill = i % 2 == 1
            align = 'C' if i % 2 == 1 else 'L'
            pdf.cell(cols1[i], ROW_HEIGHT_TABLE1, pdf.safe_text(str(cell)), border=1, align=align, fill=fill)
            x += cols1[i]
        y += ROW_HEIGHT_TABLE1

    y += 10

    # الجدول الثاني: واجبات التسجيل
    pdf.set_title_font(14)
    pdf.set_text_color(128, 0, 128)
    pdf.set_xy(margin, y)
    pdf.cell(usable_w, 8, pdf.safe_text(f'واجبات التسجيل - {current_year}'), border=0, align='C')
    pdf.set_text_color(0, 0, 0)
    y += 10

    # رسم جدول واجبات التسجيل
    cols2 = COL_WIDTHS_TABLE2

    pdf.set_detail_font(12)
    pdf.set_fill_color(255, 200, 255)

    # رسم رأس الجدول
    x = margin
    for i, header in enumerate(TABLE2_HEADERS):
        pdf.set_xy(x, y)
        pdf.cell(cols2[i], ROW_HEIGHT_HEADER, pdf.safe_text(header), border=1, align='C', fill=True)
        x += cols2[i]

    y += ROW_HEIGHT_HEADER
    pdf.set_normal_font(10)

    # محتوى جدول واجبات التسجيل
    if registration_fees:
        for i, fee in enumerate(registration_fees, 1):
            x = margin
            student_name = fee[0] or 'Unknown'
            payment_type = fee[2] or 'Unknown'
            amount_paid = float(fee[3]) if fee[3] else 0
            payment_date = fee[4] or 'Unknown'
            payment_method = fee[5] or 'Cash'

            data = [
                str(i),
                student_name,
                payment_type,
                f'{amount_paid:.2f}',
                payment_date,
                payment_method
            ]

            # تلوين متناوب للصفوف
            if i % 2 == 0:
                pdf.set_fill_color(245, 245, 245)
            else:
                pdf.set_fill_color(255, 255, 255)

            for j, cell in enumerate(data):
                pdf.set_xy(x, y)
                pdf.cell(cols2[j], ROW_HEIGHT_TABLE2, pdf.safe_text(str(cell)), border=1, align='C', fill=True)
                x += cols2[j]

            y += ROW_HEIGHT_TABLE2

            # الانتقال إلى صفحة جديدة عند الحاجة
            if y > pdf.h - 50:
                pdf.add_page()
                y = pdf.get_y()
    else:
        # إذا لم توجد واجبات تسجيل
        pdf.set_xy(margin, y)
        pdf.set_fill_color(255, 245, 245)
        pdf.cell(sum(cols2), ROW_HEIGHT_TABLE2, pdf.safe_text('لا توجد واجبات تسجيل مسجلة لهذا القسم'), border=1, align='C', fill=True)
        y += ROW_HEIGHT_TABLE2

    y += 10

    # الجدول الثالث: مجموع المبالغ وحصة الأستاذ
    pdf.set_title_font(14)
    pdf.set_text_color(0, 100, 100)
    pdf.set_xy(margin, y)
    pdf.cell(usable_w, 8, pdf.safe_text('مجموع المبالغ وحصة الأستاذ(ة)'), border=0, align='C')
    pdf.set_text_color(0, 0, 0)
    y += 10

    cols3 = COL_WIDTHS_TABLE3

    # حساب المجاميع
    total_paid = 0
    total_count = 0

    if registration_fees:
        for fee in registration_fees:
            total_paid += float(fee[3]) if fee[3] else 0
            total_count += 1

    # حساب حصة الأستاذ
    teacher_percentage = 100  # افتراضي
    if section_info and section_info['section_subjects']:
        teacher_percentage = section_info['section_subjects'][0][3] if section_info['section_subjects'][0][3] else 100

    teacher_share = (total_paid * teacher_percentage) / 100

    pdf.set_detail_font(13)
    pdf.set_fill_color(200, 255, 200)

    # رسم رأس الجدول الثالث
    x = margin
    for i, header in enumerate(TABLE3_HEADERS):
        pdf.set_xy(x, y)
        pdf.cell(cols3[i], ROW_HEIGHT_HEADER, pdf.safe_text(header), border=1, align='C', fill=True)
        x += cols3[i]

    y += ROW_HEIGHT_HEADER

    # رسم صف البيانات
    x = margin
    summary_data = [
        f'{total_paid:.2f}',
        str(total_count),
        f'{teacher_share:.2f}',
        f'{teacher_percentage}%'
    ]

    pdf.set_fill_color(220, 255, 220)
    for i, cell in enumerate(summary_data):
        pdf.set_xy(x, y)
        pdf.cell(cols3[i], ROW_HEIGHT_TABLE2, pdf.safe_text(cell), border=1, align='C', fill=True)
        x += cols3[i]

    # حفظ الملف
    pdf.output(output_path)
    print(f"Registration fees report created: {output_path}")

def print_registration_fees_report(parent=None, section=None):
    """
    دالة لإنشاء تقرير واجبات التسجيل
    """
    try:
        # التحقق من المعاملات المطلوبة
        if not section:
            return False, "", "لم يتم تحديد القسم"

        # تحديد السنة الحالية
        current_year = datetime.now().year

        # مسار قاعدة البيانات
        db_path = "data.db"

        # التحقق من وجود قاعدة البيانات
        if not os.path.exists(db_path):
            return False, "", f"لا يمكن العثور على قاعدة البيانات: {db_path}"

        print(f"🔍 إنشاء تقرير واجبات التسجيل لـ {section} - {current_year}")

        # جلب معلومات القسم
        section_info = get_section_info_for_registration(db_path, section)
        if not section_info:
            return False, "", "فشل في جلب معلومات القسم"

        # جلب جميع واجبات التسجيل للقسم
        registration_fees = get_registration_fees_by_section(db_path, section)

        # تحديد مسار الحفظ (استخدام أحرف آمنة لاسم الملف)
        safe_section = section.replace("/", "_").replace("\\", "_").replace(" ", "_")
        output_filename = f"Registration_Fees_Report_{safe_section}_{current_year}.pdf"
        output_path = os.path.join(os.getcwd(), output_filename)

        # مسار الشعار
        logo_path = "logo.png"

        # إنشاء التقرير
        generate_registration_fees_report(
            logo_path=logo_path,
            section_info=section_info,
            registration_fees=registration_fees,
            section=section,
            output_path=output_path
        )

        # فتح الملف تلقائياً
        try:
            if sys.platform == 'win32':
                os.startfile(output_path)
            elif sys.platform == 'darwin':  # macOS
                subprocess.call(['open', output_path])
            else:  # Linux
                subprocess.call(['xdg-open', output_path])
        except Exception as e:
            return True, output_path, f"تم إنشاء التقرير ولكن تعذر فتح الملف: {str(e)}"

        return True, output_path, "تم إنشاء التقرير بنجاح"

    except Exception as e:
        print(f"خطأ في إنشاء تقرير واجبات التسجيل: {str(e)}")
        return False, "", f"خطأ في إنشاء التقرير: {str(e)}"

if __name__ == "__main__":
    # اختبار التقرير
    try:
        success, output_path, message = print_registration_fees_report(
            section="قسم / 01"
        )
        
        print(f"Result: {success}")
        print(f"Path: {output_path}")
        print(f"Message: {message}")
        
    except Exception as e:
        print(f"Test error: {str(e)}")
