#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

# فحص بنية جدول البيانات
def check_table_structure():
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # فحص بنية جدول البيانات
        cursor.execute('PRAGMA table_info(جدول_البيانات)')
        columns = cursor.fetchall()
        
        print("أعمدة جدول البيانات:")
        print("-" * 50)
        for col in columns:
            print(f"{col[0]}: {col[1]} ({col[2]})")
        
        # فحص إذا كان عمود اسم_المجموعة موجود
        column_names = [col[1] for col in columns]
        if 'اسم_المجموعة' in column_names:
            print("\n✅ عمود 'اسم_المجموعة' موجود في الجدول")
        else:
            print("\n❌ عمود 'اسم_المجموعة' غير موجود في الجدول")
            print("أعمدة موجودة:", column_names)
        
        conn.close()
        
    except Exception as e:
        print(f"خطأ في فحص بنية الجدول: {e}")

if __name__ == "__main__":
    check_table_structure()
