#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نافذة إدارة المهام والمواعيد
"""

import sys
import os

def test_sub4_window():
    """اختبار النافذة الجديدة"""
    try:
        print("🧪 اختبار نافذة إدارة المهام والمواعيد...")
        print("=" * 60)
        
        # استيراد النافذة
        from sub4_window import TasksAndAppointmentsWindow
        print("✅ تم استيراد النافذة بنجاح")
        
        # اختبار إنشاء النافذة
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء النافذة
        window = TasksAndAppointmentsWindow()
        print("✅ تم إنشاء النافذة بنجاح")
        
        # التحقق من وجود قاعدة البيانات
        if os.path.exists("data.db"):
            print("✅ قاعدة البيانات موجودة")
        else:
            print("⚠️ قاعدة البيانات غير موجودة - سيتم إنشاؤها")
        
        # التحقق من وجود التبويبات
        if hasattr(window, 'tab_widget'):
            print("✅ التبويبات موجودة")
            tab_count = window.tab_widget.count()
            print(f"📋 عدد التبويبات: {tab_count}")
            
            for i in range(tab_count):
                tab_text = window.tab_widget.tabText(i)
                print(f"   التبويب {i+1}: {tab_text}")
        else:
            print("❌ التبويبات مفقودة")
            return False
        
        # التحقق من وجود الجدول
        if hasattr(window, 'tasks_table'):
            print("✅ جدول المهام موجود")
        else:
            print("❌ جدول المهام مفقود")
            return False
        
        # التحقق من وجود الحقول
        required_fields = [
            'title_input',
            'type_combo',
            'priority_combo',
            'status_combo',
            'start_date',
            'end_date',
            'start_time',
            'end_time',
            'location_input',
            'attendees_input',
            'description_input',
            'notes_input'
        ]
        
        for field in required_fields:
            if hasattr(window, field):
                print(f"✅ الحقل {field} موجود")
            else:
                print(f"❌ الحقل {field} مفقود")
                return False
        
        # التحقق من وجود الأزرار
        required_buttons = [
            'add_button',
            'update_button',
            'cancel_edit_button',
            'clear_button'
        ]
        
        for button in required_buttons:
            if hasattr(window, button):
                print(f"✅ الزر {button} موجود")
            else:
                print(f"❌ الزر {button} مفقود")
                return False
        
        # التحقق من وجود الدوال المطلوبة
        required_methods = [
            'add_new_task',
            'update_task',
            'delete_selected_task',
            'edit_selected_task',
            'clear_form',
            'load_data',
            'generate_tasks_report',
            'generate_type_report',
            'generate_priority_report',
            'generate_status_report',
            'generate_comprehensive_report'
        ]
        
        for method in required_methods:
            if hasattr(window, method):
                print(f"✅ الدالة {method} موجودة")
            else:
                print(f"❌ الدالة {method} مفقودة")
                return False
        
        # اختبار تحميل البيانات
        try:
            window.load_data()
            print("✅ تحميل البيانات يعمل")
        except Exception as e:
            print(f"⚠️ تحميل البيانات: {e}")
        
        # عرض النافذة للاختبار البصري
        window.show()
        print("✅ تم عرض النافذة للاختبار")
        
        print("\n🎉 جميع الاختبارات نجحت!")
        print("💡 يمكنك الآن:")
        print("   • إضافة مهام ومواعيد جديدة")
        print("   • إدارة المهام الموجودة")
        print("   • إنشاء تقارير متنوعة")
        print("   • تتبع زيارات أولياء الأمور")
        print("   • جدولة الاجتماعات")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_database_structure():
    """اختبار هيكل قاعدة البيانات"""
    try:
        print("\n🗄️ اختبار هيكل قاعدة البيانات...")
        print("=" * 40)
        
        import sqlite3
        
        if not os.path.exists("data.db"):
            print("⚠️ قاعدة البيانات غير موجودة")
            return True
        
        conn = sqlite3.connect("data.db")
        cursor = conn.cursor()
        
        # فحص وجود جدول المهام
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='tasks_appointments'
        """)
        
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("✅ جدول tasks_appointments موجود")
            
            # فحص أعمدة الجدول
            cursor.execute("PRAGMA table_info(tasks_appointments)")
            columns = cursor.fetchall()
            
            print(f"📋 عدد الأعمدة: {len(columns)}")
            for col in columns:
                print(f"   {col[1]} ({col[2]})")
            
            # فحص عدد السجلات
            cursor.execute("SELECT COUNT(*) FROM tasks_appointments")
            count = cursor.fetchone()[0]
            print(f"📊 عدد السجلات: {count}")
            
        else:
            print("⚠️ جدول tasks_appointments غير موجود - سيتم إنشاؤه عند تشغيل النافذة")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔍 اختبار نافذة إدارة المهام والمواعيد")
    print("=" * 60)
    print(f"🐍 Python: {sys.version}")
    print(f"📁 المجلد: {os.getcwd()}")
    
    # اختبار هيكل قاعدة البيانات
    db_success = test_database_structure()
    
    # اختبار النافذة
    window_success = test_sub4_window()
    
    print("\n" + "=" * 60)
    print("📊 ملخص الاختبارات:")
    print("=" * 60)
    
    if db_success and window_success:
        print("✅ جميع الاختبارات نجحت!")
        print("🚀 نافذة إدارة المهام والمواعيد جاهزة للاستخدام")
    else:
        print("❌ بعض الاختبارات فشلت!")
        print("🔧 راجع الأخطاء أعلاه")
    
    print("\n📋 المميزات الجديدة:")
    print("• إدارة المهام والمواعيد")
    print("• زيارات أولياء الأمور")
    print("• جدولة الاجتماعات")
    print("• تصنيف حسب الأولوية والحالة")
    print("• ذاكرة الحقول (العنوان، الوصف، الملاحظات)")
    print("• زر طباعة المهمة المحددة مع خيارات:")
    print("  - 🖨️ طباعة حرارية (مثل وصل الأداء تماماً)")
    print("  - 📄 طباعة PDF")
    print("  - جدول معكوس (القيمة ثم التسمية)")
    print("  - نفس حجم الورق والتصميم")
    print("• تبويب إدارة المهام في النافذة الرئيسية")
    print("• تقارير PDF أنيقة وجميلة مع ألوان")
    print("• تصميم التقارير مثل print111.py:")
    print("  - الشعار")
    print("  - اسم المؤسسة")
    print("  - عنوان التقرير")
    print("  - الجداول الملونة")
    print("  - توقيع المسؤول")
    print("  - تاريخ الطباعة")
    print("• خط Calibri 13 أسود غامق للجداول")
    print("• خط Calibri 14 أزرق غامق للعناوين")
    print("• حفظ التقارير على سطح المكتب")
    print("• فتح التقرير تلقائياً بعد الإنشاء")
    print("• واجهة جميلة ومبسطة (بدون تخطيط رئيسي)")
    print("• ألوان جميلة ومتدرجة")
    print("• دعم النصوص العربية")
    print("• قاعدة بيانات متكاملة")
    print("• مدمج في ملف التحزيم ultimate_pdf_build.spec")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
    
    input("\nاضغط Enter للخروج...")
