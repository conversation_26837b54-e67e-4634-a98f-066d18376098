import sqlite3
import os

print("إصلاح قاعدة البيانات...")

# حذف قاعدة البيانات القديمة إذا كانت موجودة
if os.path.exists("data.db"):
    os.remove("data.db")
    print("تم حذف قاعدة البيانات القديمة")

# إنشاء قاعدة بيانات جديدة
conn = sqlite3.connect("data.db")
cursor = conn.cursor()

# إنشاء جدول السنوات المالية
cursor.execute("""
    CREATE TABLE السنوات_المالية (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        السنة_المالية INTEGER UNIQUE NOT NULL,
        تاريخ_البداية DATE NOT NULL,
        تاريخ_النهاية DATE NOT NULL,
        الحالة TEXT DEFAULT 'نشطة',
        ملاحظات TEXT,
        تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
""")

# إنشاء جدول الموازنة السنوية بالبنية الصحيحة
cursor.execute("""
    CREATE TABLE الموازنة_السنوية (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        السنة_المالية INTEGER NOT NULL,
        نوع_البند TEXT NOT NULL,
        اسم_البند TEXT NOT NULL,
        المبلغ_المتوقع REAL NOT NULL DEFAULT 0,
        المبلغ_الفعلي REAL DEFAULT 0,
        النسبة_المحققة REAL DEFAULT 0,
        الحالة TEXT DEFAULT 'نشط',
        تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (السنة_المالية) REFERENCES السنوات_المالية(السنة_المالية),
        UNIQUE(السنة_المالية, نوع_البند, اسم_البند)
    )
""")

# إنشاء جدول إعدادات النظام
cursor.execute("""
    CREATE TABLE IF NOT EXISTS إعدادات_النظام (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        اسم_الإعداد TEXT UNIQUE NOT NULL,
        قيمة_الإعداد TEXT NOT NULL,
        وصف_الإعداد TEXT,
        تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
""")

# إضافة السنوات المالية
years_data = [
    (2024, '2024-09-01', '2025-08-31', 'نشطة'),
    (2025, '2025-09-01', '2026-08-31', 'نشطة'),
    (2026, '2026-09-01', '2027-08-31', 'غير نشطة')
]

cursor.executemany("""
    INSERT INTO السنوات_المالية (السنة_المالية, تاريخ_البداية, تاريخ_النهاية, الحالة)
    VALUES (?, ?, ?, ?)
""", years_data)

# إضافة بيانات الموازنة التجريبية
budget_data = [
    # إيرادات 2024
    (2024, "إيرادات", "registration_fees", 50000, 0),
    (2024, "إيرادات", "monthly_duties", 300000, 0),
    (2024, "إيرادات", "other_revenue", 25000, 0),
    # مصاريف 2024
    (2024, "مصاريف", "رواتب", 200000, 0),
    (2024, "مصاريف", "كراء", 60000, 0),
    (2024, "مصاريف", "فواتير وأقساط", 30000, 0),
    (2024, "مصاريف", "معدات", 15000, 0),
    (2024, "مصاريف", "صيانة وتجهيز", 10000, 0),
    (2024, "مصاريف", "إعلانات", 5000, 0),
    (2024, "مصاريف", "أدوات وأجهزة", 8000, 0),
    (2024, "مصاريف", "أخرى", 12000, 0),
    # إيرادات 2025
    (2025, "إيرادات", "registration_fees", 55000, 0),
    (2025, "إيرادات", "monthly_duties", 320000, 0),
    (2025, "إيرادات", "other_revenue", 30000, 0),
    # مصاريف 2025
    (2025, "مصاريف", "رواتب", 220000, 0),
    (2025, "مصاريف", "كراء", 65000, 0),
    (2025, "مصاريف", "فواتير وأقساط", 35000, 0),
    (2025, "مصاريف", "معدات", 18000, 0),
    (2025, "مصاريف", "صيانة وتجهيز", 12000, 0),
    (2025, "مصاريف", "إعلانات", 7000, 0),
    (2025, "مصاريف", "أدوات وأجهزة", 10000, 0),
    (2025, "مصاريف", "أخرى", 15000, 0)
]

cursor.executemany("""
    INSERT INTO الموازنة_السنوية 
    (السنة_المالية, نوع_البند, اسم_البند, المبلغ_المتوقع, المبلغ_الفعلي)
    VALUES (?, ?, ?, ?, ?)
""", budget_data)

# إضافة إعداد السنة المالية المختارة
cursor.execute("""
    INSERT INTO إعدادات_النظام 
    (اسم_الإعداد, قيمة_الإعداد, وصف_الإعداد)
    VALUES (?, ?, ?)
""", ("السنة_المالية_المختارة", "2025", "السنة المالية المختارة افتراضياً"))

conn.commit()
conn.close()

print("تم إصلاح قاعدة البيانات بنجاح!")
print("يمكنك الآن تشغيل البرنامج بدون أخطاء")
