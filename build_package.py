#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف تحزيم البرنامج الموحد
يستخدم ملف ultimate_pdf_build.spec فقط
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 60)
    print("🚀 تحزيم البرنامج - المعين في الحراسة العامة")
    print("=" * 60)
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 المجلد: {os.getcwd()}")
    print(f"🐍 Python: {sys.version}")
    print("=" * 60)

def check_requirements():
    """فحص المتطلبات"""
    print("\n🔍 فحص المتطلبات...")
    
    # فحص PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller غير مثبت")
        return False
    
    # فحص pywin32
    try:
        import win32print
        print("✅ pywin32: مثبت")
    except ImportError:
        print("❌ pywin32 غير مثبت")
        return False
    
    # فحص ملف التحزيم
    if not os.path.exists("ultimate_pdf_build.spec"):
        print("❌ ملف ultimate_pdf_build.spec غير موجود")
        return False
    else:
        print("✅ ملف ultimate_pdf_build.spec موجود")
    
    # فحص الملفات الأساسية
    essential_files = [
        "main_window.py",
        "01.ico",
        "data.db"
    ]
    
    for file in essential_files:
        if os.path.exists(file):
            print(f"✅ {file}: موجود")
        else:
            print(f"⚠️ {file}: غير موجود")
    
    return True

def clean_build_dirs():
    """تنظيف مجلدات البناء السابقة"""
    print("\n🧹 تنظيف مجلدات البناء السابقة...")
    
    dirs_to_clean = ["build", "dist"]
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✅ تم حذف مجلد {dir_name}")
            except Exception as e:
                print(f"⚠️ تعذر حذف مجلد {dir_name}: {e}")
        else:
            print(f"ℹ️ مجلد {dir_name} غير موجود")

def run_pyinstaller():
    """تشغيل PyInstaller"""
    print("\n🔨 بدء عملية التحزيم...")
    print("=" * 40)
    
    try:
        # تشغيل PyInstaller
        cmd = ["pyinstaller", "ultimate_pdf_build.spec"]
        print(f"🚀 تشغيل الأمر: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ تم التحزيم بنجاح!")
            return True
        else:
            print("❌ فشل في التحزيم!")
            print("📋 رسائل الخطأ:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل PyInstaller: {e}")
        return False

def check_output():
    """فحص المخرجات"""
    print("\n📦 فحص المخرجات...")
    
    output_dir = "dist/المعين_في_الحراسة_العامة_PDF_نهائي"
    
    if os.path.exists(output_dir):
        print(f"✅ مجلد الإخراج موجود: {output_dir}")
        
        # فحص الملف التنفيذي
        exe_file = os.path.join(output_dir, "المعين_في_الحراسة_العامة_PDF_نهائي.exe")
        if os.path.exists(exe_file):
            size_mb = os.path.getsize(exe_file) / (1024 * 1024)
            print(f"✅ الملف التنفيذي موجود: {size_mb:.1f} MB")
        else:
            print("❌ الملف التنفيذي غير موجود")
            return False
        
        # عد الملفات في المجلد
        file_count = len([f for f in os.listdir(output_dir) if os.path.isfile(os.path.join(output_dir, f))])
        print(f"📁 عدد الملفات في المجلد: {file_count}")
        
        return True
    else:
        print("❌ مجلد الإخراج غير موجود")
        return False

def show_summary():
    """عرض ملخص العملية"""
    print("\n" + "=" * 60)
    print("📋 ملخص عملية التحزيم")
    print("=" * 60)
    
    print("🎯 المميزات المضمنة:")
    print("  • جميع نوافذ البرنامج")
    print("  • مكتبات PDF (FPDF, ReportLab)")
    print("  • دعم النصوص العربية")
    print("  • مكتبات الطباعة (pywin32)")
    print("  • واجهة PyQt5")
    print("  • قاعدة البيانات SQLite")
    print("  • الخطوط العربية")
    print("  • أيقونة البرنامج")
    
    print("\n📁 مجلد الإخراج:")
    print("  dist/المعين_في_الحراسة_العامة_PDF_نهائي/")
    
    print("\n🚀 لتشغيل البرنامج:")
    print("  انتقل إلى مجلد الإخراج وشغل الملف التنفيذي")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # فحص المتطلبات
    if not check_requirements():
        print("\n❌ فشل في فحص المتطلبات!")
        input("اضغط Enter للخروج...")
        return
    
    # تنظيف المجلدات
    clean_build_dirs()
    
    # تشغيل التحزيم
    if run_pyinstaller():
        # فحص المخرجات
        if check_output():
            print("\n🎉 تم التحزيم بنجاح!")
            show_summary()
        else:
            print("\n⚠️ التحزيم تم ولكن هناك مشاكل في المخرجات")
    else:
        print("\n❌ فشل في التحزيم!")
    
    print("\n" + "=" * 60)
    input("اضغط Enter للخروج...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ تم إيقاف العملية بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
