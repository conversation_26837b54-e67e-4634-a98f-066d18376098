#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTableWidget, QTableWidgetItem, QFrame,
    QMessageBox, QHeaderView, QAbstractItemView, QComboBox, QSizePolicy, QCheckBox
)
from PyQt5.QtCore import QSize
from PyQt5.QtGui import QFont, QIcon, QColor, QFontMetrics
from PyQt5.QtCore import Qt
from datetime import datetime
import json

class DataViewWindow(QMainWindow):
    """نافذة عرض البيانات مع جدول احترافي"""

    def __init__(self, parent=None, db_path=None):
        super().__init__(parent)
        # تحديد مسار قاعدة البيانات
        if db_path:
            self.db_path = db_path
        elif parent and hasattr(parent, 'db_path'):
            self.db_path = parent.db_path
        else:
            # استخدام data.db في مجلد البرنامج
            script_dir = os.path.dirname(os.path.abspath(__file__))
            self.db_path = os.path.join(script_dir, "data.db")
        self.setupUI()
        self.setup_checkbox_style()  # إعداد نمط مربعات الاختيار المحسن
        self.load_filter_options()  # تحميل خيارات التصفية أولاً
        self.apply_filters()  # تحميل البيانات عند بدء التشغيل

        # تأخير تحديث حجم الجدول للتأكد من اكتمال التهيئة
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(100, self.delayed_table_setup)

    def delayed_table_setup(self):
        """إعداد مؤجل للجدول لضمان عرض جميع الصفوف"""
        try:
            if hasattr(self, 'table') and self.table:
                # تحديث ارتفاع الجدول بناءً على حجم النافذة الفعلي
                self.adjust_table_height()

                # تحديث حجم الجدول
                self.update_table_size()

                # إذا كانت النافذة مدمجة، تأكد من التكيف مع الحجم
                if self.parent() is not None:
                    self.table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOn)
                    self.table.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        except Exception as e:
            print(f"خطأ في الإعداد المؤجل للجدول: {str(e)}")

    def setup_checkbox_style(self):
        """إعداد نمط مربعات الاختيار المحسن - تم إزالة الأنماط المتعارضة"""
        # لا نطبق أي أنماط CSS - سيتم التحكم في الألوان برمجياً
        pass

    def adjust_table_height(self):
        """تعديل ارتفاع الجدول بناءً على حجم النافذة"""
        try:
            # الحصول على حجم النافذة الحالي
            window_height = self.height()

            # حساب الارتفاع المتاح للجدول بدقة أكبر
            # طرح ارتفاع الأزرار (80) + الفلاتر (80) + المسافات (40) + المساحة السفلية (20)
            reserved_space = 220  # مساحة محجوزة للعناصر الأخرى
            available_height = window_height - reserved_space

            # تحديد حد أدنى وأقصى للارتفاع
            min_height = 300  # حد أدنى معقول للارتفاع
            max_height = max(available_height, min_height)

            # التأكد من أن الارتفاع لا يتجاوز حجم النافذة
            if max_height > window_height - 100:  # ترك مساحة 100px للعناصر الأخرى
                max_height = window_height - 100

            # تطبيق الارتفاع على الجدول
            self.table.setMaximumHeight(max_height)
            self.table.setMinimumHeight(min_height)

            print(f"تم تعديل ارتفاع الجدول: {max_height}px (ارتفاع النافذة: {window_height}px)")

        except Exception as e:
            print(f"خطأ في تعديل ارتفاع الجدول: {str(e)}")

    def setupUI(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("عرض بيانات الطلاب")
        # إزالة showMaximized من هنا
        self.setLayoutDirection(Qt.RightToLeft)

        # تطبيق نمط احترافي للنافذة الرئيسية
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fc,
                    stop: 1 #e9ecef
                );
            }        """)

        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(2)

        # إطار الأزرار
        buttons_frame = QFrame()
        buttons_frame.setMaximumHeight(80)
        buttons_frame.setFixedHeight(80)
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                padding: 1px;
                margin: 1px 5px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(5)

        # إنشاء الأزرار المطلوبة
        btn1 = self.create_action_button("📝 التسجيل وإعادة التسجيل", "#27ae60")
        btn2 = self.create_action_button("💰 أداء الواجبات الشهرية", "#e67e22")
        btn3 = self.create_action_button("📊 التقارير", "#6f42c1")
        btn4 = self.create_action_button("🗑️ حذف تلميذ", "#dc3545")  # زر حذف التلميذ الجديد
        btn5 = self.create_action_button("🔍 استعلام الأداء", "#17a2b8")
        btn6 = self.create_action_button("🚫 إلغاء التنشيط", "#fd7e14")
        btn7 = self.create_action_button("↩️ إرجاع التلميذ", "#28a745")
        btn8 = self.create_action_button("🔄 تحديث النموذج", "#007bff")  # زر تحديث النموذج الجديد

        # ربط الأزرار بالوظائف
        btn1.clicked.connect(self.handle_registration)
        btn2.clicked.connect(self.show_monthly_duties_menu)
        btn3.clicked.connect(self.show_reports_menu)
        btn4.clicked.connect(self.handle_delete_student)  # ربط زر حذف التلميذ
        btn5.clicked.connect(self.query_payment_status)
        btn6.clicked.connect(self.handle_deactivate_student)
        btn7.clicked.connect(self.handle_restore_student)
        btn8.clicked.connect(self.refresh_form_data)  # ربط زر تحديث النموذج

        # إضافة الأزرار للتخطيط
        buttons_layout.addWidget(btn1)
        buttons_layout.addWidget(btn2)
        buttons_layout.addWidget(btn3)
        buttons_layout.addWidget(btn4)
        buttons_layout.addWidget(btn5)
        buttons_layout.addWidget(btn6)
        buttons_layout.addWidget(btn7)
        buttons_layout.addWidget(btn8)  # زر تحديث النموذج
        buttons_layout.addStretch()

        main_layout.addWidget(buttons_frame)

        # إطار التصفية
        filter_frame = QFrame()
        filter_frame.setMaximumHeight(80)
        filter_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 5px;
                margin: 5px 0px;
            }
        """)

        filter_layout = QHBoxLayout(filter_frame)
        filter_layout.setSpacing(5)

        # تسمية التصفية
        filter_label = QLabel("🔍 تصفية البيانات:")
        filter_label.setFont(QFont("Calibri", 13, QFont.Bold))
        filter_label.setStyleSheet("color: #495057; border: none;")

        # مربع البحث
        search_label = QLabel("البحث:")
        search_label.setFont(QFont("Calibri", 12, QFont.Bold))
        search_label.setStyleSheet("color: #495057; border: none;")

        from PyQt5.QtWidgets import QLineEdit
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث بالاسم أو الرمز أو المؤسسة الأصلية...")
        self.search_input.setFont(QFont("Calibri", 12))
        self.search_input.setMinimumWidth(200)
        self.search_input.setStyleSheet("""
            QLineEdit {
                background-color: white;
                border: 2px solid #ced4da;
                border-radius: 5px;
                padding: 5px;
            }
            QLineEdit:focus {
                border: 2px solid #007bff;
            }        """)
        self.search_input.textChanged.connect(self.apply_filters)

        # قائمة تصفية المجموعات
        group_filter_label = QLabel("المجموعة:")
        group_filter_label.setFont(QFont("Calibri", 12, QFont.Bold))
        group_filter_label.setStyleSheet("color: #495057; border: none;")

        self.group_filter_combo = QComboBox()
        self.group_filter_combo.setFont(QFont("Calibri", 12, QFont.Bold))
        self.group_filter_combo.setMinimumWidth(150)
        self.group_filter_combo.setStyleSheet("""
            QComboBox {
                background-color: white;
                border: 2px solid #ff5722;
                border-radius: 5px;
                padding: 5px;
                font-weight: bold;
            }
            QComboBox:focus {
                border: 2px solid #e64a19;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #495057;
                margin-right: 5px;
            }
        """)
        self.group_filter_combo.currentTextChanged.connect(self.on_group_filter_changed)

        # قائمة تصفية الأقسام
        section_filter_label = QLabel("القسم:")
        section_filter_label.setFont(QFont("Calibri", 12, QFont.Bold))
        section_filter_label.setStyleSheet("color: #495057; border: none;")

        self.section_filter_combo = QComboBox()
        self.section_filter_combo.setFont(QFont("Calibri", 12, QFont.Bold))
        self.section_filter_combo.setMinimumWidth(150)
        self.section_filter_combo.setStyleSheet("""
            QComboBox {
                background-color: white;
                border: 2px solid #ced4da;
                border-radius: 5px;
                padding: 5px;
                font-weight: bold;
            }
            QComboBox:focus {
                border: 2px solid #007bff;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #495057;
                margin-right: 5px;
            }
        """)
        self.section_filter_combo.currentTextChanged.connect(self.on_section_filter_changed)





        # عداد التحديدات والتصفيات
        self.selection_counter_label = QLabel("المحدد: 0 | المعروض: 0 | الإجمالي: 0")
        self.selection_counter_label.setFont(QFont("Calibri", 12, QFont.Bold))
        self.selection_counter_label.setStyleSheet("""
            QLabel {
                color: #007bff;
                background-color: #e7f3ff;
                border: 2px solid #007bff;
                border-radius: 5px;
                padding: 5px 10px;
                margin: 2px;
            }
        """)

        # فلتر الشهر
        month_filter_label = QLabel("الشهر:")
        month_filter_label.setFont(QFont("Calibri", 12, QFont.Bold))
        month_filter_label.setStyleSheet("color: #495057; border: none;")

        self.month_filter_combo = QComboBox()
        self.month_filter_combo.setFont(QFont("Calibri", 12, QFont.Bold))
        self.month_filter_combo.setMinimumWidth(120)
        self.month_filter_combo.setStyleSheet("""
            QComboBox {
                background-color: white;
                border: 2px solid #ced4da;
                border-radius: 5px;
                padding: 5px;
                font-weight: bold;
            }
            QComboBox:focus {
                border: 2px solid #007bff;
            }
            QComboBox::drop-down {
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #495057;
                margin-right: 5px;
            }
        """)
        self.month_filter_combo.addItems([
            "جميع الشهور", "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        ])
        self.month_filter_combo.currentTextChanged.connect(self.apply_filters)



        # إضافة العناصر للتخطيط        filter_layout.addWidget(filter_label)
        filter_layout.addWidget(search_label)
        filter_layout.addWidget(self.search_input)
        filter_layout.addWidget(group_filter_label)
        filter_layout.addWidget(self.group_filter_combo)
        filter_layout.addWidget(section_filter_label)
        filter_layout.addWidget(self.section_filter_combo)
        filter_layout.addWidget(month_filter_label)
        filter_layout.addWidget(self.month_filter_combo)

        filter_layout.addWidget(self.selection_counter_label)
        filter_layout.addStretch()

        main_layout.addWidget(filter_frame)

        # إنشاء الجدول
        self.table = QTableWidget()
        self.setup_table()

        # تحديد ارتفاع محدد للجدول لضمان ظهور جميع الصفوف
        # حساب الارتفاع المتاح بناءً على حجم النافذة الافتراضي (600px)
        initial_height = 380  # ارتفاع ابتدائي آمن (600 - 220 = 380)
        self.table.setMaximumHeight(initial_height)
        self.table.setMinimumHeight(300)  # حد أدنى للارتفاع محسن

        # ضمان أن الجدول يحتوي على شريط تمرير عمودي دائماً
        self.table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOn)

        # إضافة الجدول مباشرة للتخطيط الرئيسي
        main_layout.addWidget(self.table)

        # إضافة مساحة 5 نقاط أسفل الجدول لضمان ظهور جميع الصفوف
        bottom_spacer = QWidget()
        bottom_spacer.setFixedHeight(50)
        bottom_spacer.setStyleSheet("""
            QWidget {
                background-color: transparent;
                border: none;
                margin: 0px;
                padding: 0px;
            }
        """)
        main_layout.addWidget(bottom_spacer)

        # ضمان أن الجدول يأخذ كامل المساحة المتاحة
        main_layout.setStretchFactor(self.table, 1)

        # ضبط المسافات بين العناصر
        main_layout.setSpacing(5)

        # ضبط الحواف للتخطيط الرئيسي لضمان المساحة السفلية
        main_layout.setContentsMargins(5, 5, 5, 0)  # إزالة الحافة السفلية لتجنب التداخل

        # ضبط الحد الأدنى للحجم للنافذة
        self.setMinimumSize(1200, 600)

        # تحديد الحجم الافتراضي للنافذة
        self.resize(1200, 600)

        # ضمان أن النافذة تتوسع لتملأ المساحة المتاحة عند الفتح المدمج
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # تحديد الحد الأقصى للحجم ليتناسب مع الشاشة
        from PyQt5.QtWidgets import QApplication
        screen = QApplication.desktop().screenGeometry()
        self.setMaximumSize(screen.width(), screen.height())

    def create_action_button(self, text, color):
        """إنشاء زر عمليات منسق مع عرض حسب النص"""
        button = QPushButton(text)
        button.setFont(QFont("Calibri", 13, QFont.Bold))
        button.setMaximumHeight(43)

        # حساب العرض المناسب حسب النص مع زيادة بسيطة
        font_metrics = QFontMetrics(button.font())
        text_width = font_metrics.width(text)
        button_width = text_width + 40  # زيادة 40 بكسل للحواف والمساحة
        button.setMinimumWidth(button_width)
        button.setMaximumWidth(button_width + 20)  # مرونة إضافية

        # تحويل لون hex إلى لون مظلم
        dark_color = self.darken_color(color, 50)
        light_color = self.lighten_color(color, 40)

        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color},
                    stop: 1 {dark_color}
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 15px;
                font-weight: bold;
                border: 2px solid {dark_color};
            }}
            QPushButton:hover {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {light_color},
                    stop: 1 {color}
                );
                border: 2px solid {color};
            }}
            QPushButton:pressed {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {dark_color},
                    stop: 1 {self.darken_color(dark_color, 20)}
                );
                border: 2px solid {self.darken_color(dark_color, 20)};
            }}
        """)
        return button

    def lighten_color(self, color, amount):
        """تفتيح اللون"""
        if color.startswith('#'):
            color = color[1:]

        r = min(255, int(color[0:2], 16) + amount)
        g = min(255, int(color[2:4], 16) + amount)
        b = min(255, int(color[4:6], 16) + amount)

        return f"#{r:02x}{g:02x}{b:02x}"

    def darken_color(self, color, amount):
        """تغميق اللون"""
        if color.startswith('#'):
            color = color[1:]

        r = max(0, int(color[0:2], 16) - amount)
        g = max(0, int(color[2:4], 16) - amount)
        b = max(0, int(color[4:6], 16) - amount)

        return f"#{r:02x}{g:02x}{b:02x}"

    def create_enhanced_checkbox(self, row_index):
        """إنشاء مربع اختيار محسن مع تأثيرات سلسة وحجم أكبر مثل student_multi_registration.py"""
        # إنشاء مربع اختيار حقيقي مثل الموجود في student_multi_registration.py
        checkbox = QCheckBox()
        checkbox.setFont(QFont("Calibri", 13, QFont.Bold))
        checkbox.setStyleSheet("""
            QCheckBox {
                spacing: 8px;
                padding: 5px;
            }
            QCheckBox::indicator {
                width: 28px;
                height: 28px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
            }
            QCheckBox::indicator:hover {
                border: 2px solid #3498db;
                background-color: #e3f2fd;
                transform: scale(1.05);
            }
            QCheckBox::indicator:checked {
                background-color: #ffc107;
                border: 2px solid #ff8f00;
                color: #0d47a1;
            }
            QCheckBox::indicator:checked:hover {
                background-color: #ff8f00;
                border: 2px solid #e65100;
            }
            QCheckBox::indicator:pressed {
                background-color: #e3f2fd;
                border: 2px solid #1565C0;
            }
            QCheckBox::indicator:checked:after {
                content: "✓";
                color: #0d47a1;
                font-weight: bold;
                font-size: 18px;
                text-align: center;
            }
        """)

        # إضافة خصائص مخصصة للتتبع
        checkbox.setProperty("row_index", row_index)

        # ربط إشارة التغيير
        checkbox.stateChanged.connect(lambda state: self.on_checkbox_state_changed(checkbox, state))

        # إضافة tooltip للمساعدة
        checkbox.setToolTip("انقر للتحديد/إلغاء التحديد - مربع اختيار محسن")

        # إنشاء widget مركزي لمحاذاة مربع الاختيار في وسط الخلية
        checkbox_widget = QWidget()
        checkbox_layout = QHBoxLayout(checkbox_widget)
        checkbox_layout.addWidget(checkbox)
        checkbox_layout.setAlignment(Qt.AlignCenter)
        checkbox_layout.setContentsMargins(0, 0, 0, 0)

        return checkbox_widget

    def on_checkbox_state_changed(self, checkbox, state):
        """معالجة تغيير حالة مربع الاختيار"""
        try:
            row_index = checkbox.property("row_index")
            if row_index is not None:
                print(f"🎯 تغيير حالة مربع الاختيار في الصف {row_index}: {'محدد' if state == 2 else 'غير محدد'}")

                if state == 2:  # Qt.Checked
                    # إلغاء تحديد باقي الصفوف (تحديد واحد فقط)
                    for other_row in range(self.table.rowCount()):
                        if other_row != row_index:
                            other_checkbox_widget = self.table.cellWidget(other_row, 0)
                            if other_checkbox_widget:
                                other_checkbox = other_checkbox_widget.findChild(QCheckBox)
                                if other_checkbox and other_checkbox.isChecked():
                                    other_checkbox.blockSignals(True)
                                    other_checkbox.setChecked(False)
                                    other_checkbox.blockSignals(False)
                                    self.apply_smooth_selection_effect(other_row, False)

                    # تطبيق تأثير التحديد على الصف الحالي
                    self.apply_smooth_selection_effect(row_index, True)
                else:
                    # إلغاء التحديد
                    self.apply_smooth_selection_effect(row_index, False)

                # تحديث العداد
                self.update_selection_counter()

        except Exception as e:
            print(f"❌ خطأ في معالجة تغيير حالة مربع الاختيار: {str(e)}")

    def add_checkbox_animation_effects(self):
        """إضافة تأثيرات الحركة لمربعات الاختيار"""
        try:
            from PyQt5.QtCore import QPropertyAnimation, QEasingCurve, pyqtProperty
            from PyQt5.QtWidgets import QGraphicsOpacityEffect

            # إضافة تأثيرات بصرية عند التفاعل مع مربعات الاختيار
            for row in range(self.table.rowCount()):
                checkbox_item = self.table.item(row, 0)
                if checkbox_item:
                    # إضافة تأثير الشفافية
                    opacity_effect = QGraphicsOpacityEffect()
                    opacity_effect.setOpacity(1.0)

        except Exception as e:
            print(f"تعذر إضافة تأثيرات الحركة: {str(e)}")

    def setup_table(self):
        """إعداد الجدول"""        # تعيين عناوين الأعمدة (تحويل المبلغ النهائي الشهري إلى حالة الدفع)
        columns = [
            "اختيار", "ID", "اسم التلميذ", "رمز التلميذ", "النوع", "رقم الهاتف الأول",
            "القسم", "المجموعة", "إجمالي مبلغ التسجيل", "الواجب الشهري", "حالة الدفع"
        ]

        self.table.setColumnCount(len(columns))
        self.table.setHorizontalHeaderLabels(columns)

        # إزالة جميع الأنماط المتعارضة - سيتم تطبيق الألوان برمجياً
        self.table.setStyleSheet("")

        # إعدادات الجدول - التحديد فقط من مربع الاختيار
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QAbstractItemView.SelectItems)
        self.table.setSelectionMode(QAbstractItemView.NoSelection)  # منع التحديد من الصف
        self.table.setSortingEnabled(True)

        # ملاحظة: ارتفاع الصفوف سيتم تعيينه في دالة set_fixed_column_widths()

        # منع التعديل على البيانات - فقط التحديد والتحديد المتعدد
        self.table.setEditTriggers(QAbstractItemView.NoEditTriggers)

        # تطبيق نمط بسيط فقط لرأس الجدول بلون أزرق فاتح
        self.table.setStyleSheet("""
            QHeaderView::section {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e3f2fd,
                    stop: 1 #bbdefb
                );
                color: #0d47a1;
                font-weight: bold;
                font-size: 16px;
                font-family: 'Calibri';
                padding: 12px;
                border: 1px solid #90caf9;
                text-align: center;
            }
            QHeaderView::section:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #bbdefb,
                    stop: 1 #90caf9
                );
                color: #1565c0;
            }
        """)

        # ربط إشارة النقر على مربع الاختيار فقط
        self.table.itemClicked.connect(self.on_checkbox_clicked)

        # تعيين ارتفاع رأس الجدول والصفوف بشكل صحيح
        header = self.table.horizontalHeader()
        header.setFixedHeight(40)  # ارتفاع الرأس (زيادة ليتناسب مع حجم الخط الأكبر)
        # سيتم تعيين وضع الأعمدة الثابت لاحقاً

        # ملاحظة: ارتفاع الصفوف سيتم تعيينه في دالة set_fixed_column_widths()

        # ضمان أن الجدول يتوسع ليملأ المساحة المتاحة
        self.table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # تمكين التمرير العمودي والأفقي
        self.table.setHorizontalScrollMode(QAbstractItemView.ScrollPerPixel)
        self.table.setVerticalScrollMode(QAbstractItemView.ScrollPerPixel)

        # ضمان عرض جميع الصفوف
        self.table.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.table.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # تحسين أداء الجدول
        self.table.setAutoScroll(True)
        self.table.setAutoScrollMargin(16)

        # إخفاء رؤوس الصفوف الجانبية لتوفير مساحة
        self.table.verticalHeader().setVisible(False)

        # سيتم تعيين عرض الأعمدة الثابت في دالة منفصلة

        # ربط إشارة تغيير التحديد بدالة تحديث العداد
        self.table.itemSelectionChanged.connect(self.update_selection_counter)

        # تعيين عرض ثابت للأعمدة
        self.set_fixed_column_widths()

    def load_filter_options(self):
        """تحميل خيارات التصفية (الأقسام فقط)"""
        try:            # اختبار الاتصال بقاعدة البيانات
            print(f"محاولة الاتصال بقاعدة البيانات: {self.db_path}")
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # تحميل الأقسام من جدول الاساتذة مع JOIN للحصول على أسماء الأقسام
            cursor.execute("""
                SELECT DISTINCT q.اسم_القسم 
                FROM الاساتذة a
                LEFT JOIN الاقسام q ON a.قسم_id = q.id
                WHERE q.اسم_القسم IS NOT NULL AND q.اسم_القسم != '' 
                ORDER BY q.اسم_القسم
            """)
            sections_from_teachers = [row[0] for row in cursor.fetchall()]
            print(f"✅ تم تحميل {len(sections_from_teachers)} قسم من جدول الاساتذة")

            # إذا لم توجد أقسام في جدول الاساتذة، استخدم جدول_البيانات كبديل
            if not sections_from_teachers:
                print("محاولة تحميل الأقسام من جدول_البيانات كبديل")
                cursor.execute("SELECT DISTINCT القسم FROM جدول_البيانات WHERE القسم IS NOT NULL AND القسم != '' ORDER BY القسم")
                sections_from_teachers = [row[0] for row in cursor.fetchall()]
                print(f"✅ تم تحميل {len(sections_from_teachers)} قسم من جدول_البيانات كبديل")            # تحميل المجموعات من جدول_البيانات
            cursor.execute("SELECT DISTINCT اسم_المجموعة FROM جدول_البيانات WHERE اسم_المجموعة IS NOT NULL AND اسم_المجموعة != '' ORDER BY اسم_المجموعة")
            groups = [row[0] for row in cursor.fetchall()]
            print(f"✅ تم تحميل {len(groups)} مجموعة من جدول_البيانات")

            # تحديث قائمة المجموعات
            self.group_filter_combo.blockSignals(True)
            self.group_filter_combo.clear()
            self.group_filter_combo.addItem("جميع المجموعات")
            if groups:
                self.group_filter_combo.addItems(groups)
            else:
                self.group_filter_combo.addItem("لا توجد مجموعات متاحة")
                self.group_filter_combo.setEnabled(False)
            self.group_filter_combo.setCurrentIndex(0)
            self.group_filter_combo.blockSignals(False)

            # ربط تغيير المجموعة بتحديث الأقسام
            self.group_filter_combo.currentTextChanged.connect(self.load_sections_for_group)

            # تحديث قائمة الأقسام
            # تحديث قائمة الأقسام - تعطيل حتى يتم اختيار مجموعة
            self.section_filter_combo.blockSignals(True)
            self.section_filter_combo.clear()
            self.section_filter_combo.addItem("اختر المجموعة أولاً")
            self.section_filter_combo.setEnabled(False)  # تعطيل حتى يتم اختيار مجموعة
            self.section_filter_combo.blockSignals(False)

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل خيارات التصفية: {str(e)}")

    def load_sections_for_group(self):
        """تحميل الأقسام بناءً على المجموعة المختارة"""
        try:
            selected_group = self.group_filter_combo.currentText()

            # التحقق من اختيار مجموعة صحيحة
            if not selected_group or selected_group in ["جميع المجموعات", ""]:
                # إذا لم يتم اختيار مجموعة محددة، تعطيل قائمة الأقسام
                self.section_filter_combo.blockSignals(True)
                self.section_filter_combo.clear()
                self.section_filter_combo.addItem("اختر مجموعة محددة أولاً")
                self.section_filter_combo.setEnabled(False)
                self.section_filter_combo.blockSignals(False)
                return

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # تحميل الأقسام للمجموعة المحددة فقط
            cursor.execute("""
                SELECT DISTINCT القسم
                FROM جدول_البيانات
                WHERE اسم_المجموعة = ? AND القسم IS NOT NULL AND القسم != ''
                ORDER BY القسم
            """, (selected_group,))

            sections = [row[0] for row in cursor.fetchall()]
            print(f"✅ تم تحميل {len(sections)} قسم للمجموعة: {selected_group}")

            # تحديث قائمة الأقسام وتفعيلها
            self.section_filter_combo.blockSignals(True)
            self.section_filter_combo.clear()
            self.section_filter_combo.addItem("اختر القسم")
            self.section_filter_combo.addItem("التلاميذ المجمدين")

            if sections:
                self.section_filter_combo.addItems(sections)
                self.section_filter_combo.setEnabled(True)  # تفعيل القائمة
            else:
                self.section_filter_combo.addItem("لا توجد أقسام متاحة")
                self.section_filter_combo.setEnabled(False)

            self.section_filter_combo.blockSignals(False)

            # تطبيق التصفية تلقائياً عند تغيير المجموعة
            self.apply_filters()
            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل أقسام المجموعة: {str(e)}")

    def get_student_creation_date(self, student_id):
        """الحصول على تاريخ إنشاء التلميذ"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # البحث عن تاريخ الإنشاء في جدول_البيانات
            cursor.execute("""
                SELECT تاريخ_الانشاء
                FROM جدول_البيانات
                WHERE id = ?
                LIMIT 1
            """, (student_id,))

            result = cursor.fetchone()
            conn.close()

            if result and result[0]:
                return result[0]
            else:
                # إذا لم يوجد تاريخ إنشاء، استخدم التاريخ الحالي
                from datetime import datetime
                return datetime.now().strftime("%Y-%m-%d")

        except Exception as e:
            print(f"خطأ في الحصول على تاريخ إنشاء التلميذ: {str(e)}")
            from datetime import datetime
            return datetime.now().strftime("%Y-%m-%d")

    def get_student_sections_by_creation_date(self, student_id, creation_date):
        """الحصول على جميع أقسام التلميذ المرتبطة بتاريخ الإنشاء"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # البحث عن جميع الأقسام للتلميذ بنفس تاريخ الإنشاء
            cursor.execute("""
                SELECT id, القسم, اسم_المجموعة, مبلغ_التسجيل, الواجب_الشهري,
                       اسم_التلميذ, رمز_التلميذ, النوع, رقم_الهاتف_الأول, رقم_الهاتف_الثاني,
                       المؤسسة_الاصلية, تاريخ_الانشاء
                FROM جدول_البيانات
                WHERE رمز_التلميذ = (
                    SELECT رمز_التلميذ FROM جدول_البيانات WHERE id = ?
                ) AND DATE(تاريخ_الانشاء) = DATE(?)
                ORDER BY القسم
            """, (student_id, creation_date))

            sections_data = cursor.fetchall()
            conn.close()

            print(f"✅ تم العثور على {len(sections_data)} قسم للتلميذ بتاريخ الإنشاء: {creation_date}")

            return sections_data

        except Exception as e:
            print(f"خطأ في الحصول على أقسام التلميذ: {str(e)}")
            return []

    def on_row_selection_changed(self):
        """معالجة تغيير تحديد الصفوف مع تأثيرات سلسة"""
        try:
            selected_rows = set()
            for item in self.table.selectedItems():
                selected_rows.add(item.row())

            # منع التحديد المتعدد - السماح بصف واحد فقط
            if len(selected_rows) > 1:
                # إذا تم تحديد أكثر من صف، احتفظ بآخر صف محدد فقط
                last_selected_row = max(selected_rows)

                # إلغاء تحديد جميع الصفوف
                self.table.clearSelection()

                # تحديد الصف الأخير فقط
                self.table.selectRow(last_selected_row)
                selected_rows = {last_selected_row}

            # تحديث مربعات الاختيار والألوان بناءً على الصف المحدد مع تأثيرات سلسة
            for row in range(self.table.rowCount()):
                checkbox_item = self.table.item(row, 0)
                if checkbox_item:
                    if row in selected_rows:
                        # تحديد مربع الاختيار مع تأثير سلس
                        checkbox_item.setCheckState(Qt.Checked)

                        # تطبيق تأثير التحديد مع انتقال سلس
                        self.apply_smooth_selection_effect(row, True)
                    else:
                        # إلغاء تحديد مربع الاختيار
                        checkbox_item.setCheckState(Qt.Unchecked)

                        # إزالة تأثير التحديد مع انتقال سلس
                        self.apply_smooth_selection_effect(row, False)

            # تحديث العداد عند تغيير التحديدات
            self.update_selection_counter()

        except Exception as e:
            print(f"خطأ في معالجة تحديد الصفوف: {str(e)}")

    def apply_smooth_selection_effect(self, row, is_selected):
        """تطبيق تأثير التحديد السلس على الصف - أصفر مع نص أزرق"""
        try:
            print(f"🎨 تطبيق تأثير التحديد على الصف {row}: {'محدد' if is_selected else 'غير محدد'}")

            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                if item:
                    if is_selected:
                        # تطبيق تأثير التحديد - خلفية صفراء غامقة مع نص أزرق
                        item.setBackground(QColor("#ffc107"))  # أصفر غامق
                        item.setForeground(QColor("#0d47a1"))  # نص أزرق غامق
                        print(f"✅ تم تطبيق اللون الأصفر الغامق على العمود {col}")

                        # تمييز خاص لمربع الاختيار
                        if col == 0:  # مربع الاختيار
                            item.setBackground(QColor("#ff8f00"))  # أصفر أكثر غمقاً
                            print(f"✅ تم تطبيق اللون الأصفر الأغمق على مربع الاختيار")
                    else:
                        # استعادة الألوان الأصلية
                        if col == 1:  # ID
                            item.setBackground(QColor("#ecf0f1"))
                            item.setForeground(QColor("#2c3e50"))
                        elif col in [7, 8, 9]:  # الأعمدة المالية
                            item.setBackground(QColor("#e8f5e8"))
                            item.setForeground(QColor("#27ae60"))
                        else:
                            # لون متناوب للصفوف
                            if row % 2 == 0:
                                item.setBackground(QColor("white"))
                            else:
                                item.setBackground(QColor("#f8f9fa"))
                            item.setForeground(QColor("#2c3e50"))
                        print(f"🔄 تم استعادة اللون الأصلي للعمود {col}")

        except Exception as e:
            print(f"❌ خطأ في تطبيق تأثير التحديد: {str(e)}")

    def on_checkbox_clicked(self, item):
        """معالجة النقر على مربعات الاختيار فقط - تم تعطيلها لأننا نستخدم QCheckBox الآن"""
        # هذه الدالة لم تعد مستخدمة لأننا نستخدم QCheckBox مع on_checkbox_state_changed
        pass

    def add_click_feedback(self, item):
        """إضافة تأثير بصري عند النقر"""
        try:
            # تأثير وميض بسيط مع ألوان واضحة
            original_bg = item.background()

            # تغيير اللون مؤقتاً إلى أخضر فاتح
            item.setBackground(QColor("#c8e6c9"))
            item.setForeground(QColor("#2e7d32"))

            # استعادة اللون الأصلي بعد فترة قصيرة
            from PyQt5.QtCore import QTimer
            def restore_color():
                try:
                    if item.checkState() == Qt.Checked:
                        # استعادة اللون الأصفر الغامق للعنصر المحدد
                        item.setBackground(QColor("#ff8f00"))  # أصفر غامق للمربع
                        item.setForeground(QColor("#0d47a1"))  # نص أزرق غامق

                        # تطبيق اللون الأصفر على كامل الصف
                        row = item.row()
                        self.apply_smooth_selection_effect(row, True)
                    else:
                        item.setBackground(original_bg)
                        item.setForeground(QColor("#2c3e50"))
                except:
                    pass

            QTimer.singleShot(150, restore_color)

        except Exception as e:
            print(f"خطأ في إضافة تأثير النقر: {str(e)}")

    def clear_all_selections(self):
        """إزالة جميع التحديدات من الجدول مثل نافذة الحضور والغياب"""
        try:
            # إلغاء تحديد جميع الصفوف
            self.table.clearSelection()

            # إلغاء تحديد جميع مربعات الاختيار وإزالة التلوين
            for row in range(self.table.rowCount()):
                checkbox_widget = self.table.cellWidget(row, 0)
                if checkbox_widget:
                    checkbox = checkbox_widget.findChild(QCheckBox)
                    if checkbox:
                        checkbox.blockSignals(True)
                        checkbox.setChecked(False)
                        checkbox.blockSignals(False)

                # إزالة تلوين جميع الصفوف واستعادة الألوان الأصلية
                for col in range(self.table.columnCount()):
                    item = self.table.item(row, col)
                    if item:
                        # استعادة اللون الأصلي حسب نوع العمود
                        if col == 1:  # ID
                            item.setBackground(QColor("#ecf0f1"))
                            item.setForeground(QColor("black"))
                        elif col in [7, 8, 9]:  # الأعمدة المالية
                            item.setBackground(QColor("#e8f5e8"))
                            item.setForeground(QColor("black"))
                        else:
                            # لون متناوب للصفوف مثل نافذة الحضور والغياب
                            if row % 2 == 0:
                                item.setBackground(QColor("white"))
                            else:
                                item.setBackground(QColor("#f8f9fa"))
                            item.setForeground(QColor("black"))

            # تحديث العداد بعد إزالة التحديدات
            self.update_selection_counter()

        except Exception as e:            print(f"خطأ في إزالة التحديدات: {str(e)}")

    def on_group_filter_changed(self):
        """معالج تغيير تصفية المجموعة"""
        # تطبيق التصفية مباشرة عند تغيير المجموعة
        self.apply_filters()

    def on_section_filter_changed(self):
        """معالج تغيير تصفية القسم"""
        # تطبيق التصفية مباشرة عند تغيير القسم
        self.apply_filters()

    def apply_filters(self):
        """تطبيق التصفية مع عرض حالة الدفع"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إنشاء جدول الواجبات الشهرية إذا لم يكن موجوداً
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS monthly_duties (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    student_id INTEGER NOT NULL,
                    month TEXT NOT NULL,
                    year INTEGER NOT NULL,
                    amount_required REAL NOT NULL DEFAULT 0,
                    amount_paid REAL NOT NULL DEFAULT 0,
                    amount_remaining REAL NOT NULL DEFAULT 0,
                    payment_date TEXT,
                    payment_status TEXT NOT NULL DEFAULT 'غير مدفوع',
                    notes TEXT,
                    اسم_الاستاذ TEXT,
                    القسم TEXT,
                    المادة TEXT,
                    النوع TEXT,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (student_id) REFERENCES جدول_البيانات(id),
                    UNIQUE(student_id, month, year)
                )
            """)

            # إضافة الأعمدة الجديدة إذا لم تكن موجودة
            try:
                cursor.execute("ALTER TABLE monthly_duties ADD COLUMN المادة TEXT")
                print("✅ تم إضافة عمود المادة")
            except sqlite3.OperationalError:
                pass

            try:
                cursor.execute("ALTER TABLE monthly_duties ADD COLUMN النوع TEXT")
                print("✅ تم إضافة عمود النوع")
            except sqlite3.OperationalError:
                pass            # الحصول على قيم التصفية
            selected_group = self.group_filter_combo.currentText()
            selected_section = self.section_filter_combo.currentText()
            selected_month = self.month_filter_combo.currentText()
            search_text = self.search_input.text().strip() if hasattr(self, 'search_input') else ""            # بناء الاستعلام الأساسي - الحصول على المجموعة من جدول_البيانات مباشرة
            base_query = """
                SELECT jb.id, jb.اسم_التلميذ, jb.رمز_التلميذ, jb.النوع, jb.رقم_الهاتف_الأول,
                       jb.القسم,
                       COALESCE(jb.اسم_المجموعة, 'غير محدد') as المجموعة,
                       COALESCE(jb.مبلغ_التسجيل, 0) as اجمالي_مبلغ_التسجيل,
                       COALESCE(jb.الواجب_الشهري, 0) as الواجب_الشهري
                FROM جدول_البيانات jb
            """# تطبيق تصفية المجموعة والقسم والبحث
            conditions = []
            params = []            # معالجة تصفية المجموعة
            if selected_group and selected_group not in ["جميع المجموعات", ""]:
                # إضافة شرط للمجموعة من عمود اسم_المجموعة مباشرة
                conditions.append("jb.اسم_المجموعة = ?")
                params.append(selected_group)

            # معالجة تصفية القسم
            if selected_section and selected_section not in ["اختر القسم", ""]:
                if selected_section == "التلاميذ المجمدين":
                    # عرض التلاميذ المجمدين (الذين يحتوي قسمهم على "غير منشط")
                    conditions.append("jb.القسم LIKE ?")
                    params.append("%غير منشط%")
                else:
                    # عرض قسم محدد
                    conditions.append("jb.القسم = ?")
                    params.append(selected_section)

            # معالجة البحث
            if search_text:
                search_conditions = []
                search_conditions.append("jb.اسم_التلميذ LIKE ?")
                search_conditions.append("jb.رمز_التلميذ LIKE ?")
                # إزالة البحث في المؤسسة الأصلية مؤقتاً لتجنب الأخطاء
                conditions.append("(" + " OR ".join(search_conditions) + ")")
                search_param = f"%{search_text}%"
                params.extend([search_param, search_param])

            # بناء الاستعلام النهائي
            if conditions:
                query = base_query + " WHERE " + " AND ".join(conditions) + " ORDER BY jb.id DESC"
            else:
                query = base_query + " ORDER BY jb.id DESC"

            print(f"الاستعلام المنفذ: {query}")
            print(f"المعاملات: {params}")

            # اختبار وجود البيانات أولاً
            cursor.execute("SELECT COUNT(*) FROM جدول_البيانات")
            total_count = cursor.fetchone()[0]
            print(f"إجمالي السجلات في جدول_البيانات: {total_count}")

            cursor.execute(query, params)
            records = cursor.fetchall()
            print(f"عدد السجلات المسترجعة: {len(records)}")

            # طباعة أول سجل للتحقق
            if records:
                print(f"أول سجل: {records[0]}")
            else:
                print("لا توجد سجلات مطابقة للاستعلام")

            # تعيين عدد الصفوف
            self.table.setRowCount(len(records))

            # تطبيق العرض الثابت للأعمدة وارتفاع الصفوف - دائماً
            self.set_fixed_column_widths()

            # ملء الجدول بالبيانات
            for row_index, record in enumerate(records):
                # إضافة مربع اختيار محسن في العمود الأول
                checkbox_widget = self.create_enhanced_checkbox(row_index)
                self.table.setCellWidget(row_index, 0, checkbox_widget)                # ملء الأعمدة العادية (من 0 إلى 8)
                for col_index in range(len(record)):
                    if col_index >= 9:  # تجنب تجاوز عدد الأعمدة
                        break
                    value = record[col_index]

                    # معالجة القيم الخاصة للأعمدة المالية
                    if col_index in [7, 8] and value:  # الأعمدة المالية (تم تحديث الأرقام بعد إضافة عمود المجموعة)
                        try:
                            display_value = f"{float(value):.2f} درهم"
                        except (ValueError, TypeError):
                            display_value = "0.00 درهم"
                    else:
                        display_value = str(value) if value is not None else ""

                    item = QTableWidgetItem(display_value)
                    item.setTextAlignment(Qt.AlignCenter)
                    item.setFont(QFont("Calibri", 13, QFont.Bold))

                    # تلوين بعض الأعمدة
                    if col_index == 0:  # ID
                        item.setBackground(QColor("#ecf0f1"))
                    elif col_index == 1:  # اسم التلميذ
                        item.setForeground(QColor("#2c3e50"))
                    elif col_index in [6, 7]:  # الأعمدة المالية
                        item.setBackground(QColor("#e8f5e8"))
                        item.setForeground(QColor("#27ae60"))

                    # وضع العنصر في العمود المناسب (col_index + 1 للإزاحة)
                    self.table.setItem(row_index, col_index + 1, item)                # إضافة عمود حالة الدفع فارغ افتراضياً (العمود 10)
                status_item = QTableWidgetItem("غير محدد")
                status_item.setBackground(QColor("#f8f9fa"))
                status_item.setForeground(QColor("#6c757d"))
                status_item.setTextAlignment(Qt.AlignCenter)
                status_item.setFont(QFont("Calibri", 13, QFont.Bold))
                self.table.setItem(row_index, 10, status_item)

            conn.close()

            # تحديث عنوان النافذة
            filter_info = ""
            if selected_section and selected_section != "اختر القسم":
                filter_info += f" - القسم: {selected_section}"
            if selected_month != "جميع الشهور":
                filter_info += f" - الشهر: {selected_month}"

            self.setWindowTitle(f"عرض بيانات الطلاب{filter_info} - إجمالي السجلات: {len(records)}")

            # تطبيق العرض الثابت للأعمدة وارتفاع الصفوف - دائماً (حتى عند التصفية)
            self.set_fixed_column_widths()

            # تحديث العداد بعد تطبيق التصفية
            self.update_selection_counter()

            # ضمان عرض جميع الصفوف بعد تحميل البيانات
            if len(records) > 0:
                # إعادة حساب ارتفاع الجدول أولاً
                self.adjust_table_height()

                # التمرير إلى أعلى الجدول
                self.table.scrollToTop()

                # تحديث حجم الجدول
                self.update_table_size()

                # إجبار إعادة رسم الجدول
                self.table.viewport().update()

        except Exception as e:
            print(f"خطأ في تطبيق التصفية: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في تطبيق التصفية: {str(e)}")

    def reset_filters(self):
        """إعادة تعيين جميع التصفيات"""
        self.group_filter_combo.blockSignals(True)
        self.group_filter_combo.setCurrentIndex(0)  # جميع المجموعات
        self.group_filter_combo.blockSignals(False)

        self.section_filter_combo.blockSignals(True)
        self.section_filter_combo.setCurrentIndex(0)  # اختر القسم
        self.section_filter_combo.blockSignals(False)

        self.month_filter_combo.blockSignals(True)
        self.month_filter_combo.setCurrentIndex(0)  # جميع الشهور
        self.month_filter_combo.blockSignals(False)

        self.year_filter_combo.blockSignals(True)
        current_year = datetime.now().year
        self.year_filter_combo.setCurrentText(str(current_year))
        self.year_filter_combo.blockSignals(False)

        # إعادة تعيين مربع البحث
        if hasattr(self, 'search_input'):
            self.search_input.blockSignals(True)
            self.search_input.clear()
            self.search_input.blockSignals(False)

        # تطبيق التصفية (عرض جميع الطلاب)
        self.apply_filters()

    def refresh_data(self):
        """تحديث البيانات"""
        # تحديث خيارات التصفية أولاً
        current_section = self.section_filter_combo.currentText()

        self.load_filter_options()

        # استعادة التحديد السابق إذا كان متاحاً
        if current_section and current_section != "اختر القسم":
            section_index = self.section_filter_combo.findText(current_section)
            if section_index >= 0:
                self.section_filter_combo.setCurrentIndex(section_index)

        self.apply_filters()

        # تحديث العداد
        self.update_selection_counter()

        # تحديث حجم الجدول بعد تحميل البيانات
        self.update_table_size()

        # تطبيق العرض الثابت للأعمدة
        self.set_fixed_column_widths()

    def query_payment_status(self):
        """استعلام حالة الدفع من جدول monthly_duties وتحديث الجدول"""
        try:
            # التحقق من اختيار الشهر والقسم
            selected_month = self.month_filter_combo.currentText()
            selected_section = self.section_filter_combo.currentText()
            selected_year = int(self.year_filter_combo.currentText())

            if selected_month == "جميع الشهور":
                QMessageBox.warning(self, "تحذير", "يرجى اختيار شهر محدد للاستعلام عن الأداءات.")
                return

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إنشاء جدول الواجبات الشهرية إذا لم يكن موجوداً
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS monthly_duties (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    student_id INTEGER NOT NULL,
                    month TEXT NOT NULL,
                    year INTEGER NOT NULL,
                    amount_required REAL NOT NULL DEFAULT 0,
                    amount_paid REAL NOT NULL DEFAULT 0,
                    amount_remaining REAL NOT NULL DEFAULT 0,
                    payment_date TEXT,
                    payment_status TEXT NOT NULL DEFAULT 'غير مدفوع',
                    notes TEXT,
                    اسم_الاستاذ TEXT,
                    القسم TEXT,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (student_id) REFERENCES جدول_البيانات(id),
                    UNIQUE(student_id, month, year)
                )
            """)            # بناء الاستعلام للحصول على بيانات الطلاب مع حالة الدفع
            base_query = """
                SELECT jb.id, jb.اسم_التلميذ, jb.رمز_التلميذ, jb.النوع, jb.رقم_الهاتف_الأول,
                       jb.القسم,
                       COALESCE(jb.مبلغ_التسجيل, 0) as اجمالي_مبلغ_التسجيل,
                       COALESCE(jb.الواجب_الشهري, 0) as الواجب_الشهري,
                       COALESCE(md.payment_status, '') as payment_status
                FROM جدول_البيانات jb
                LEFT JOIN monthly_duties md ON jb.id = md.student_id
                    AND md.month = ? AND md.year = ? AND md.القسم = jb.القسم
            """

            params = [selected_month, selected_year]
            conditions = []

            # إضافة شرط القسم إذا تم اختياره
            if selected_section and selected_section != "اختر القسم":
                conditions.append("jb.القسم = ?")
                params.append(selected_section)

            # بناء الاستعلام النهائي
            if conditions:
                query = base_query + " WHERE " + " AND ".join(conditions) + " ORDER BY jb.id DESC"
            else:
                query = base_query + " ORDER BY jb.id DESC"

            cursor.execute(query, params)
            records = cursor.fetchall()

            # تحديث الجدول
            self.table.setRowCount(len(records))

            # تطبيق العرض الثابت للأعمدة
            self.set_fixed_column_widths()

            for row_index, record in enumerate(records):
                # إضافة مربع اختيار محسن في العمود الأول
                checkbox_widget = self.create_enhanced_checkbox(row_index)
                self.table.setCellWidget(row_index, 0, checkbox_widget)

                # ملء الأعمدة العادية (من 0 إلى 7)
                for col_index in range(8):
                    value = record[col_index] if col_index < len(record) else ""

                    # معالجة القيم الخاصة للأعمدة المالية
                    if col_index in [6, 7] and value:  # الأعمدة المالية
                        display_value = f"{float(value):.2f} درهم" if value else "0.00 درهم"
                    else:
                        display_value = str(value) if value is not None else ""

                    item = QTableWidgetItem(display_value)
                    item.setTextAlignment(Qt.AlignCenter)
                    item.setFont(QFont("Calibri", 13, QFont.Bold))

                    # تلوين بعض الأعمدة
                    if col_index == 0:  # ID
                        item.setBackground(QColor("#ecf0f1"))
                    elif col_index == 1:  # اسم التلميذ
                        item.setForeground(QColor("#2c3e50"))
                    elif col_index in [6, 7]:  # الأعمدة المالية
                        item.setBackground(QColor("#e8f5e8"))
                        item.setForeground(QColor("#27ae60"))

                    # وضع العنصر في العمود المناسب (col_index + 1 للإزاحة)
                    self.table.setItem(row_index, col_index + 1, item)

                # إضافة عمود حالة الدفع مع الرموز (العمود 9)
                payment_status = record[8] if len(record) > 8 else ""
                status_item = QTableWidgetItem()

                if payment_status == "مدفوع كاملاً" or payment_status == "مدفوع":
                    status_item.setText("✅ مدفوع")
                    status_item.setBackground(QColor("#d4edda"))
                    status_item.setForeground(QColor("#155724"))
                elif payment_status == "مدفوع جزئياً":
                    status_item.setText("🔶 مدفوع جزئياً")
                    status_item.setBackground(QColor("#fff3cd"))
                    status_item.setForeground(QColor("#856404"))
                else:
                    # حالة الدفع فارغة حتى يستعلم المستخدم
                    status_item.setText("")
                    status_item.setBackground(QColor("#ffffff"))
                    status_item.setForeground(QColor("#000000"))
                    status_item.setTextAlignment(Qt.AlignCenter)
                status_item.setFont(QFont("Calibri", 13, QFont.Bold))
                self.table.setItem(row_index, 10, status_item)

            conn.close()

            # تحديث عنوان النافذة
            filter_info = f" - استعلام الأداءات: {selected_month}/{selected_year}"
            if selected_section and selected_section != "اختر القسم":
                filter_info += f" - القسم: {selected_section}"

            self.setWindowTitle(f"عرض بيانات الطلاب{filter_info} - إجمالي السجلات: {len(records)}")

            # إعادة حساب ارتفاع الجدول بعد تحميل البيانات
            if len(records) > 0:
                self.adjust_table_height()
                self.update_table_size()

            # تحديث العداد
            self.update_selection_counter()

            # إظهار رسالة نجاح
            paid_count = sum(1 for record in records if record[8] in ["مدفوع كاملاً", "مدفوع"])
            unpaid_count = len(records) - paid_count

            QMessageBox.information(self, "نتائج الاستعلام",
                f"تم الاستعلام عن أداءات شهر {selected_month}/{selected_year}\n\n"
                f"إجمالي الطلاب: {len(records)}\n"
                f"المدفوعين: {paid_count}\n"
                f"غير المدفوعين: {unpaid_count}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في استعلام حالة الدفع: {str(e)}")

    def update_selection_counter(self):
        """تحديث عداد التحديدات والتصفيات"""
        try:
            # عدد الصفوف المحددة
            selected_count = 0
            for row in range(self.table.rowCount()):
                checkbox_item = self.table.item(row, 0)
                if checkbox_item and checkbox_item.checkState() == Qt.Checked:
                    selected_count += 1

            # عدد الصفوف المعروضة (غير المخفية)
            displayed_count = 0
            for row in range(self.table.rowCount()):
                if not self.table.isRowHidden(row):
                    displayed_count += 1

            # العدد الإجمالي للطلاب في قاعدة البيانات
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM جدول_البيانات")
                total_count = cursor.fetchone()[0]
                conn.close()
            except:
                total_count = self.table.rowCount()

            # تحديث النص
            self.selection_counter_label.setText(f"المحدد: {selected_count} | المعروض: {displayed_count} | الإجمالي: {total_count}")

        except Exception as e:
            print(f"خطأ في تحديث عداد التحديدات: {str(e)}")

    def get_selected_student_id(self):
        """الحصول على ID التلميذ المحدد"""
        selected_ids = []
        for row in range(self.table.rowCount()):
            checkbox_widget = self.table.cellWidget(row, 0)
            if checkbox_widget:
                checkbox = checkbox_widget.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    # ID موجود في العمود الثاني (فهرس 1)
                    id_item = self.table.item(row, 1)
                    if id_item:
                        try:
                            student_id = int(id_item.text())
                            selected_ids.append(student_id)
                        except ValueError:
                            continue
        return selected_ids

    def select_row_manually(self, row_index):
        """تحديد صف يدوياً في الكود"""
        try:
            if 0 <= row_index < self.table.rowCount():
                # إلغاء تحديد جميع الصفوف أولاً
                self.clear_all_selections()

                # تحديد الصف المطلوب
                checkbox_widget = self.table.cellWidget(row_index, 0)
                if checkbox_widget:
                    checkbox = checkbox_widget.findChild(QCheckBox)
                    if checkbox:
                        checkbox.setChecked(True)
                        print(f"✅ تم تحديد الصف {row_index} يدوياً")
                        return True
                else:
                    print(f"❌ لم يتم العثور على مربع اختيار في الصف {row_index}")
                    return False
            else:
                print(f"❌ رقم الصف {row_index} خارج النطاق المسموح (0-{self.table.rowCount()-1})")
                return False
        except Exception as e:
            print(f"❌ خطأ في تحديد الصف يدوياً: {str(e)}")
            return False

    def handle_registration(self):
        """التعامل مع التسجيل وإعادة التسجيل"""
        try:
            selected_ids = self.get_selected_student_id()

            if len(selected_ids) == 0:
                # الحالة الأولى: لا يوجد تلميذ محدد - فتح النافذة في وضع الإضافة
                self.open_registration_window_add_mode()

            elif len(selected_ids) == 1:
                student_id = selected_ids[0]

                # إنشاء قائمة منسدلة تحت الزر
                self.show_registration_dropdown(student_id)

            else:
                # أكثر من تلميذ محدد
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "يرجى تحديد تلميذ واحد فقط أو عدم تحديد أي تلميذ للإضافة الجديدة."
                )

            # إزالة التحديد بعد انجاز العملية
            self.clear_all_selections()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح نافذة التسجيل: {str(e)}")

    def show_registration_dropdown(self, student_id):
        """إظهار قائمة منسدلة احترافية مثل نافذة الحضور والغياب"""
        try:
            from PyQt5.QtWidgets import QMenu

            # إنشاء قائمة منسدلة مثل نافذة الحضور والغياب
            menu = QMenu(self)
            menu.setFont(QFont("Calibri", 12, QFont.Bold))
            menu.setStyleSheet("""
                QMenu {
                    background-color: white;
                    border: 2px solid #3498db;
                    border-radius: 8px;
                    padding: 5px;
                }
                QMenu::item {
                    background-color: transparent;
                    padding: 8px 20px;
                    border-radius: 4px;
                    color: #2c3e50;
                }
                QMenu::item:selected {
                    background-color: #3498db;
                    color: white;
                }
            """)            # تعديل بيانات التلميذ
            edit_action = menu.addAction("✏️ تعديل بيانات التلميذ وتحديثها")
            edit_action.triggered.connect(lambda: self.handle_dropdown_edit_choice(menu, student_id))

            # البحث عن زر التسجيل لعرض القائمة تحته
            registration_btn = None
            for btn in self.findChildren(QPushButton):
                if "التسجيل وإعادة التسجيل" in btn.text():
                    registration_btn = btn
                    break

            # عرض القائمة
            if registration_btn:
                menu.exec_(registration_btn.mapToGlobal(registration_btn.rect().bottomLeft()))
            else:
                # موقع افتراضي
                menu.exec_(self.mapToGlobal(self.rect().center()))

        except Exception as e:
            print(f"خطأ في إظهار القائمة المنسدلة: {str(e)}")

    def handle_dropdown_edit_choice(self, menu, student_id):
        """التعامل مع اختيار تعديل البيانات من القائمة المنسدلة"""        # إغلاق القائمة المنسدلة
        menu.close()
        self.open_registration_window_edit_mode(student_id)

    def clear_selection(self):
        """إزالة تحديد جميع الصفوف ومربعات الاختيار - استدعاء clear_all_selections"""
        self.clear_all_selections()

    def generate_next_student_code(self):
        """إنشاء رمز التلميذ التالي تلقائياً بالبحث في جدول_البيانات عمود رمز_التلميذ"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # البحث عن جميع أرقام التلاميذ الموجودة في عمود رمز_التلميذ
            cursor.execute("""
                SELECT رمز_التلميذ FROM جدول_البيانات
                WHERE رمز_التلميذ IS NOT NULL AND رمز_التلميذ != ''
                ORDER BY رمز_التلميذ
            """)

            results = cursor.fetchall()
            conn.close()

            # استخراج جميع الأرقام الصحيحة
            numbers = []
            for result in results:
                code = result[0].strip()
                # محاولة استخراج الرقم من الرمز
                if code.startswith('R') and len(code) > 1:
                    try:
                        number = int(code[1:])
                        numbers.append(number)
                    except ValueError:
                        continue
                else:
                    # إذا كان الرمز رقم فقط
                    try:
                        number = int(code)
                        numbers.append(number)
                    except ValueError:
                        continue

            if numbers:
                # العثور على أعلى رقم وإضافة 1
                max_number = max(numbers)
                next_number = max_number + 1
                print(f"تم العثور على أعلى رقم: {max_number}, الرقم التالي: {next_number}")
                return f"R{next_number}"
            else:
                # لا توجد أرقام صحيحة، ابدأ من R10000
                print("لا توجد أرقام صحيحة، البدء من R10000")
                return "R10000"

        except Exception as e:
            print(f"خطأ في إنشاء رمز التلميذ: {str(e)}")
            return "R10000"

    def open_registration_window_add_mode(self):
        """فتح نافذة التسجيل في وضع الإضافة"""
        try:            # استيراد النافذة المطلوبة
            from student_multi_registration import StudentMultiSectionRegistrationWindow

            # إنشاء وفتح النافذة في وضع الإضافة
            self.registration_window = StudentMultiSectionRegistrationWindow(
                parent=self,
                db_path=self.db_path
            )

            # إضافة زر الحفظ في تبويب الواجبات الشهرية
            self.add_save_button_to_monthly_tab(is_update=False)

            # تعيين وضع الإضافة
            self.registration_window.current_student_id = None

            # التأكد من أن النافذة في وضع الإضافة أولاً (هذا سيمسح الحقول)
            if hasattr(self.registration_window, 'setup_add_mode'):
                self.registration_window.setup_add_mode()

            # إنشاء رمز التلميذ التلقائي وتعبئته بعد مسح الحقول - لا يتدخل المستخدم
            next_student_code = self.generate_next_student_code()
            print(f"رمز التلميذ التلقائي المُنشأ: {next_student_code}")

            if hasattr(self.registration_window, 'student_code_input'):
                # تعبئة رمز التلميذ تلقائ<|im_start|>
                self.registration_window.student_code_input.setText(next_student_code)

                # منع المستخدم من تعديل رمز التلميذ نهائ<|im_start|> - يتم تحديده تلقائ<|im_start|>
                self.registration_window.student_code_input.setReadOnly(True)
                self.registration_window.student_code_input.setEnabled(False)  # تعطيل الحقل تمام所有情节

                # تنسيق مرئي يوضح أن الحقل تلقائي
                self.registration_window.student_code_input.setStyleSheet("""
                    QLineEdit {
                        background-color: #e8e8e8;
                        color: #333;
                        border: 2px solid #bbb;
                        border-radius: 5px;
                        padding: 8px;
                        font-weight: bold;
                        font-size: 14px;
                    }
                """)

                print(f"تم تعبئة رمز التلميذ تلقائ<|im_start|> ومنع تعديله: {next_student_code}")
            else:
                print("تحذير: لم يتم العثور على حقل student_code_input في النافذة")

            # تغيير عنوان النافذة
            self.registration_window.setWindowTitle("إضافة تلميذ جديد")

            # إظهار النافذة
            self.registration_window.show()
            self.registration_window.raise_()
            self.registration_window.activateWindow()

        except ImportError:
            QMessageBox.critical(
                self,
                "خطأ",
                "لا يمكن العثور على نافذة التسجيل (student_multi_registration.py)."
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في فتح نافذة التسجيل: {str(e)}"
            )

    def open_registration_window_edit_mode(self, student_id):
        """فتح نافذة التسجيل لعرض/تعديل بيانات التلميذ"""
        try:
            # التحقق من وجود التلميذ في قاعدة البيانات
            if not self.student_exists(student_id):
                QMessageBox.warning(
                    self,
                    "تحذير",
                    f"لا يمكن العثور على التلميذ بالمعرف: {student_id}"
                )
                return

            # استيراد النافذة المطلوبة
            from student_multi_registration import StudentMultiSectionRegistrationWindow

            # إنشاء وفتح النافذة
            self.registration_window = StudentMultiSectionRegistrationWindow(
                parent=self,
                db_path=self.db_path
            )

            # إضافة زر التحديث في تبويب الواجبات الشهرية
            self.add_save_button_to_monthly_tab(is_update=True)

            # الحصول على تاريخ إنشاء التلميذ للتصفية
            creation_date = self.get_student_creation_date(student_id)

            # الحصول على جميع الأقسام المرتبطة بالتلميذ حسب تاريخ الإنشاء
            student_sections = self.get_student_sections_by_creation_date(student_id, creation_date)

            # تحميل بيانات التلميذ مع جميع أقسامه
            self.load_student_data_to_window(student_id, creation_date, student_sections)

            # تأمين حقل القسم في وضع التعديل
            self.secure_section_field_in_edit_mode()

            # تعيين ID للتحديث
            self.registration_window.current_student_id = student_id

            # تغيير عنوان النافذة مع تاريخ الإنشاء
            self.registration_window.setWindowTitle(f"تعديل بيانات التلميذ - ID: {student_id} - تاريخ الإنشاء: {creation_date}")

            # إظهار النافذة
            self.registration_window.show()
            self.registration_window.raise_()
            self.registration_window.activateWindow()

        except ImportError:
            QMessageBox.critical(
                self,
                "خطأ",
                "لا يمكن العثور على نافذة التسجيل (student_multi_registration.py)."
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في فتح نافذة التسجيل: {str(e)}"
            )

    def add_save_button_to_monthly_tab(self, is_update=False, is_add_section=False):
        """إضافة زر الحفظ/التحديث إلى تبويب الواجبات الشهرية"""
        try:
            window = self.registration_window

            # البحث عن تبويب الواجبات الشهرية
            tab_widget = window.tab_widget
            monthly_tab_index = -1

            for i in range(tab_widget.count()):
                if "الواجبات الشهرية" in tab_widget.tabText(i):
                    monthly_tab_index = i
                    break

            if monthly_tab_index == -1:
                return

            # الحصول على تبويب الواجبات الشهرية
            monthly_tab = tab_widget.widget(monthly_tab_index)
            layout = monthly_tab.layout()

            # إنشاء إطار الأزرار
            from PyQt5.QtWidgets import QHBoxLayout
            buttons_layout = QHBoxLayout()
            buttons_layout.setSpacing(15)

            # إنشاء الزر المناسب
            if is_update:
                save_button = window.create_styled_button("🔄 تحديث البيانات", "#2E7D32")
                save_button.clicked.connect(lambda: self.update_student_data())
            else:
                save_button = window.create_styled_button("💾 حفظ البيانات الجديدة", "#2E7D32")
                save_button.clicked.connect(lambda: self.save_new_student_data())

            # إضافة الزر للتخطيط
            buttons_layout.addStretch()
            buttons_layout.addWidget(save_button)
            buttons_layout.addStretch()

            # إضافة التخطيط إلى التبويب
            layout.addLayout(buttons_layout)

            # حفظ مرجع للزر
            window.save_button = save_button

        except Exception as e:
            print(f"خطأ في إضافة زر الحفظ: {str(e)}")

    def student_exists(self, student_id):
        """التحقق من وجود التلميذ في قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT COUNT(*) FROM جدول_البيانات WHERE id = ?", (student_id,))
            count = cursor.fetchone()[0]

            conn.close()
            return count > 0
        except Exception:
            return False

    def load_student_basic_data_to_window(self, student_id):
        """تحميل البيانات الأساسية للتلميذ (بدون القسم) إلى النافذة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # جلب بيانات التلميذ
            cursor.execute("SELECT * FROM جدول_البيانات WHERE id = ?", (student_id,))
            record = cursor.fetchone()
            conn.close()

            if record and hasattr(self, 'registration_window'):
                window = self.registration_window

                # ملء الحقول الأساسية فقط
                window.student_name_input.setText(record[1] or "")
                window.student_code_input.setText(record[2] or "")

                if record[3]:  # النوع
                    gender_index = window.gender_combo.findText(record[3])
                    if gender_index >= 0:
                        window.gender_combo.setCurrentIndex(gender_index)

                window.phone_input.setText(record[4] or "")
                window.phone2_input.setText(record[5] or "")
                window.address_input.setPlainText(record[6] or "")

                # ترك حقل القسم فارغ للاختيار الجديد
                window.section_combo.setCurrentIndex(0)
                window.institution_combo.setCurrentIndex(0)

                # إعادة تعيين الحقول المالية
                window.total_amount_input.clear()
                window.monthly_duty_input.clear()

                # إلغاء تحديد جميع الأشهر
                for checkbox in window.month_checkboxes.values():
                    checkbox.setChecked(False)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات الأساسية للتلميذ: {str(e)}")

    def load_student_data_to_window(self, student_id, creation_date=None, student_sections=None):
        """تحميل بيانات التلميذ مع جميع أقسامه إلى النافذة"""
        try:
            # إذا تم تمرير بيانات الأقسام، استخدمها مباشرة
            if student_sections:
                print(f"✅ تحميل {len(student_sections)} قسم للتلميذ من البيانات المرسلة")

                # التحقق من وجود دالة تحميل الأقسام المتعددة في نافذة التسجيل
                if hasattr(self.registration_window, 'load_multiple_sections_data'):
                    # تمرير جميع بيانات الأقسام لنافذة التسجيل
                    self.registration_window.load_multiple_sections_data(student_sections, creation_date)
                    print(f"✅ تم تحميل جميع الأقسام المرتبطة بتاريخ الإنشاء: {creation_date}")
                else:
                    # إذا لم تكن الدالة متاحة، عرض رسالة تحذيرية واستخدم الطريقة التقليدية
                    print("⚠️ دالة load_multiple_sections_data غير متاحة في نافذة التسجيل")
                    print("💡 يرجى تحديث ملف student_multi_registration.py لدعم تحميل الأقسام المتعددة")

                    # استخدام الطريقة التقليدية كبديل
                    self.load_single_section_data(student_id)

                    # عرض رسالة للمستخدم
                    QMessageBox.information(
                        self,
                        "معلومات",
                        f"تم العثور على {len(student_sections)} قسم للتلميذ بتاريخ الإنشاء: {creation_date}\n\n"
                        f"سيتم تحميل القسم الأول فقط في وضع التعديل الحالي.\n"
                        f"لتحميل جميع الأقسام، يرجى تحديث نافذة التسجيل."
                    )
            else:
                # الطريقة التقليدية لتحميل قسم واحد
                self.load_single_section_data(student_id)

        except Exception as e:
            print(f"خطأ في تحميل بيانات التلميذ: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات التلميذ: {str(e)}")

    def load_single_section_data(self, student_id):
        """تحميل بيانات قسم واحد للتلميذ (الطريقة التقليدية)"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # جلب بيانات التلميذ
            cursor.execute("SELECT * FROM جدول_البيانات WHERE id = ?", (student_id,))
            record = cursor.fetchone()
            conn.close()

            if record and hasattr(self, 'registration_window'):
                window = self.registration_window

                # ملء الحقول ببيانات التلميذ
                window.student_name_input.setText(record[1] or "")
                window.student_code_input.setText(record[2] or "")

                if record[3]:  # النوع
                    gender_index = window.gender_combo.findText(record[3])
                    if gender_index >= 0:
                        window.gender_combo.setCurrentIndex(gender_index)

                window.phone_input.setText(record[4] or "")
                window.phone2_input.setText(record[5] or "")
                window.address_input.setPlainText(record[6] or "")

                # معلومات التمدرس
                if record[7]:  # القسم
                    window.section_combo.setCurrentText(record[8])

                if record[9]:  # المؤسسة الأصلية
                    window.institution_combo.setCurrentText(record[9])

                # واجبات التسجيل - إجمالي المبلغ فقط
                if record[10]:  # إجمالي مبلغ التسجيل
                    window.total_amount_input.setText(str(record[10]))

                # الواجبات الشهرية
                if record[13]:  # الواجب الشهري
                    window.monthly_duty_input.setText(str(record[13]))

                # الأشهر المحددة
                if record[14]:
                    try:
                        selected_months = json.loads(record[14])
                        for month, checkbox in window.month_checkboxes.items():
                            checkbox.setChecked(month in selected_months)
                    except:
                        pass

                # تحديث الحسابات
                window.calculate_total_monthly()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات التلميذ: {str(e)}")

    def save_new_student_data(self):
        """حفظ بيانات تلميذ جديد"""
        try:
            window = self.registration_window

            # التحقق من البيانات الأساسية
            if not window.student_name_input.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم التلميذ.")
                return

            if not window.student_code_input.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال رمز التلميذ.")
                return

            # حفظ البيانات في قاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # جمع البيانات
            selected_months = []
            for month, checkbox in window.month_checkboxes.items():
                if checkbox.isChecked():
                    selected_months.append(month)

            # حساب القيم
            total_amount = float(window.total_amount_input.text() or 0)
            monthly_duty = float(window.monthly_duty_input.text() or 0)
            total_monthly = monthly_duty * len(selected_months)

            # إدراج السجل الجديد
            cursor.execute('''
                INSERT INTO جدول_البيانات
                (اسم_التلميذ, رمز_التلميذ, النوع, رقم_الهاتف_الأول, رقم_الهاتف_الثاني, ملاحظات,
                 القسم, المؤسسة_الاصلية,
                 اجمالي_مبلغ_التسجيل, عدد_الاقساط, مبلغ_القسط,
                 الواجب_الشهري, الاشهر_المحددة, المبلغ_النهائي_الشهري)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                window.student_name_input.text().strip(),
                window.student_code_input.text().strip(),
                window.gender_combo.currentText(),
                window.phone_input.text().strip(),
                window.phone2_input.text().strip(),
                window.address_input.toPlainText().strip(),
                window.section_combo.currentText(),
                window.institution_combo.currentText(),
                total_amount,
                1,  # قيمة افتراضية لعدد الأقساط
                total_amount,  # مبلغ القسط = إجمالي المبلغ (قسط واحد)
                monthly_duty,
                json.dumps(selected_months),
                total_monthly
            ))

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", "تم حفظ بيانات التلميذ الجديد بنجاح.")

            # تحديث الجدول
            self.refresh_data()

            # إزالة التحديد
            self.clear_all_selections()

            # إغلاق النافذة
            window.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ البيانات: {str(e)}")

    def save_new_section_for_student(self):
        """حفظ قسم جديد للتلميذ الموجود"""
        try:
            window = self.registration_window
            student_id = window.current_student_id

            if not student_id:
                QMessageBox.warning(self, "تحذير", "لا يمكن تحديد التلميذ.")
                return

            # التحقق من البيانات الأساسية
            if not window.section_combo.currentText().strip():
                QMessageBox.warning(self, "تحذير", "يرجى اختيار قسم جديد.")
                return



            # التحقق من عدم تكرار القسم للتلميذ نفسه
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT COUNT(*) FROM جدول_البيانات
                WHERE id != ? AND رمز_التلميذ = (
                    SELECT رمز_التلميذ FROM جدول_البيانات WHERE id = ?
                ) AND القسم = ?
            """, (student_id, student_id, window.section_combo.currentText()))

            if cursor.fetchone()[0] > 0:
                conn.close()
                QMessageBox.warning(
                    self,
                    "تحذير",
                    f"التلميذ مسجل بالفعل في القسم '{window.section_combo.currentText()}'.\n"
                    f"لا يمكن تسجيل نفس التلميذ في نفس القسم مرتين."
                )
                return

            # جلب البيانات الأساسية للتلميذ
            cursor.execute("SELECT * FROM جدول_البيانات WHERE id = ?", (student_id,))
            original_record = cursor.fetchone()

            if not original_record:
                conn.close()
                QMessageBox.warning(self, "تحذير", "لا يمكن العثور على بيانات التلميذ الأصلية.")
                return

            # جمع البيانات الجديدة
            selected_months = []
            for month, checkbox in window.month_checkboxes.items():
                if checkbox.isChecked():
                    selected_months.append(month)

            # حساب القيم
            total_amount = float(window.total_amount_input.text() or 0)
            monthly_duty = float(window.monthly_duty_input.text() or 0)
            total_monthly = monthly_duty * len(selected_months)

            # إنشاء رمز تلميذ جديد للقسم الجديد
            new_student_code = self.generate_next_student_code()

            # إدراج السجل الجديد (نسخة من التلميذ في قسم جديد)
            cursor.execute('''
                INSERT INTO جدول_البيانات
                (اسم_التلميذ, رمز_التلميذ, النوع, رقم_الهاتف_الأول, رقم_الهاتف_الثاني, ملاحظات,
                 القسم, المؤسسة_الاصلية,
                 اجمالي_مبلغ_التسجيل, عدد_الاقساط, مبلغ_القسط,
                 الواجب_الشهري, الاشهر_المحددة, المبلغ_النهائي_الشهري)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                original_record[1],  # اسم التلميذ (نفس الاسم)
                new_student_code,    # رمز تلميذ جديد
                original_record[3],  # النوع
                original_record[4],  # رقم الهاتف الأول
                original_record[5],  # رقم الهاتف الثاني
                f"تسجيل إضافي في قسم {window.section_combo.currentText()}",  # ملاحظة
                window.section_combo.currentText(),   # القسم الجديد
                window.institution_combo.currentText(),  # المؤسسة
                total_amount,
                1,  # قيمة افتراضية لعدد الأقساط
                total_amount,  # مبلغ القسط = إجمالي المبلغ
                monthly_duty,
                json.dumps(selected_months),
                total_monthly
            ))

            conn.commit()
            conn.close()

            QMessageBox.information(
                self,
                "نجح",
                f"تم تسجيل التلميذ في القسم الجديد بنجاح!\n\n"
                f"القسم الجديد: {window.section_combo.currentText()}\n"
                f"رمز التلميذ الجديد: {new_student_code}"
            )

            # تحديث الجدول
            self.refresh_data()

            # إزالة التحديد
            self.clear_all_selections()

            # إغلاق النافذة
            window.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ القسم الجديد: {str(e)}")

    def update_student_data(self):
        """تحديث بيانات التلميذ الموجود"""
        try:
            window = self.registration_window
            student_id = window.current_student_id

            if not student_id:
                QMessageBox.warning(self, "تحذير", "لا يمكن تحديد التلميذ للتحديث.")
                return

            # التحقق من البيانات الأساسية
            if not window.student_name_input.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم التلميذ.")
                return

            # تحديث البيانات في قاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # جمع البيانات
            selected_months = []
            for month, checkbox in window.month_checkboxes.items():
                if checkbox.isChecked():
                    selected_months.append(month)

            # حساب القيم
            total_amount = float(window.total_amount_input.text() or 0)
            monthly_duty = float(window.monthly_duty_input.text() or 0)
            total_monthly = monthly_duty * len(selected_months)

            # تحديث السجل
            cursor.execute('''
                UPDATE جدول_البيانات
                SET اسم_التلميذ = ?, رمز_التلميذ = ?, النوع = ?, رقم_الهاتف_الأول = ?,
                    رقم_الهاتف_الثاني = ?, ملاحظات = ?,
                    القسم = ?, المؤسسة_الاصلية = ?,
                    اجمالي_مبلغ_التسجيل = ?, عدد_الاقساط = ?, مبلغ_القسط = ?,
                    الواجب_الشهري = ?, الاشهر_المحددة = ?, المبلغ_النهائي_الشهري = ?,
                    تاريخ_التحديث = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (
                window.student_name_input.text().strip(),
                window.student_code_input.text().strip(),
                window.gender_combo.currentText(),
                window.phone_input.text().strip(),
                window.phone2_input.text().strip(),
                window.address_input.toPlainText().strip(),
                window.section_combo.currentText(),
                window.institution_combo.currentText(),
                total_amount,
                1,  # قيمة افتراضية لعدد الأقساط
                total_amount,  # مبلغ القسط = إجمالي المبلغ (قسط واحد)
                monthly_duty,
                json.dumps(selected_months),
                total_monthly,
                student_id
            ))

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", "تم تحديث بيانات التلميذ بنجاح.")

            # تحديث الجدول
            self.refresh_data()

            # إزالة التحديد
            self.clear_all_selections()

            # إغلاق النافذة
            window.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحديث البيانات: {str(e)}")

    def handle_monthly_duties(self):
        """التعامل مع أداء الواجبات الشهرية"""
        try:
            selected_ids = self.get_selected_student_id()

            if len(selected_ids) == 0:
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "يرجى تحديد تلميذ واحد لإدارة واجباته الشهرية."
                )
                return

            elif len(selected_ids) == 1:
                # تلميذ واحد محدد - فتح نافذة إدارة الواجبات
                student_id = selected_ids[0]
                self.open_monthly_duties_window(student_id)

            else:
                # أكثر من تلميذ محدد
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "يرجى تحديد تلميذ واحد فقط لإدارة واجباته الشهرية."
                )

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء فتح نافذة الواجبات الشهرية: {str(e)}"
            )

    def open_monthly_duties_window(self, student_id):
        """فتح نافذة إدارة الواجبات الشهرية"""
        try:
            # التحقق من وجود التلميذ في قاعدة البيانات
            if not self.student_exists(student_id):
                QMessageBox.warning(
                    self,
                    "تحذير",
                    f"لا يمكن العثور على التلميذ بالمعرف: {student_id}"
                )
                return

            # محاولة استيراد وفتح نافذة الواجبات الشهرية
            try:
                from monthly_duties_window import MonthlyDutiesManagementWindow

                # إنشاء وفتح النافذة
                self.monthly_duties_window = MonthlyDutiesManagementWindow(
                    parent=self,
                    db_path=self.db_path,
                    student_id=student_id
                )

                # إظهار النافذة
                self.monthly_duties_window.show()
                self.monthly_duties_window.raise_()
                self.monthly_duties_window.activateWindow()

            except ImportError:
                QMessageBox.critical(
                    self,
                    "خطأ في الاستيراد",
                    "لا يمكن العثور على نافذة الواجبات الشهرية.\n"
                    "تأكد من وجود ملف monthly_duties_window.py"
                )
                return

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في فتح نافذة الواجبات الشهرية: {str(e)}"
            )

    def handle_detailed_report(self):
        """التعامل مع طباعة التقرير المفصل"""
        try:
            selected_ids = self.get_selected_student_id()

            if len(selected_ids) == 0:
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "يرجى تحديد تلميذ واحد لإنشاء تقرير مفصل عنه."
                )
                return

            elif len(selected_ids) == 1:
                # تلميذ واحد محدد - إنشاء تقرير PDF مباشر
                student_id = selected_ids[0]
                self.create_pdf_detailed_report(student_id)

            else:
                # أكثر من تلميذ محدد
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "يرجى تحديد تلميذ واحد فقط لإنشاء التقرير المفصل."
                )

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء إنشاء التقرير المفصل: {str(e)}"
            )

    def create_pdf_detailed_report(self, student_id):
        """إنشاء تقرير PDF مفصل باستخدام print101"""
        try:
            # التحقق من وجود التلميذ في قاعدة البيانات
            if not self.student_exists(student_id):
                QMessageBox.warning(
                    self,
                    "تحذير",
                    f"لا يمكن العثور على التلميذ بالمعرف: {student_id}"
                )
                return

            # محاولة استيراد وتشغيل تقرير PDF
            try:
                from print101 import print_student_detailed_report
            except ImportError:
                QMessageBox.critical(
                    self,
                    "خطأ",
                    "لا يمكن العثور على وحدة التقرير.\n"
                    "تأكد من وجود ملف print101.py"
                )
                return

            # إنشاء التقرير
            try:
                success, output_path, message = print_student_detailed_report(
                    parent=self,
                    student_id=student_id
                )

                if success:
                    QMessageBox.information(
                        self,
                        "نجح",
                        f"تم إنشاء التقرير المفصل بنجاح!\n\n"
                        f"المسار: {output_path}\n\n{message}"
                    )
                else:
                    QMessageBox.critical(
                        self,
                        "خطأ",
                        f"فشل في إنشاء التقرير المفصل.\n\nالسبب: {message}"
                    )

            except Exception as creation_error:
                QMessageBox.critical(
                    self,
                    "خطأ",
                    f"حدث خطأ أثناء إنشاء التقرير: {str(creation_error)}"
                )
                return

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في إنشاء التقرير المفصل: {str(e)}"
            )

    def handle_section_monthly_report(self):
        """التعامل مع تقرير القسم الشهري"""
        try:
            # الحصول على القسم المحدد من التصفية
            selected_section = self.section_filter_combo.currentText()

            if not selected_section or selected_section == "اختر القسم":
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "يرجى تحديد قسم من قائمة التصفية أولاً لإنشاء التقرير الشهري."
                )
                return

            # فتح نافذة اختيار الشهر
            self.show_month_selection_dialog(selected_section)

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء إنشاء تقرير القسم الشهري: {str(e)}"
            )

    def show_month_selection_dialog(self, section):
        """إظهار نافذة اختيار الشهر"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QComboBox

            dialog = QDialog(self)
            dialog.setWindowTitle(f"اختيار الشهر لتقرير القسم: {section}")
            dialog.setModal(True)
            dialog.setLayoutDirection(Qt.RightToLeft)
            dialog.resize(400, 200)

            layout = QVBoxLayout(dialog)

            # عنوان الحوار
            title_label = QLabel(f"اختر الشهر لإنشاء تقرير القسم: {section}")
            title_label.setFont(QFont("Calibri", 14, QFont.Bold))
            title_label.setStyleSheet("color: #2c3e50; margin: 10px;")
            layout.addWidget(title_label)

            # قائمة الأشهر
            month_layout = QHBoxLayout()
            month_label = QLabel("الشهر:")
            month_label.setFont(QFont("Calibri", 12, QFont.Bold))

            month_combo = QComboBox()
            month_combo.setFont(QFont("Calibri", 12))
            months = [
                "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
            ]
            month_combo.addItems(months)

            # تحديد الشهر الحالي
            from datetime import datetime
            current_month = datetime.now().month - 1  # فهرس الشهر (0-11)
            month_combo.setCurrentIndex(current_month)

            month_layout.addWidget(month_label)
            month_layout.addWidget(month_combo)
            month_layout.addStretch()
            layout.addLayout(month_layout)

            # الأزرار
            button_layout = QHBoxLayout()

            # زر إنشاء التقرير
            create_btn = QPushButton("📊 إنشاء التقرير")
            create_btn.setFont(QFont("Calibri", 12, QFont.Bold))
            create_btn.setStyleSheet("""
                QPushButton {
                    background-color: #17a2b8;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 15px;
                    margin: 5px;
                }
                QPushButton:hover {
                    background-color: #138496;
                }
            """)
            create_btn.clicked.connect(lambda: self.create_section_monthly_report(
                dialog, section, month_combo.currentText()
            ))

            # زر الإلغاء
            cancel_btn = QPushButton("❌ إلغاء")
            cancel_btn.setFont(QFont("Calibri", 12, QFont.Bold))
            cancel_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 15px;
                    margin: 5px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)
            cancel_btn.clicked.connect(dialog.reject)

            button_layout.addWidget(create_btn)
            button_layout.addWidget(cancel_btn)

            layout.addLayout(button_layout)

            # عرض الحوار
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إظهار نافذة اختيار الشهر: {str(e)}")

    def create_section_monthly_report(self, dialog, section, month):
        """إنشاء تقرير القسم الشهري"""
        try:
            dialog.accept()  # إغلاق النافذة

            # محاولة استيراد وحدة التقرير
            try:
                from print_section_monthly import print_section_monthly_report
            except ImportError:
                QMessageBox.critical(
                    self,
                    "خطأ",
                    "لا يمكن العثور على وحدة تقرير القسم الشهري.\n"
                    "تأكد من وجود ملف print_section_monthly.py"
                )
                return

            # إنشاء التقرير
            try:
                success, output_path, message = print_section_monthly_report(
                    parent=self,
                    section=section,
                    month=month
                )

                if success:
                    QMessageBox.information(
                        self,
                        "نجح",
                        f"تم إنشاء تقرير القسم الشهري بنجاح!\n\n"
                        f"القسم: {section}\n"
                        f"الشهر: {month}\n"
                        f"المسار: {output_path}\n\n{message}"
                    )
                else:
                    QMessageBox.critical(
                        self,
                        "خطأ",
                        f"فشل في إنشاء تقرير القسم الشهري.\n\nالسبب: {message}"
                    )

            except Exception as creation_error:
                QMessageBox.critical(
                    self,
                    "خطأ",
                    f"حدث خطأ أثناء إنشاء التقرير: {str(creation_error)}"
                )
                return

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في إنشاء تقرير القسم الشهري: {str(e)}"
            )

    def handle_section_yearly_report(self):
        """التعامل مع تقرير القسم السنوي"""
        try:
            # الحصول على القسم المحدد من التصفية
            selected_section = self.section_filter_combo.currentText()

            if not selected_section or selected_section == "اختر القسم":
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "يرجى تحديد قسم من قائمة التصفية أولاً لإنشاء التقرير السنوي."
                )
                return

            # فتح نافذة اختيار السنة
            self.show_year_selection_dialog(selected_section)

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء إنشاء تقرير القسم السنوي: {str(e)}"
            )

    def show_year_selection_dialog(self, section):
        """إظهار نافذة اختيار السنة"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QComboBox
            from datetime import datetime

            dialog = QDialog(self)
            dialog.setWindowTitle(f"اختيار السنة لتقرير القسم: {section}")
            dialog.setModal(True)
            dialog.setLayoutDirection(Qt.RightToLeft)
            dialog.resize(400, 200)

            layout = QVBoxLayout(dialog)

            # عنوان الحوار
            title_label = QLabel(f"اختر السنة لإنشاء تقرير القسم: {section}")
            title_label.setFont(QFont("Calibri", 14, QFont.Bold))
            title_label.setStyleSheet("color: #2c3e50; margin: 10px;")
            layout.addWidget(title_label)

            # قائمة السنوات
            year_layout = QHBoxLayout()
            year_label = QLabel("السنة:")
            year_label.setFont(QFont("Calibri", 12, QFont.Bold))
            year_combo = QComboBox()
            year_combo.setFont(QFont("Calibri", 12, QFont.Bold))

            # إضافة السنوات (السنة الحالية و 5 سنوات سابقة)
            current_year = datetime.now().year
            for year in range(current_year, current_year - 6, -1):
                year_combo.addItem(str(year))

            year_layout.addWidget(year_label)
            year_layout.addWidget(year_combo)
            year_layout.addStretch()

            layout.addLayout(year_layout)

            # الأزرار
            button_layout = QHBoxLayout()

            # زر إنشاء التقرير
            create_btn = QPushButton("📈 إنشاء التقرير السنوي")
            create_btn.setFont(QFont("Calibri", 12, QFont.Bold))
            create_btn.setStyleSheet("""
                QPushButton {
                    background-color: #fd7e14;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 15px;
                    margin: 5px;
                }
                QPushButton:hover {
                    background-color: #e8650e;
                }
            """)
            create_btn.clicked.connect(lambda: self.create_section_yearly_report(
                dialog, section, year_combo.currentText()
            ))

            # زر الإلغاء
            cancel_btn = QPushButton("❌ إلغاء")
            cancel_btn.setFont(QFont("Calibri", 12, QFont.Bold))
            cancel_btn.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 15px;
                    margin: 5px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """)
            cancel_btn.clicked.connect(dialog.reject)

            button_layout.addWidget(create_btn)
            button_layout.addWidget(cancel_btn)
            button_layout.addStretch()

            layout.addLayout(button_layout)

            # عرض النافذة
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في إظهار نافذة اختيار السنة: {str(e)}"
            )

    def create_section_yearly_report(self, dialog, section, year):
        """إنشاء تقرير القسم السنوي"""
        try:
            dialog.accept()  # إغلاق النافذة

            # محاولة استيراد وحدة التقرير
            try:
                from print_section_yearly import print_section_yearly_report
            except ImportError:
                QMessageBox.critical(
                    self,
                    "خطأ",
                    "لا يمكن العثور على وحدة تقرير القسم السنوي.\n"
                    "تأكد من وجود ملف print_section_yearly.py"
                )
                return

            # إنشاء التقرير
            try:
                success, output_path, message = print_section_yearly_report(
                    parent=self,
                    section=section,
                    year=year
                )

                if success:
                    QMessageBox.information(
                        self,
                        "نجح",
                        f"تم إنشاء تقرير القسم السنوي بنجاح!\n\n"
                        f"القسم: {section}\n"
                        f"السنة: {year}\n"
                        f"المسار: {output_path}\n\n{message}"
                    )
                else:
                    QMessageBox.critical(
                        self,
                        "خطأ",
                        f"فشل في إنشاء تقرير القسم السنوي.\n\nالسبب: {message}"
                    )

            except Exception as creation_error:
                QMessageBox.critical(
                    self,
                    "خطأ",
                    f"حدث خطأ أثناء إنشاء التقرير: {str(creation_error)}"
                )
                return

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في إنشاء تقرير القسم السنوي: {str(e)}"
            )


    def handle_registration_fees_report(self):
        """التعامل مع تقرير واجبات التسجيل"""
        try:
            # الحصول على القسم المحدد من التصفية
            selected_section = self.section_filter_combo.currentText()

            if not selected_section or selected_section == "اختر القسم":
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "يرجى تحديد قسم من قائمة التصفية أولاً."
                )
                return

            # إنشاء التقرير مباشرة بدون اختيار شهر
            self.create_registration_fees_report_direct(selected_section)

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء إنشاء تقرير واجبات التسجيل: {str(e)}"
            )

    def create_registration_fees_report_direct(self, section):
        """إنشاء تقرير واجبات التسجيل مباشرة بدون اختيار شهر"""
        try:
            # محاولة استيراد وحدة التقرير
            try:
                from print_registration_fees_monthly_style import print_registration_fees_report
            except ImportError:
                QMessageBox.critical(
                    self,
                    "خطأ",
                    "لا يمكن العثور على وحدة تقرير واجبات التسجيل.\n"
                    "تأكد من وجود ملف print_registration_fees_monthly_style.py"
                )
                return

            # إنشاء التقرير
            try:
                success, output_path, message = print_registration_fees_report(
                    section=section
                )

                if success:
                    QMessageBox.information(
                        self,
                        "نجح",
                        f"تم إنشاء تقرير واجبات التسجيل بنجاح!\n\n"
                        f"القسم: {section}\n"
                        f"المسار: {output_path}\n\n{message}"
                    )
                else:
                    QMessageBox.critical(
                        self,
                        "خطأ",
                        f"فشل في إنشاء تقرير واجبات التسجيل.\n\nالسبب: {message}"
                    )

            except Exception as creation_error:
                QMessageBox.critical(
                    self,
                    "خطأ",
                    f"خطأ أثناء إنشاء التقرير: {str(creation_error)}"
                )

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في إنشاء تقرير واجبات التسجيل: {str(e)}"
            )

    def showEvent(self, event):
        """عند إظهار النافذة"""
        super().showEvent(event)

        # إذا كانت النافذة مستقلة، قم بتعظيمها
        if self.parent() is None:
            self.showMaximized()
        else:
            # إذا كانت مدمجة، تأكد من الحجم المناسب
            self.resize(1200, 900)
            # تعديل ارتفاع الجدول أولاً
            self.adjust_table_height()
            # ثم تحديث حجم الجدول
            self.update_table_size()

    def update_table_size(self):
        """تحديث حجم الجدول ليتناسب مع المساحة المتاحة"""
        try:
            if hasattr(self, 'table') and self.table:
                # تحديث حجم الجدول
                self.table.updateGeometry()

                # التأكد من أن الجدول يعرض جميع الصفوف
                if self.table.rowCount() > 0:
                    # التمرير إلى أعلى الجدول
                    self.table.scrollToTop()

                    # تطبيق الإعدادات الثابتة بدلاً من resizeColumnsToContents()
                    # self.table.resizeColumnsToContents()  # تم تعطيل هذا السطر لأنه يلغي الإعدادات الثابتة
                    self.set_fixed_column_widths()  # تطبيق الإعدادات الثابتة

                    # ضمان أن شريط التمرير العمودي يعمل بشكل صحيح
                    self.table.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

                    # إزالة adjustSize() لتجنب توسيع الجدول خارج حدود النافذة
                    # self.table.adjustSize()  # تم تعطيل هذا السطر لحل مشكلة اختفاء الجدول

                    # إجبار إعادة رسم الجدول
                    self.table.viewport().update()

        except Exception as e:
            print(f"خطأ في تحديث حجم الجدول: {str(e)}")

    def resizeEvent(self, event):
        """عند تغيير حجم النافذة"""
        super().resizeEvent(event)
        # تعديل ارتفاع الجدول أولاً
        self.adjust_table_height()
        # ثم تحديث حجم الجدول عند تغيير حجم النافذة
        self.update_table_size()

    def handle_edit_choice(self, dialog, student_id):
        """التعامل مع اختيار تعديل البيانات الحالية"""
        dialog.accept()
        self.open_registration_window_edit_mode(student_id)

    def get_student_info(self, student_id):
        """الحصول على معلومات التلميذ من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT اسم_التلميذ, رمز_التلميذ, النوع, رقم_الهاتف_الأول, رقم_الهاتف_الثاني
                FROM جدول_البيانات
                WHERE id = ?
                LIMIT 1
            """, (student_id,))

            result = cursor.fetchone()
            conn.close()

            if result:
                return {
                    'name': result[0],
                    'code': result[1],
                    'gender': result[2],
                    'phone1': result[3],
                    'phone2': result[4]
                }
            return None
        except Exception as e:
            print(f"خطأ في الحصول على معلومات التلميذ: {str(e)}")
            return None

    def handle_delete_student(self):
        """التعامل مع حذف التلميذ المحدد"""
        try:
            selected_ids = self.get_selected_student_id()

            if len(selected_ids) == 0:
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "يرجى تحديد تلميذ واحد لحذفه."
                )
                return
            elif len(selected_ids) > 1:
                QMessageBox.warning(
                    self,
                    "تحذير",
                    f"تم تحديد {len(selected_ids)} تلميذ. يرجى تحديد تلميذ واحد فقط للحذف."
                )
                return
            else:
                # تلميذ واحد محدد - التحقق من إمكانية الحذف
                student_id = selected_ids[0]
                self.delete_student_with_validation(student_id)

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء محاولة حذف التلميذ: {str(e)}"
            )

    def open_bulk_edit_window(self, selected_ids):
        """فتح نافذة التعديل الجماعي"""
        try:
            from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                                       QLabel, QLineEdit, QComboBox, QCheckBox, QGroupBox,
                                       QGridLayout, QScrollArea)

            # إنشاء النافذة
            dialog = QDialog(self)
            dialog.setWindowTitle(f"التعديل الجماعي - {len(selected_ids)} تلميذ محدد")
            dialog.setModal(True)
            dialog.setLayoutDirection(Qt.RightToLeft)
            dialog.resize(700, 600)  # زيادة الحجم لاستيعاب المحتوى
            dialog.setMinimumSize(650, 550)  # تحديد حد أدنى للحجم
            dialog.setStyleSheet("""
                QDialog {
                    background-color: #f8f9fa;
                }
                QGroupBox {
                    font-weight: bold;
                    font-size: 14px;
                    border: 2px solid #dee2e6;
                    border-radius: 8px;
                    margin-top: 10px;
                    padding-top: 10px;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px 0 5px;
                }
            """)

            layout = QVBoxLayout(dialog)

            # عنوان النافذة
            title_label = QLabel(f"🔄 التعديل الجماعي لـ {len(selected_ids)} تلميذ")
            title_label.setFont(QFont("Calibri", 16, QFont.Bold))
            title_label.setStyleSheet("color: #2c3e50; margin: 10px; text-align: center;")
            layout.addWidget(title_label)

            # مجموعة معلومات التمدرس
            study_group = QGroupBox("📚 معلومات التمدرس")
            study_layout = QGridLayout(study_group)





            # القسم الجديد
            section_check = QCheckBox("تغيير القسم:")
            section_check.setFont(QFont("Calibri", 13, QFont.Bold))
            section_combo = QComboBox()
            section_combo.setFont(QFont("Calibri", 13, QFont.Bold))
            section_combo.setEnabled(False)
            section_check.toggled.connect(section_combo.setEnabled)

            # تحميل الأقسام المتاحة
            self.load_sections_to_combo(section_combo)

            study_layout.addWidget(section_check, 1, 0)
            study_layout.addWidget(section_combo, 1, 1)

            layout.addWidget(study_group)

            # مجموعة الواجبات المالية
            financial_group = QGroupBox("💰 الواجبات المالية")
            financial_layout = QGridLayout(financial_group)

            # واجبات التسجيل
            registration_check = QCheckBox("تغيير واجبات التسجيل:")
            registration_check.setFont(QFont("Calibri", 13, QFont.Bold))
            registration_input = QLineEdit()
            registration_input.setFont(QFont("Calibri", 13, QFont.Bold))
            registration_input.setPlaceholderText("مثال: 200 درهم ")
            registration_input.setStyleSheet("""
                QLineEdit {
                    padding: 8px;
                    border: 2px solid #ced4da;
                    border-radius: 5px;
                    background-color: white;
                }
                QLineEdit:focus {
                    border: 2px solid #007bff;
                }
                QLineEdit:disabled {
                    background-color: #f8f9fa;
                    color: #6c757d;
                }
            """)
            registration_input.setEnabled(False)
            registration_check.toggled.connect(registration_input.setEnabled)

            financial_layout.addWidget(registration_check, 0, 0)
            financial_layout.addWidget(registration_input, 0, 1)

            # الواجب الشهري
            monthly_check = QCheckBox("تغيير الواجب الشهري:")
            monthly_check.setFont(QFont("Calibri", 13, QFont.Bold))
            monthly_input = QLineEdit()
            monthly_input.setFont(QFont("Calibri", 13, QFont.Bold))
            monthly_input.setPlaceholderText("مثال: 200 درهم   ")
            monthly_input.setStyleSheet("""
                QLineEdit {
                    padding: 8px;
                    border: 2px solid #ced4da;
                    border-radius: 5px;
                    background-color: white;
                }
                QLineEdit:focus {
                    border: 2px solid #007bff;
                }
                QLineEdit:disabled {
                    background-color: #f8f9fa;
                    color: #6c757d;
                }
            """)
            monthly_input.setEnabled(False)
            monthly_check.toggled.connect(monthly_input.setEnabled)

            financial_layout.addWidget(monthly_check, 1, 0)
            financial_layout.addWidget(monthly_input, 1, 1)

            layout.addWidget(financial_group)

            # معلومات التلاميذ المحددين مع منطقة تمرير
            info_group = QGroupBox("👥 التلاميذ المحددين")
            info_layout = QVBoxLayout(info_group)

            # إنشاء منطقة تمرير للتلاميذ
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_area.setMaximumHeight(150)  # تحديد ارتفاع أقصى لمنطقة التمرير
            scroll_area.setStyleSheet("""
                QScrollArea {
                    border: 1px solid #dee2e6;
                    border-radius: 5px;
                    background-color: white;
                }
            """)

            students_info = self.get_selected_students_info(selected_ids)
            info_text = "\n".join([f"• {info['name']} (رمز: {info['code']}) - القسم: {info['section']}"
                                 for info in students_info])

            info_label = QLabel(info_text)
            info_label.setFont(QFont("Calibri", 12))
            info_label.setStyleSheet("background-color: white; padding: 10px; border: none;")
            info_label.setWordWrap(True)

            scroll_area.setWidget(info_label)
            info_layout.addWidget(scroll_area)

            layout.addWidget(info_group)

            # إضافة مساحة مرنة لضمان ظهور الأزرار
            layout.addStretch()

            # الأزرار
            button_layout = QHBoxLayout()

            # زر التطبيق
            apply_btn = QPushButton("✅ تطبيق التغييرات")
            apply_btn.setFont(QFont("Calibri", 13, QFont.Bold))
            apply_btn.setStyleSheet("""
                QPushButton {
                    background-color: #28a745;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 12px 20px;
                    margin: 5px;
                }
                QPushButton:hover {
                    background-color: #218838;
                }
            """)
            apply_btn.clicked.connect(lambda: self.apply_bulk_changes(
                dialog, selected_ids, section_check, section_combo,
                registration_check, registration_input, monthly_check, monthly_input
            ))

            # زر الإلغاء
            cancel_btn = QPushButton("❌ إلغاء")
            cancel_btn.setFont(QFont("Calibri", 12, QFont.Bold))
            cancel_btn.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 12px 20px;
                    margin: 5px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """)
            cancel_btn.clicked.connect(dialog.reject)

            button_layout.addWidget(apply_btn)
            button_layout.addWidget(cancel_btn)

            layout.addLayout(button_layout)

            # عرض النافذة
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في فتح نافذة التعديل الجماعي: {str(e)}"
            )















    def show_monthly_duties_menu(self):
        """إظهار قائمة منسدلة لأداء الواجبات الشهرية"""
        try:
            from PyQt5.QtWidgets import QMenu

            # إنشاء قائمة منسدلة مثل نافذة الحضور والغياب
            menu = QMenu(self)
            menu.setFont(QFont("Calibri", 12, QFont.Bold))
            menu.setStyleSheet("""
                QMenu {
                    background-color: white;
                    border: 2px solid #e67e22;
                    border-radius: 8px;
                    padding: 5px;
                }
                QMenu::item {
                    background-color: transparent;
                    padding: 8px 20px;
                    border-radius: 4px;
                    color: #2c3e50;
                }
                QMenu::item:selected {
                    background-color: #e67e22;
                    color: white;
                }
            """)

            # أداء الواجبات الشهرية حسب التلميذ
            individual_action = menu.addAction("👤 أداء الواجبات الشهرية حسب التلميذ")
            individual_action.triggered.connect(self.handle_monthly_duties)

            # إضافة فاصل
            menu.addSeparator()

            # أداء الواجبات للتلاميذ متعددي الأقسام
            multi_section_action = menu.addAction("🏫 إدارة التلاميذ متعددي الأقسام")
            multi_section_action.triggered.connect(self.handle_multi_section_duties)

            # البحث عن زر أداء الواجبات الشهرية لعرض القائمة تحته
            monthly_btn = None
            for btn in self.findChildren(QPushButton):
                if "أداء الواجبات الشهرية" in btn.text():
                    monthly_btn = btn
                    break

            # عرض القائمة
            if monthly_btn:
                menu.exec_(monthly_btn.mapToGlobal(monthly_btn.rect().bottomLeft()))
            else:
                # موقع افتراضي
                menu.exec_(self.mapToGlobal(self.rect().center()))

        except Exception as e:
            print(f"خطأ في إظهار قائمة أداء الواجبات الشهرية: {str(e)}")

    def handle_multi_section_duties(self):
        """التعامل مع إدارة التلاميذ متعددي الأقسام - من خلال تحديد السجل"""
        try:
            # التحقق من وجود سجل محدد
            selected_rows = set()
            for item in self.table.selectedItems():
                selected_rows.add(item.row())

            if not selected_rows:
                QMessageBox.information(
                    self,
                    "معلومة",
                    "يرجى تحديد سجل التلميذ أولاً من الجدول."
                )
                return

            if len(selected_rows) > 1:
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "يرجى تحديد سجل واحد فقط."
                )
                return

            # الحصول على الصف المحدد
            selected_row = list(selected_rows)[0]

            # الحصول على رمز التلميذ من العمود المناسب (العمود 3 - رمز التلميذ)
            student_code_item = self.table.item(selected_row, 3)
            if not student_code_item:
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "لا يمكن الحصول على رمز التلميذ من السجل المحدد."
                )
                return

            student_code = student_code_item.text()

            # الحصول على اسم التلميذ للتأكيد
            student_name_item = self.table.item(selected_row, 2)
            student_name = student_name_item.text() if student_name_item else "غير محدد"

            # التحقق من وجود التلميذ في أقسام متعددة
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT COUNT(*) FROM جدول_البيانات
                WHERE رمز_التلميذ = ?
            """, (student_code,))

            count = cursor.fetchone()[0]
            conn.close()

            if count == 0:
                QMessageBox.warning(
                    self,
                    "تحذير",
                    f"لم يتم العثور على تلميذ برمز: {student_code}"
                )
                return
            elif count == 1:
                QMessageBox.information(
                    self,
                    "معلومة",
                    f"التلميذ {student_name} برمز {student_code} مسجل في قسم واحد فقط.\n"
                    "يمكنك استخدام 'أداء الواجبات الشهرية حسب التلميذ' للتلاميذ ذوي القسم الواحد."
                )
                return
            else:
                # التلميذ مسجل في أقسام متعددة - تأكيد الفتح
                reply = QMessageBox.question(
                    self,
                    "تأكيد",
                    f"فتح نافذة إدارة التلاميذ متعددي الأقسام للتلميذ:\n"
                    f"الاسم: {student_name}\n"
                    f"الرمز: {student_code}\n"
                    f"عدد الأقسام: {count}",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )

                if reply == QMessageBox.Yes:
                    # فتح النافذة المتخصصة
                    self.open_multi_section_duties_window(student_code)

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء فتح نافذة التلاميذ متعددي الأقسام: {str(e)}"
            )

    def open_multi_section_duties_window(self, student_code):
        """فتح نافذة إدارة التلاميذ متعددي الأقسام"""
        try:
            # محاولة استيراد وفتح النافذة المتخصصة
            try:
                from multi_section_duties_window import MultiSectionDutiesWindow

                # إنشاء وفتح النافذة
                self.multi_section_duties_window = MultiSectionDutiesWindow(
                    parent=self,
                    db_path=self.db_path,
                    student_code=student_code
                )

                # إظهار النافذة
                self.multi_section_duties_window.show()
                self.multi_section_duties_window.raise_()
                self.multi_section_duties_window.activateWindow()

            except ImportError:
                QMessageBox.critical(
                    self,
                    "خطأ في الاستيراد",
                    "لا يمكن العثور على نافذة التلاميذ متعددي الأقسام.\n"
                    "تأكد من وجود ملف multi_section_duties_window.py"
                )
                return

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في فتح نافذة التلاميذ متعددي الأقسام: {str(e)}"
            )

    def show_reports_menu(self):
        """إظهار قائمة منسدلة للتقارير"""
        try:
            from PyQt5.QtWidgets import QMenu

            # إنشاء قائمة منسدلة مثل نافذة الحضور والغياب
            menu = QMenu(self)
            menu.setFont(QFont("Calibri", 12, QFont.Bold))
            menu.setStyleSheet("""
                QMenu {
                    background-color: white;
                    border: 2px solid #6f42c1;
                    border-radius: 8px;
                    padding: 5px;
                }
                QMenu::item {
                    background-color: transparent;
                    padding: 8px 20px;
                    border-radius: 4px;
                    color: #2c3e50;
                }
                QMenu::item:selected {
                    background-color: #6f42c1;
                    color: white;
                }
            """)

            # طباعة تقرير مفصل
            detailed_action = menu.addAction("🖨️ طباعة تقرير مفصل")
            detailed_action.triggered.connect(self.handle_detailed_report)

            # تقرير القسم الشهري
            monthly_report_action = menu.addAction("📊 تقرير القسم الشهري")
            monthly_report_action.triggered.connect(self.handle_section_monthly_report)

            # تقرير القسم السنوي
            yearly_report_action = menu.addAction("📈 تقرير القسم السنوي")
            yearly_report_action.triggered.connect(self.handle_section_yearly_report)

            # تقرير واجبات التسجيل
            registration_report_action = menu.addAction("💰 تقرير واجبات التسجيل")
            registration_report_action.triggered.connect(self.handle_registration_fees_report)

            # البحث عن زر التقارير لعرض القائمة تحته
            reports_btn = None
            for btn in self.findChildren(QPushButton):
                if "التقارير" in btn.text():
                    reports_btn = btn
                    break

            # عرض القائمة
            if reports_btn:
                menu.exec_(reports_btn.mapToGlobal(reports_btn.rect().bottomLeft()))
            else:
                # موقع افتراضي
                menu.exec_(self.mapToGlobal(self.rect().center()))

        except Exception as e:
            print(f"خطأ في إظهار قائمة التقارير: {str(e)}")







    def handle_deactivate_student(self):
        """إلغاء تنشيط التلميذ المحدد"""
        try:
            selected_ids = self.get_selected_student_id()

            if len(selected_ids) == 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد تلميذ واحد على الأقل لإلغاء التنشيط.")
                return

            # تأكيد العملية
            reply = QMessageBox.question(
                self,
                "تأكيد إلغاء التنشيط",
                f"هل أنت متأكد من إلغاء تنشيط {len(selected_ids)} تلميذ؟\n"
                "سيتم إضافة 'غير منشط' إلى قسم كل تلميذ.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                deactivated_count = 0
                for student_id in selected_ids:
                    # الحصول على القسم الحالي
                    cursor.execute("SELECT القسم FROM جدول_البيانات WHERE id = ?", (student_id,))
                    result = cursor.fetchone()

                    if result:
                        current_section = result[0] or ""
                        # التحقق من أن التلميذ ليس مجمد بالفعل
                        if "غير منشط" not in current_section:
                            new_section = f"غير منشط + {current_section}"
                            cursor.execute(
                                "UPDATE جدول_البيانات SET القسم = ? WHERE id = ?",
                                (new_section, student_id)
                            )
                            deactivated_count += 1

                conn.commit()
                conn.close()

                QMessageBox.information(
                    self,
                    "تم بنجاح",
                    f"تم إلغاء تنشيط {deactivated_count} تلميذ بنجاح."
                )

                # تحديث البيانات
                self.refresh_data()
                self.clear_all_selections()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إلغاء تنشيط التلميذ: {str(e)}")

    def handle_restore_student(self):
        """إرجاع التلميذ المجمد إلى قسمه الأصلي"""
        try:
            selected_ids = self.get_selected_student_id()

            if len(selected_ids) == 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد تلميذ مجمد واحد على الأقل للإرجاع.")
                return

            # التحقق من أن التلاميذ المحددين مجمدين
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            frozen_students = []
            for student_id in selected_ids:
                cursor.execute("SELECT اسم_التلميذ, القسم FROM جدول_البيانات WHERE id = ?", (student_id,))
                result = cursor.fetchone()
                if result and "غير منشط" in (result[1] or ""):
                    frozen_students.append((student_id, result[0], result[1]))

            if not frozen_students:
                QMessageBox.warning(self, "تحذير", "لا يوجد تلاميذ مجمدين في التحديد.")
                conn.close()
                return

            # تأكيد العملية
            reply = QMessageBox.question(
                self,
                "تأكيد الإرجاع",
                f"هل أنت متأكد من إرجاع {len(frozen_students)} تلميذ مجمد إلى أقسامهم الأصلية؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                restored_count = 0
                for student_id, _, current_section in frozen_students:
                    # استخراج القسم الأصلي
                    if "غير منشط + " in current_section:
                        original_section = current_section.replace("غير منشط + ", "")
                        cursor.execute(
                            "UPDATE جدول_البيانات SET القسم = ? WHERE id = ?",
                            (original_section, student_id)
                        )
                        restored_count += 1

                conn.commit()
                conn.close()

                QMessageBox.information(
                    self,
                    "تم بنجاح",
                    f"تم إرجاع {restored_count} تلميذ إلى أقسامهم الأصلية بنجاح."
                )

                # تحديث البيانات
                self.refresh_data()
                self.clear_all_selections()
            else:
                conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إرجاع التلميذ: {str(e)}")

    def secure_section_field_in_edit_mode(self):
        """تأمين حقل القسم في وضع التعديل"""
        try:
            if hasattr(self, 'registration_window') and hasattr(self.registration_window, 'section_combo'):
                # تعطيل حقل القسم في وضع التعديل
                self.registration_window.section_combo.setEnabled(False)

                # تطبيق تنسيق مرئي يوضح أن الحقل مؤمن
                self.registration_window.section_combo.setStyleSheet("""
                    QComboBox {
                        background-color: #f0f0f0;
                        color: #666;
                        border: 2px solid #ccc;
                        border-radius: 5px;
                        padding: 8px;
                        font-weight: bold;
                        font-size: 14px;
                    }
                    QComboBox::drop-down {
                        border: none;
                    }
                    QComboBox::down-arrow {
                        image: none;
                    }
                """)

                print("تم تأمين حقل القسم في وضع التعديل")
            else:
                print("تحذير: لم يتم العثور على حقل القسم في النافذة")
        except Exception as e:
            print(f"خطأ في تأمين حقل القسم: {str(e)}")

    def set_fixed_column_widths(self):
        """تعيين عرض ثابت لأعمدة الجدول وارتفاع الصفوف - يطبق في جميع الحالات"""
        try:
            # تعيين عرض ثابت لكل عمود يدوياً - يطبق دائماً
            # يمكنك تعديل هذه القيم حسب الحاجة
            column_widths = [
                90,   # 0 - اختيار (مربع الاختيار الكبير)
                70,   # 1 - ID
                170,   # 2 - اسم التلميذ - أعرض للأسماء الطويلة
                120,   # 3 - رمز التلميذ
                90,    # 4 - النوع
                150,   # 5 - رقم الهاتف الأول
                100,   # 6 - القسم
                200,   # 7 - المجموعة
                120,   # 8 - إجمالي مبلغ التسجيل
                120,   # 9 - الواجب الشهري
                120    # 10 - حالة الدفع
            ]

            # تعيين ارتفاع الصفوف يدوياً - ثابت في جميع الحالات
            # يمكنك تعديل هذه القيمة لجعل الصفوف أطول أو أقصر
            row_height = 35  # ارتفاع مناسب لمربعات الاختيار الكبيرة

            # تطبيق ارتفاع الصفوف على الجدول
            self.table.verticalHeader().setDefaultSectionSize(row_height)
            self.table.verticalHeader().setMinimumSectionSize(row_height)
            self.table.verticalHeader().setMaximumSectionSize(row_height)

            # تعيين ارتفاع كل صف موجود يدوياً
            for row in range(self.table.rowCount()):
                self.table.setRowHeight(row, row_height)

            header = self.table.horizontalHeader()

            # تعطيل التمدد التلقائي
            header.setStretchLastSection(False)

            # تعيين عرض كل عمود يدوياً وبشكل ثابت
            for i, width in enumerate(column_widths):
                if i < self.table.columnCount():
                    self.table.setColumnWidth(i, width)
                    # جعل جميع الأعمدة ثابتة العرض
                    header.setSectionResizeMode(i, QHeaderView.Fixed)

            print(f"✅ تم تعيين عرض ثابت لـ {len(column_widths)} عمود وارتفاع {row_height} بكسل للصفوف")

        except Exception as e:
            print(f"❌ خطأ في تعيين عرض الأعمدة وارتفاع الصفوف: {str(e)}")

    def refresh_form_data(self):
        """تحديث جميع بيانات النموذج"""
        try:
            print("🔄 بدء تحديث النموذج...")

            # إظهار رسالة تحديث للمستخدم
            from PyQt5.QtWidgets import QProgressDialog
            from PyQt5.QtCore import Qt

            progress = QProgressDialog("جاري تحديث البيانات...", "إلغاء", 0, 100, self)
            progress.setWindowTitle("تحديث النموذج")
            progress.setWindowModality(Qt.WindowModal)
            progress.setAutoClose(True)
            progress.setAutoReset(True)
            progress.show()

            # حفظ التحديدات الحالية
            selected_rows = []
            for row in range(self.table.rowCount()):
                checkbox_item = self.table.item(row, 0)
                if checkbox_item and checkbox_item.checkState() == Qt.Checked:
                    # حفظ ID التلميذ للصف المحدد
                    id_item = self.table.item(row, 1)
                    if id_item:
                        selected_rows.append(id_item.text())

            progress.setValue(20)
            QApplication.processEvents()

            # حفظ إعدادات التصفية الحالية
            current_search = self.search_input.text()
            current_section = self.section_filter_combo.currentText()
            current_month = self.month_filter_combo.currentText()
            current_year = self.year_filter_combo.currentText()

            progress.setValue(40)
            QApplication.processEvents()

            # تحديث خيارات التصفية (الأقسام)
            self.load_filter_options()

            progress.setValue(60)
            QApplication.processEvents()

            # إعادة تطبيق إعدادات التصفية المحفوظة
            self.search_input.setText(current_search)

            # استعادة تحديد القسم
            section_index = self.section_filter_combo.findText(current_section)
            if section_index >= 0:
                self.section_filter_combo.setCurrentIndex(section_index)

            # استعادة تحديد الشهر
            month_index = self.month_filter_combo.findText(current_month)
            if month_index >= 0:
                self.month_filter_combo.setCurrentIndex(month_index)

            # استعادة تحديد السنة
            year_index = self.year_filter_combo.findText(current_year)
            if year_index >= 0:
                self.year_filter_combo.setCurrentIndex(year_index)

            progress.setValue(80)
            QApplication.processEvents()

            # تحديث بيانات الجدول
            self.refresh_data()

            progress.setValue(90)
            QApplication.processEvents()

            # استعادة التحديدات السابقة
            if selected_rows:
                self.restore_selections(selected_rows)

            progress.setValue(100)
            QApplication.processEvents()

            # إخفاء شريط التقدم
            progress.close()

            # إظهار رسالة نجاح
            QMessageBox.information(
                self,
                "تم التحديث",
                f"✅ تم تحديث النموذج بنجاح!\n\n"
                f"📊 إحصائيات التحديث:\n"
                f"• تم تحديث قوائم التصفية\n"
                f"• تم إعادة تحميل البيانات\n"
                f"• تم الحفاظ على إعدادات التصفية\n"
                f"• تم استعادة التحديدات السابقة: {len(selected_rows)} عنصر"
            )

            print("✅ تم تحديث النموذج بنجاح")

        except Exception as e:
            print(f"❌ خطأ في تحديث النموذج: {str(e)}")
            QMessageBox.critical(
                self,
                "خطأ في التحديث",
                f"حدث خطأ أثناء تحديث النموذج:\n{str(e)}\n\nيرجى المحاولة مرة أخرى."
            )

    def restore_selections(self, selected_ids):
        """استعادة التحديدات السابقة بناءً على IDs"""
        try:
            restored_count = 0
            for row in range(self.table.rowCount()):
                id_item = self.table.item(row, 1)
                if id_item and id_item.text() in selected_ids:
                    checkbox_item = self.table.item(row, 0)
                    if checkbox_item:
                        checkbox_item.setCheckState(Qt.Checked)
                        # تلوين الصف المحدد
                        for col in range(self.table.columnCount()):
                            item = self.table.item(row, col)
                            if item:
                                item.setBackground(QColor("#1e3a8a"))  # أزرق غامق
                                item.setForeground(QColor("white"))   # نص أبيض
                        restored_count += 1

            print(f"✅ تم استعادة {restored_count} تحديد من أصل {len(selected_ids)}")

        except Exception as e:
            print(f"❌ خطأ في استعادة التحديدات: {str(e)}")







    def send_to_thermal_printer(self, content):
        """إرسال المحتوى للطابعة الحرارية"""
        try:
            # هنا يمكن إضافة كود الطباعة الفعلي
            # مؤقتاً سنعرض المحتوى في نافذة
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton

            dialog = QDialog(self)
            dialog.setWindowTitle("معاينة التوصيل")
            dialog.setModal(True)
            dialog.resize(600, 800)

            layout = QVBoxLayout(dialog)

            text_edit = QTextEdit()
            text_edit.setPlainText(content)
            text_edit.setReadOnly(True)
            text_edit.setFont(QFont("Courier New", 10))
            layout.addWidget(text_edit)

            close_btn = QPushButton("إغلاق")
            close_btn.clicked.connect(dialog.accept)
            layout.addWidget(close_btn)

            dialog.exec_()

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إرسال المحتوى للطابعة: {str(e)}")



    def delete_student_with_validation(self, student_id):
        """حذف التلميذ مع التحقق من عدم وجود سجلات أداء"""
        try:
            # الحصول على معلومات التلميذ أولاً
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # جلب معلومات التلميذ
            cursor.execute("""
                SELECT اسم_التلميذ, رمز_التلميذ, القسم
                FROM جدول_البيانات
                WHERE id = ?
            """, (student_id,))

            student_info = cursor.fetchone()
            if not student_info:
                QMessageBox.warning(
                    self,
                    "تحذير",
                    f"لم يتم العثور على التلميذ بالرقم: {student_id}"
                )
                conn.close()
                return

            student_name = student_info[0] or "غير محدد"
            student_code = student_info[1] or "غير محدد"
            student_section = student_info[2] or "غير محدد"

            # التحقق من وجود سجلات في جدول registration_fees
            cursor.execute("""
                SELECT COUNT(*) FROM registration_fees
                WHERE student_id = ?
            """, (student_id,))

            registration_count = cursor.fetchone()[0]

            # التحقق من وجود سجلات في جدول monthly_duties
            cursor.execute("""
                SELECT COUNT(*) FROM monthly_duties
                WHERE student_id = ?
            """, (student_id,))

            monthly_count = cursor.fetchone()[0]

            conn.close()

            # إذا كان هناك سجلات أداء، منع الحذف
            if registration_count > 0 or monthly_count > 0:
                error_message = f"❌ لا يمكن حذف التلميذ: {student_name}\n\n"
                error_message += f"📋 معلومات التلميذ:\n"
                error_message += f"• الاسم: {student_name}\n"
                error_message += f"• الرمز: {student_code}\n"
                error_message += f"• القسم: {student_section}\n\n"
                error_message += f"🚫 سبب المنع:\n"

                if registration_count > 0:
                    error_message += f"• يوجد {registration_count} سجل أداء في واجبات التسجيل\n"

                if monthly_count > 0:
                    error_message += f"• يوجد {monthly_count} سجل أداء في الواجبات الشهرية\n"

                error_message += f"\n💡 لحذف التلميذ، يجب حذف جميع سجلات الأداء أولاً."

                QMessageBox.critical(
                    self,
                    "منع الحذف",
                    error_message
                )
                return

            # إذا لم توجد سجلات أداء، اطلب التأكيد
            confirmation_message = f"⚠️ تأكيد حذف التلميذ\n\n"
            confirmation_message += f"📋 معلومات التلميذ:\n"
            confirmation_message += f"• الاسم: {student_name}\n"
            confirmation_message += f"• الرمز: {student_code}\n"
            confirmation_message += f"• القسم: {student_section}\n\n"
            confirmation_message += f"✅ لا توجد سجلات أداء مالي\n\n"
            confirmation_message += f"🗑️ هل أنت متأكد من حذف هذا التلميذ نهائياً؟\n"
            confirmation_message += f"⚠️ هذا الإجراء لا يمكن التراجع عنه!"

            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                confirmation_message,
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.perform_student_deletion(student_id, student_name, student_code)

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء التحقق من إمكانية حذف التلميذ:\n{str(e)}"
            )

    def perform_student_deletion(self, student_id, student_name, student_code):
        """تنفيذ حذف التلميذ من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # حذف التلميذ من جدول_البيانات
            cursor.execute("DELETE FROM جدول_البيانات WHERE id = ?", (student_id,))

            # التحقق من نجاح الحذف
            if cursor.rowcount > 0:
                conn.commit()
                conn.close()

                # إظهار رسالة نجاح
                success_message = f"✅ تم حذف التلميذ بنجاح!\n\n"
                success_message += f"📋 التلميذ المحذوف:\n"
                success_message += f"• الاسم: {student_name}\n"
                success_message += f"• الرمز: {student_code}\n"
                success_message += f"• ID: {student_id}\n\n"
                success_message += f"🔄 سيتم تحديث القائمة تلقائياً."

                QMessageBox.information(
                    self,
                    "تم الحذف",
                    success_message
                )

                # تحديث البيانات
                self.refresh_data()

                # إزالة التحديد
                self.clear_all_selections()

            else:
                conn.close()
                QMessageBox.warning(
                    self,
                    "تحذير",
                    f"لم يتم العثور على التلميذ للحذف.\nربما تم حذفه مسبقاً."
                )

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ في الحذف",
                f"فشل في حذف التلميذ من قاعدة البيانات:\n{str(e)}"
            )

# تشغيل التطبيق للاختبار
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    window = DataViewWindow()
    window.show()

    sys.exit(app.exec_())
