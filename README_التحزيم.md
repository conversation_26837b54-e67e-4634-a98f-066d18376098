# 📦 دليل تحزيم البرنامج

## 🎯 ملف التحزيم الموحد

تم توحيد جميع ملفات التحزيم في ملف واحد فقط:

**`ultimate_pdf_build.spec`** - الملف الوحيد للتحزيم

## 🚀 طرق التحزيم

### 1. الطريقة الأولى - ملف Python
```bash
python build_package.py
```

### 2. الطريقة الثانية - ملف Batch
```bash
package_build.bat
```

### 3. الطريقة الثالثة - مباشرة
```bash
pyinstaller ultimate_pdf_build.spec
```

## 📋 المتطلبات

### المكتبات المطلوبة:
- ✅ **PyInstaller** - للتحزيم
- ✅ **pywin32** - للطباعة (تم إضافتها حديثاً)
- ✅ **PyQt5** - واجهة المستخدم
- ✅ **FPDF** - إنشاء ملفات PDF
- ✅ **ReportLab** - PDF متقدم
- ✅ **arabic_reshaper** - النصوص العربية
- ✅ **PIL/Pillow** - معالجة الصور
- ✅ **openpyxl** - ملفات Excel
- ✅ **matplotlib** - الرسوم البيانية
- ✅ **numpy** - العمليات الرياضية
- ✅ **pandas** - تحليل البيانات

### الملفات المطلوبة:
- ✅ `main_window.py` - النافذة الرئيسية
- ✅ `01.ico` - أيقونة البرنامج
- ✅ `data.db` - قاعدة البيانات
- ✅ `fonts/` - مجلد الخطوط العربية

## 🎯 المميزات المضمنة في التحزيم

### 🖼️ النوافذ والواجهات:
- النافذة الرئيسية
- نوافذ إدارة الطلاب
- نوافذ الواجبات الشهرية
- نوافذ التقارير والطباعة
- نوافذ الإعدادات

### 📄 مكتبات PDF:
- **FPDF** - إنشاء PDF أساسي
- **ReportLab** - PDF متقدم مع الرسوم
- **arabic_reshaper** - دعم النصوص العربية
- **bidi** - اتجاه النصوص

### 🖨️ مكتبات الطباعة:
- **win32print** - طباعة Windows
- **win32api** - واجهات Windows
- **win32gui** - واجهة المستخدم
- **pywintypes** - أنواع البيانات

### 🎨 الخطوط والتنسيق:
- خطوط عربية (Arial, Calibri)
- دعم UTF-8
- تنسيق RTL

## 📁 هيكل الإخراج

```
dist/
└── المعين_في_الحراسة_العامة_PDF_نهائي/
    ├── المعين_في_الحراسة_العامة_PDF_نهائي.exe  # الملف التنفيذي
    ├── fonts/                                      # الخطوط العربية
    ├── logs/                                       # ملفات السجلات
    ├── reports/                                    # التقارير
    ├── data.db                                     # قاعدة البيانات
    └── [ملفات المكتبات والدعم]
```

## ⚡ التشغيل السريع

1. **تأكد من تثبيت pywin32:**
   ```bash
   pip install pywin32
   ```

2. **شغل التحزيم:**
   ```bash
   python build_package.py
   ```

3. **انتقل لمجلد الإخراج:**
   ```
   dist/المعين_في_الحراسة_العامة_PDF_نهائي/
   ```

4. **شغل البرنامج:**
   ```
   المعين_في_الحراسة_العامة_PDF_نهائي.exe
   ```

## 🔧 استكشاف الأخطاء

### مشكلة: pywin32 غير موجود
```bash
pip install pywin32
```

### مشكلة: PyInstaller غير موجود
```bash
pip install pyinstaller
```

### مشكلة: ملفات مفقودة
تأكد من وجود جميع الملفات المطلوبة في نفس المجلد

### مشكلة: خطأ في الطباعة
تأكد من تثبيت pywin32 بشكل صحيح

## 📞 الدعم

في حالة وجود مشاكل:
1. تأكد من تثبيت جميع المتطلبات
2. تأكد من وجود جميع الملفات
3. شغل التحزيم من موجه الأوامر لرؤية الأخطاء

## 🎉 النتيجة النهائية

برنامج مكتمل وجاهز للتوزيع يحتوي على:
- ✅ جميع المميزات
- ✅ دعم الطباعة الكامل
- ✅ النصوص العربية
- ✅ قاعدة البيانات
- ✅ الخطوط العربية
- ✅ أيقونة مخصصة
