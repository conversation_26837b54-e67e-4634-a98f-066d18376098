#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت لإصلاح أعمدة جدول_البيانات وإضافة الأعمدة المفقودة
"""

import sqlite3
import os

def fix_database_columns():
    """إصلاح أعمدة قاعدة البيانات"""
    db_path = "data.db"
    
    if not os.path.exists(db_path):
        print(f"❌ قاعدة البيانات غير موجودة: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 فحص بنية جدول جدول_البيانات...")
        
        # فحص الأعمدة الموجودة
        cursor.execute("PRAGMA table_info(جدول_البيانات)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        print(f"📋 الأعمدة الموجودة حالياً: {len(column_names)}")
        for i, col_name in enumerate(column_names, 1):
            print(f"  {i:2d}. {col_name}")
        
        # قائمة الأعمدة المطلوبة
        required_columns = [
            ('اسم_الاستاذ', 'TEXT'),
            ('المادة_الدراسية', 'TEXT'),
            ('اجمالي_مبلغ_التسجيل', 'REAL'),
            ('الواجب_الشهري', 'REAL'),
            ('اسم_المجموعة', 'TEXT'),
            ('المؤسسة_الاصلية', 'TEXT')
        ]
        
        # إضافة الأعمدة المفقودة
        added_columns = []
        for col_name, col_type in required_columns:
            if col_name not in column_names:
                print(f"🔧 إضافة العمود المفقود: {col_name} ({col_type})")
                cursor.execute(f"ALTER TABLE جدول_البيانات ADD COLUMN {col_name} {col_type}")
                added_columns.append(col_name)
            else:
                print(f"✅ العمود موجود: {col_name}")
        
        if added_columns:
            print(f"✅ تم إضافة {len(added_columns)} عمود جديد:")
            for col in added_columns:
                print(f"  - {col}")
        else:
            print("✅ جميع الأعمدة المطلوبة موجودة")
        
        # فحص البنية النهائية
        cursor.execute("PRAGMA table_info(جدول_البيانات)")
        final_columns = cursor.fetchall()
        
        print(f"\n📋 البنية النهائية للجدول: {len(final_columns)} عمود")
        for i, (cid, name, type_, notnull, default, pk) in enumerate(final_columns, 1):
            print(f"  {i:2d}. {name} ({type_})")
        
        conn.commit()
        conn.close()
        
        print("\n🎉 تم إصلاح قاعدة البيانات بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح قاعدة البيانات: {str(e)}")

if __name__ == "__main__":
    fix_database_columns()
