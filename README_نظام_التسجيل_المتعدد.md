# 🎓 نظام تسجيل تلميذ في أقسام متعددة

## 📋 وصف النظام

هذا النظام يسمح بتسجيل تلميذ واحد في عدة أقسام مختلفة بطريقة احترافية ومبسطة. تم تطويره ليكون سهل الاستخدام مع واجهة عربية جميلة ومنظمة.

## ✨ المميزات الرئيسية

### 🧑‍🎓 إدارة معلومات التلميذ
- إدخال المعلومات الأساسية (الاسم، الرمز، النوع، تاريخ الميلاد)
- إدارة معلومات الاتصال (أرقام الهاتف، العنوان)
- واجهة سهلة ومنظمة

### 📚 اختيار الأقسام المتعددة
- عرض جميع الأقسام المتاحة من قاعدة البيانات
- إمكانية اختيار عدة أقسام للتلميذ الواحد
- عرض تفاصيل كل قسم (المادة، القسم، الأستاذ)
- أزرار سريعة لاختيار الكل أو إلغاء الكل

### 📊 ملخص التسجيل
- عرض ملخص شامل لبيانات التلميذ
- قائمة الأقسام المختارة
- إحصائيات التسجيل (عدد الأقسام، عدد المواد)

### ⚡ إجراءات متقدمة
- حفظ التسجيل في قاعدة البيانات
- طباعة تقرير التسجيل
- مسح البيانات للتسجيل التالي
- تحديث تلقائي للملخص

## 🚀 كيفية التشغيل

### الطريقة الأولى: تشغيل مباشر
```bash
python run_student_registration.py
```

### الطريقة الثانية: تشغيل الملف الرئيسي
```bash
python student_multi_registration.py
```

## 📁 هيكل الملفات

```
📦 نظام التسجيل المتعدد
├── 📄 student_multi_registration.py    # الملف الرئيسي للنظام
├── 📄 run_student_registration.py      # ملف التشغيل
├── 📄 README_نظام_التسجيل_المتعدد.md   # هذا الملف
└── 📄 data.db                          # قاعدة البيانات (تُنشأ تلقائياً)
```

## 🎯 كيفية الاستخدام

### الخطوة 1: إدخال معلومات التلميذ
1. افتح تبويب "معلومات التلميذ"
2. أدخل الاسم الكامل للتلميذ (مطلوب)
3. أدخل رمز التلميذ (اختياري)
4. اختر النوع وتاريخ الميلاد
5. أدخل معلومات الاتصال

### الخطوة 2: اختيار الأقسام
1. انتقل إلى تبويب "اختيار الأقسام"
2. ستظهر قائمة بجميع الأقسام المتاحة
3. اختر الأقسام المطلوبة بوضع علامة ✓
4. يمكنك استخدام "اختيار الكل" أو "إلغاء الكل"
5. ستظهر الأقسام المختارة في الجدول السفلي

### الخطوة 3: مراجعة الملخص
1. انتقل إلى تبويب "ملخص التسجيل"
2. راجع معلومات التلميذ
3. تأكد من الأقسام المختارة
4. اطلع على الإحصائيات

### الخطوة 4: حفظ التسجيل
1. انتقل إلى تبويب "الإجراءات"
2. انقر على "حفظ التسجيل"
3. ستظهر رسالة تأكيد النجاح
4. يمكنك طباعة التقرير أو مسح البيانات للتسجيل التالي

## 🔧 المتطلبات التقنية

### البرمجيات المطلوبة
- Python 3.6 أو أحدث
- PyQt5
- SQLite3 (مدمج مع Python)

### تثبيت المتطلبات
```bash
pip install PyQt5
```

## 🗄️ قاعدة البيانات

النظام يستخدم قاعدة بيانات SQLite مع الجدول التالي:

### جدول `تسجيل_التلاميذ_متعدد_الاقسام`
- `id`: المعرف الفريد
- `اسم_التلميذ`: اسم التلميذ
- `رمز_التلميذ`: رمز التلميذ
- `النوع`: ذكر/أنثى
- `تاريخ_الميلاد`: تاريخ الميلاد
- `رقم_الهاتف_الأول`: رقم الهاتف الأساسي
- `رقم_الهاتف_الثاني`: رقم هاتف إضافي
- `العنوان_الملاحظات`: العنوان والملاحظات
- `المادة`: اسم المادة الدراسية
- `القسم`: اسم القسم
- `اسم_الاستاذ`: اسم الأستاذ
- `تاريخ_التسجيل`: تاريخ ووقت التسجيل

## 🎨 واجهة المستخدم

### التصميم
- واجهة عربية بالكامل
- ألوان احترافية ومريحة للعين
- تبويبات منظمة لسهولة التنقل
- أزرار واضحة مع رموز تعبيرية

### التبويبات
1. **معلومات التلميذ**: إدخال البيانات الأساسية
2. **اختيار الأقسام**: اختيار الأقسام المطلوبة
3. **ملخص التسجيل**: مراجعة البيانات
4. **الإجراءات**: حفظ وطباعة ومسح

## 🔍 استكشاف الأخطاء

### مشكلة: لا تظهر الأقسام المتاحة
**الحل**: تأكد من وجود بيانات في جدول `جدول_المواد_والاقسام`

### مشكلة: خطأ في قاعدة البيانات
**الحل**: تأكد من صلاحيات الكتابة في مجلد البرنامج

### مشكلة: لا يعمل الحفظ
**الحل**: تأكد من إدخال اسم التلميذ واختيار قسم واحد على الأقل

## 📞 الدعم الفني

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- تأكد من قراءة هذا الدليل كاملاً
- تحقق من رسائل الخطأ المعروضة
- تواصل مع فريق التطوير مع تفاصيل المشكلة

## 📝 ملاحظات مهمة

- يجب إدخال اسم التلميذ (مطلوب)
- يجب اختيار قسم واحد على الأقل
- يمكن تسجيل نفس التلميذ في أقسام متعددة
- البيانات تُحفظ فوراً عند النقر على "حفظ التسجيل"
- يمكن طباعة تقرير شامل للتسجيل

---

© 2024 نظام إدارة المدرسة - جميع الحقوق محفوظة
