#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار التحديثات الجديدة للجدول الموحد
"""

import sys
import os
import sqlite3

def test_database_query():
    """اختبار استعلام قاعدة البيانات المحدث"""
    try:
        print("🧪 اختبار استعلام قاعدة البيانات...")
        
        # التحقق من وجود قاعدة البيانات
        if not os.path.exists("data.db"):
            print("❌ قاعدة البيانات غير موجودة")
            return False
        
        conn = sqlite3.connect("data.db")
        cursor = conn.cursor()
        
        # اختبار استعلام واجبات التسجيل المحدث
        print("🔍 اختبار استعلام واجبات التسجيل...")
        cursor.execute("""
            SELECT 'واجب تسجيل' as type, rf.id, rf.payment_type, rf.payment_date, 
                   COALESCE(jb.اجمالي_مبلغ_التسجيل, 0) as amount_required, 
                   rf.amount_paid, 
                   (COALESCE(jb.اجمالي_مبلغ_التسجيل, 0) - rf.amount_paid) as amount_remaining, 
                   'مدفوع' as payment_status, rf.notes, rf.created_date
            FROM registration_fees rf
            LEFT JOIN جدول_البيانات jb ON rf.student_id = jb.id
            WHERE rf.student_id = ?
            ORDER BY rf.created_date ASC
        """, (1,))
        
        registration_records = cursor.fetchall()
        print(f"✅ تم العثور على {len(registration_records)} سجل تسجيل")
        
        # عرض عينة من البيانات
        if registration_records:
            print("📋 عينة من بيانات واجبات التسجيل:")
            for i, record in enumerate(registration_records[:3]):
                print(f"   السجل {i+1}: النوع={record[0]}, المبلغ المطلوب={record[4]}, المبلغ المدفوع={record[5]}")
        
        # اختبار استعلام واجبات الأداء
        print("\n🔍 اختبار استعلام واجبات الأداء...")
        cursor.execute("""
            SELECT 'واجب شهري' as type, id, month, year, amount_required, 
                   amount_paid, amount_remaining, payment_status, notes, created_date
            FROM monthly_duties 
            WHERE student_id = ?
            ORDER BY year DESC, 
            CASE month 
                WHEN 'يناير' THEN 1 WHEN 'فبراير' THEN 2 WHEN 'مارس' THEN 3
                WHEN 'أبريل' THEN 4 WHEN 'مايو' THEN 5 WHEN 'يونيو' THEN 6
                WHEN 'يوليو' THEN 7 WHEN 'أغسطس' THEN 8 WHEN 'سبتمبر' THEN 9
                WHEN 'أكتوبر' THEN 10 WHEN 'نوفمبر' THEN 11 WHEN 'ديسمبر' THEN 12
            END DESC
        """, (1,))
        
        duties_records = cursor.fetchall()
        print(f"✅ تم العثور على {len(duties_records)} واجب شهري")
        
        # عرض عينة من البيانات
        if duties_records:
            print("📋 عينة من بيانات واجبات الأداء:")
            for i, record in enumerate(duties_records[:3]):
                print(f"   السجل {i+1}: النوع={record[0]}, الشهر={record[2]}, المبلغ المطلوب={record[4]}")
        
        conn.close()
        
        # اختبار الترتيب الجديد
        print("\n🔄 اختبار الترتيب الجديد...")
        all_records = list(registration_records) + list(duties_records)
        print(f"📊 إجمالي السجلات: {len(all_records)}")
        print(f"   واجبات التسجيل: {len(registration_records)} (أولاً)")
        print(f"   واجبات الأداء: {len(duties_records)} (ثانياً)")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_font_settings():
    """اختبار إعدادات الخط الجديدة"""
    try:
        print("\n🔤 اختبار إعدادات الخط...")
        
        from PyQt5.QtGui import QFont
        
        # اختبار الخط الجديد
        font = QFont("Calibri", 13, QFont.Bold)
        print(f"✅ خط الجدول: {font.family()}, حجم: {font.pointSize()}, غامق: {font.bold()}")
        
        if font.family() == "Calibri" and font.pointSize() == 13 and font.bold():
            print("✅ إعدادات الخط صحيحة")
            return True
        else:
            print("❌ إعدادات الخط غير صحيحة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الخط: {e}")
        return False

def test_receipt_content():
    """اختبار محتوى التوصيل الموحد"""
    try:
        print("\n🧾 اختبار محتوى التوصيل الموحد...")
        
        # بيانات تجريبية
        sample_records = [
            ["واجب تسجيل", "1", "دفعة أولى", "2024-01-15", "500.00 درهم", "500.00 درهم", "0.00 درهم", "مدفوع", "تم الدفع", "2024-01-15"],
            ["واجب شهري", "2", "يناير", "2024", "200.00 درهم", "200.00 درهم", "0.00 درهم", "مدفوع كاملاً", "تم الدفع", "2024-01-20"]
        ]
        
        print("📋 بيانات تجريبية:")
        for i, record in enumerate(sample_records):
            print(f"   السجل {i+1}: {record[0]} - {record[2]} - {record[5]}")
        
        # محاكاة إنشاء محتوى التوصيل
        receipt_lines = []
        receipt_lines.append("=" * 40)
        receipt_lines.append("المؤسسة التعليمية".center(40))
        receipt_lines.append("=" * 40)
        receipt_lines.append("وصل دفع موحد".center(40))
        receipt_lines.append("-" * 40)
        
        total_amount = 0.0
        for i, record in enumerate(sample_records, 1):
            receipt_lines.append(f"السجل {i}: {record[0]}".rjust(40))
            receipt_lines.append(f"المبلغ المدفوع: {record[5]}".rjust(40))
            
            # حساب الإجمالي
            try:
                amount = float(record[5].replace(" درهم", ""))
                total_amount += amount
            except:
                pass
        
        receipt_lines.append("-" * 40)
        receipt_lines.append(f"إجمالي المبلغ: {total_amount:.2f} درهم".rjust(40))
        
        receipt_content = "\n".join(receipt_lines)
        print(f"✅ تم إنشاء محتوى التوصيل ({len(receipt_lines)} سطر)")
        print(f"💰 إجمالي المبلغ: {total_amount:.2f} درهم")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار محتوى التوصيل: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔍 اختبار التحديثات الجديدة للجدول الموحد")
    print("=" * 60)
    
    tests = [
        ("استعلام قاعدة البيانات", test_database_query),
        ("إعدادات الخط", test_font_settings),
        ("محتوى التوصيل", test_receipt_content)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} - نجح")
        else:
            print(f"❌ {test_name} - فشل")
    
    print("\n" + "=" * 60)
    print("📊 ملخص الاختبارات:")
    print("=" * 60)
    print(f"✅ نجح: {passed}/{total}")
    
    if passed == total:
        print("🎉 جميع التحديثات تعمل بشكل صحيح!")
    else:
        print("⚠️ بعض التحديثات تحتاج مراجعة")
    
    print("\n🔧 التحديثات المطبقة:")
    print("• المبلغ المطلوب لواجبات التسجيل من جدول_البيانات.اجمالي_مبلغ_التسجيل")
    print("• ترتيب السجلات: واجبات التسجيل أولاً (ASC) ثم واجبات الأداء (DESC)")
    print("• خط الجدول: Calibri 13 أسود غامق")
    print("• طباعة التوصيل بنفس طريقة واجبات الأداء (طابعة حرارية)")
    print("• محتوى التوصيل الموحد مع تفاصيل كل سجل")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
    
    input("\nاضغط Enter للخروج...")
