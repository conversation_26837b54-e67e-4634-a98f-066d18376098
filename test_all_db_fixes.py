#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل لجميع إصلاحات مسارات قاعدة البيانات
"""

import os
import sys

def test_file_db_path(file_name):
    """اختبار مسار قاعدة البيانات في ملف واحد"""
    print(f"\n🔍 اختبار {file_name}...")
    
    if not os.path.exists(file_name):
        print(f"❌ الملف غير موجود: {file_name}")
        return False
    
    try:
        module_name = file_name.replace('.py', '')
        
        if module_name == 'print101':
            from print101 import get_database_path
            db_path = get_database_path()
            print(f"✅ print101 - مسار قاعدة البيانات: {db_path}")
            
        elif module_name == 'print_section_monthly':
            from print_section_monthly import get_database_path
            db_path = get_database_path()
            print(f"✅ print_section_monthly - مسار قاعدة البيانات: {db_path}")
            
        elif module_name == 'print_section_yearly':
            from print_section_yearly import get_database_path
            db_path = get_database_path()
            print(f"✅ print_section_yearly - مسار قاعدة البيانات: {db_path}")
            
        elif module_name == 'print_registration_fees_monthly_style':
            from print_registration_fees_monthly_style import get_database_path
            db_path = get_database_path()
            print(f"✅ print_registration_fees_monthly_style - مسار قاعدة البيانات: {db_path}")
            
        elif module_name == 'print111':
            from print111 import get_database_path
            db_path = get_database_path()
            print(f"✅ print111 - مسار قاعدة البيانات: {db_path}")
            
        elif module_name == 'print144':
            from print144 import get_database_path
            db_path = get_database_path()
            print(f"✅ print144 - مسار قاعدة البيانات: {db_path}")
            
        elif module_name == 'attendance_sheet_report':
            from attendance_sheet_report import get_database_path
            db_path = get_database_path()
            print(f"✅ attendance_sheet_report - مسار قاعدة البيانات: {db_path}")
            
        elif module_name == 'monthly_duties_window':
            from monthly_duties_window import get_db_path
            db_path = get_db_path()
            print(f"✅ monthly_duties_window - مسار قاعدة البيانات: {db_path}")
            
        elif module_name == 'multi_section_duties_window':
            from multi_section_duties_window import get_db_path
            db_path = get_db_path()
            print(f"✅ multi_section_duties_window - مسار قاعدة البيانات: {db_path}")
            
        else:
            print(f"⚠️ ملف غير مدعوم للاختبار: {file_name}")
            return True
        
        # التحقق من وجود قاعدة البيانات
        if os.path.exists(db_path):
            print(f"   ✅ قاعدة البيانات موجودة في: {db_path}")
        else:
            print(f"   ⚠️ قاعدة البيانات غير موجودة في: {db_path}")
        
        # التحقق من أن المسار لا يحتوي على _internal
        if '_internal' in db_path:
            print(f"   ❌ خطأ: المسار يحتوي على _internal!")
            return False
        else:
            print(f"   ✅ المسار صحيح (لا يحتوي على _internal)")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار شامل لجميع إصلاحات مسارات قاعدة البيانات")
    print("=" * 70)
    print(f"🐍 Python: {sys.version}")
    print(f"📁 المجلد الحالي: {os.getcwd()}")
    print(f"🔧 حالة التحزيم: {'محزم' if getattr(sys, 'frozen', False) else 'غير محزم'}")
    
    # قائمة جميع الملفات المُصلحة
    files_to_test = [
        "print101.py",
        "print_section_monthly.py",
        "print_section_yearly.py", 
        "print_registration_fees_monthly_style.py",
        "print111.py",
        "print144.py",
        "attendance_sheet_report.py",
        "monthly_duties_window.py",
        "multi_section_duties_window.py"
    ]
    
    passed = 0
    total = len(files_to_test)
    
    for file_name in files_to_test:
        if test_file_db_path(file_name):
            passed += 1
    
    print("\n" + "=" * 70)
    print("📊 ملخص النتائج:")
    print("=" * 70)
    print(f"✅ نجح: {passed}/{total} ملف")
    
    if passed == total:
        print("🎉 جميع الملفات تم إصلاحها بنجاح!")
        print("\n💡 الآن يمكنك إعادة تحزيم البرنامج بأمان:")
        print("   pyinstaller ultimate_pdf_build.spec")
        print("   copy data.db \"dist\\المعين_في_الحراسة_العامة_PDF_نهائي\\\"")
        print("\n🔧 بعد التحزيم:")
        print("   • ستعمل جميع التقارير من مجلد البرنامج الرئيسي")
        print("   • لن تبحث عن قاعدة البيانات في مجلد _internal")
        print("   • ستجد data.db في نفس مجلد الملف التنفيذي")
    else:
        print("⚠️ بعض الملفات لم تنجح - راجع الأخطاء أعلاه")
    
    print("\n🔍 الإصلاحات المطبقة:")
    print("• إضافة دالة get_database_path() أو get_db_path() لكل ملف")
    print("• تغيير مسار قاعدة البيانات من __file__ إلى sys.executable")
    print("• البحث في مجلد البرنامج الرئيسي بدلاً من _internal")
    print("• دعم البيئة العادية والمحزمة")
    print("• إزالة الاعتماد على ملف db_path.py غير الموجود")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
    
    input("\nاضغط Enter للخروج...")
