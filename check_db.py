#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def fix_budget_table():
    """إصلاح جدول الموازنة السنوية"""
    db_path = "data.db"

    if not os.path.exists(db_path):
        print(f"❌ قاعدة البيانات غير موجودة: {db_path}")
        return

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        print("🔧 بدء إصلاح جدول الموازنة السنوية...")

        # التحقق من وجود الجدول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='الموازنة_السنوية';")
        table_exists = cursor.fetchone()

        if table_exists:
            print("📋 الجدول موجود، سيتم إعادة إنشاؤه...")

            # نسخ البيانات الموجودة إلى جدول مؤقت
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS الموازنة_السنوية_مؤقت AS
                SELECT * FROM الموازنة_السنوية;
            """)

            # حذف الجدول القديم
            cursor.execute("DROP TABLE الموازنة_السنوية;")
            print("✅ تم حذف الجدول القديم")

        # إنشاء الجدول الجديد بالبنية الصحيحة
        cursor.execute("""
            CREATE TABLE الموازنة_السنوية (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                السنة_المالية INTEGER NOT NULL,
                نوع_البند TEXT NOT NULL,
                اسم_البند TEXT NOT NULL,
                المبلغ_المتوقع REAL NOT NULL DEFAULT 0,
                المبلغ_الفعلي REAL DEFAULT 0,
                النسبة_المحققة REAL DEFAULT 0,
                الحالة TEXT DEFAULT 'نشط',
                تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(السنة_المالية, نوع_البند, اسم_البند)
            )
        """)
        print("✅ تم إنشاء الجدول الجديد بالبنية الصحيحة")

        # استعادة البيانات إذا كانت موجودة
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='الموازنة_السنوية_مؤقت';")
        temp_table_exists = cursor.fetchone()

        if temp_table_exists:
            try:
                # محاولة نسخ البيانات مع تعديل أسماء الأعمدة
                cursor.execute("SELECT * FROM الموازنة_السنوية_مؤقت LIMIT 1;")
                sample = cursor.fetchone()

                if sample:
                    # التحقق من بنية الجدول المؤقت
                    cursor.execute("PRAGMA table_info(الموازنة_السنوية_مؤقت);")
                    temp_columns = cursor.fetchall()

                    has_old_column = any(col[1] == 'السنة' for col in temp_columns)

                    if has_old_column:
                        # نسخ البيانات مع تعديل اسم العمود
                        cursor.execute("""
                            INSERT INTO الموازنة_السنوية
                            (السنة_المالية, نوع_البند, اسم_البند, المبلغ_المتوقع, المبلغ_الفعلي)
                            SELECT السنة, نوع_البند, اسم_البند, المبلغ_المتوقع, المبلغ_الفعلي
                            FROM الموازنة_السنوية_مؤقت;
                        """)
                    else:
                        # نسخ البيانات مباشرة
                        cursor.execute("""
                            INSERT INTO الموازنة_السنوية
                            (السنة_المالية, نوع_البند, اسم_البند, المبلغ_المتوقع, المبلغ_الفعلي)
                            SELECT السنة_المالية, نوع_البند, اسم_البند, المبلغ_المتوقع, المبلغ_الفعلي
                            FROM الموازنة_السنوية_مؤقت;
                        """)

                    print("✅ تم استعادة البيانات الموجودة")

            except Exception as e:
                print(f"⚠️ تعذر استعادة البيانات القديمة: {e}")

            # حذف الجدول المؤقت
            cursor.execute("DROP TABLE الموازنة_السنوية_مؤقت;")
            print("✅ تم حذف الجدول المؤقت")

        # إضافة بيانات تجريبية إذا كان الجدول فارغ
        cursor.execute("SELECT COUNT(*) FROM الموازنة_السنوية;")
        count = cursor.fetchone()[0]

        if count == 0:
            print("📋 إضافة بيانات تجريبية...")

            # بيانات الإيرادات التجريبية
            revenue_data = [
                (2024, "إيرادات", "registration_fees", 50000, 0),
                (2024, "إيرادات", "monthly_duties", 300000, 0),
                (2024, "إيرادات", "other_revenue", 25000, 0),
                (2025, "إيرادات", "registration_fees", 55000, 0),
                (2025, "إيرادات", "monthly_duties", 320000, 0),
                (2025, "إيرادات", "other_revenue", 30000, 0)
            ]

            # بيانات المصاريف التجريبية
            expense_data = [
                (2024, "مصاريف", "رواتب", 200000, 0),
                (2024, "مصاريف", "كراء", 60000, 0),
                (2024, "مصاريف", "فواتير وأقساط", 30000, 0),
                (2025, "مصاريف", "رواتب", 220000, 0),
                (2025, "مصاريف", "كراء", 65000, 0),
                (2025, "مصاريف", "فواتير وأقساط", 35000, 0)
            ]

            all_data = revenue_data + expense_data

            cursor.executemany("""
                INSERT INTO الموازنة_السنوية
                (السنة_المالية, نوع_البند, اسم_البند, المبلغ_المتوقع, المبلغ_الفعلي)
                VALUES (?, ?, ?, ?, ?)
            """, all_data)

            print("✅ تم إضافة البيانات التجريبية")

        conn.commit()
        conn.close()

        print("🎉 تم إصلاح جدول الموازنة السنوية بنجاح!")

    except Exception as e:
        print(f"❌ خطأ في إصلاح الجدول: {e}")

def check_database():
    """فحص قاعدة البيانات وعرض الجداول والأعمدة"""
    db_path = "data.db"

    if not os.path.exists(db_path):
        print(f"❌ قاعدة البيانات غير موجودة: {db_path}")
        return

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # فحص جدول الموازنة السنوية تحديداً
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='الموازنة_السنوية';")
        table_exists = cursor.fetchone()

        if table_exists:
            print("📋 جدول الموازنة السنوية:")
            print("=" * 40)

            # عرض أعمدة الجدول
            cursor.execute("PRAGMA table_info(الموازنة_السنوية);")
            columns = cursor.fetchall()

            print("الأعمدة:")
            for col in columns:
                col_id, col_name, col_type, not_null, default_val, primary_key = col
                pk_marker = " (PRIMARY KEY)" if primary_key else ""
                nn_marker = " NOT NULL" if not_null else ""
                print(f"  - {col_name}: {col_type}{nn_marker}{pk_marker}")

            # عرض عينة من البيانات
            cursor.execute("SELECT * FROM الموازنة_السنوية LIMIT 5;")
            sample_data = cursor.fetchall()

            if sample_data:
                print("\nعينة من البيانات:")
                for row in sample_data:
                    print(f"  {row}")
            else:
                print("\nالجدول فارغ")
        else:
            print("❌ جدول الموازنة السنوية غير موجود")

        conn.close()

    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")

if __name__ == "__main__":
    print("🔧 أداة إصلاح جدول الموازنة السنوية")
    print("=" * 50)

    choice = input("اختر العملية:\n1. إصلاح الجدول\n2. فحص الجدول\nالاختيار (1 أو 2): ")

    if choice == "1":
        fix_budget_table()
    elif choice == "2":
        check_database()
    else:
        print("❌ اختيار غير صحيح")
