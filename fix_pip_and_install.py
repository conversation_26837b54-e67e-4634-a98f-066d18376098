#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح pip وتثبيت PyQt5
"""

import subprocess
import sys
import os

def run_command(command, description):
    """تشغيل أمر وطباعة النتيجة"""
    print(f"\n{description}...")
    print(f"الأمر: {' '.join(command)}")
    
    try:
        result = subprocess.run(command, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ نجح: {description}")
            if result.stdout:
                print(f"النتيجة: {result.stdout[:200]}...")
            return True
        else:
            print(f"❌ فشل: {description}")
            if result.stderr:
                print(f"الخطأ: {result.stderr[:200]}...")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ انتهت المهلة الزمنية: {description}")
        return False
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("    إصلاح pip وتثبيت PyQt5")
    print("=" * 60)
    
    python_exe = sys.executable
    print(f"مسار Python: {python_exe}")
    
    # الخطوة 1: إصلاح pip
    print("\n🔧 الخطوة 1: إصلاح pip")
    commands = [
        ([python_exe, "-m", "ensurepip", "--upgrade"], "إصلاح pip"),
        ([python_exe, "-m", "pip", "install", "--upgrade", "pip"], "تحديث pip"),
    ]
    
    for command, desc in commands:
        run_command(command, desc)
    
    # الخطوة 2: تثبيت PyQt5
    print("\n📦 الخطوة 2: تثبيت PyQt5")
    
    # جرب طرق مختلفة لتثبيت PyQt5
    pyqt5_commands = [
        ([python_exe, "-m", "pip", "install", "PyQt5"], "تثبيت PyQt5 (الطريقة العادية)"),
        ([python_exe, "-m", "pip", "install", "PyQt5==5.15.9"], "تثبيت PyQt5 إصدار محدد"),
        ([python_exe, "-m", "pip", "install", "--user", "PyQt5"], "تثبيت PyQt5 للمستخدم فقط"),
        ([python_exe, "-m", "pip", "install", "--no-cache-dir", "PyQt5"], "تثبيت PyQt5 بدون cache"),
    ]
    
    pyqt5_installed = False
    for command, desc in pyqt5_commands:
        if run_command(command, desc):
            pyqt5_installed = True
            break
    
    # الخطوة 3: تثبيت المكتبات الأخرى
    if pyqt5_installed:
        print("\n📚 الخطوة 3: تثبيت المكتبات الأخرى")
        
        other_packages = [
            "fpdf2",
            "arabic-reshaper", 
            "python-bidi",
            "openpyxl",
            "matplotlib",
            "numpy",
            "Pillow"
        ]
        
        for package in other_packages:
            run_command([python_exe, "-m", "pip", "install", package], f"تثبيت {package}")
    
    # الخطوة 4: اختبار التثبيت
    print("\n🧪 الخطوة 4: اختبار التثبيت")
    
    try:
        import PyQt5
        print("✅ PyQt5 مثبت بنجاح!")
        
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtGui import QFont, QColor, QIcon, QPixmap
        from PyQt5.QtCore import Qt
        print("✅ جميع وحدات PyQt5 تعمل!")
        
    except ImportError as e:
        print(f"❌ PyQt5 ما زال غير مثبت: {e}")
        
        # حل بديل: تحميل PyQt5 يدوياً
        print("\n💡 حل بديل:")
        print("1. اذهب إلى: https://www.riverbankcomputing.com/software/pyqt/download5")
        print("2. حمل PyQt5 installer لـ Windows")
        print("3. أو جرب: conda install pyqt (إذا كان لديك Anaconda)")
        
        return False
    
    print("\n🎉 تم تثبيت جميع المكتبات بنجاح!")
    print("يمكنك الآن تشغيل البرنامج:")
    print("python main_window.py")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ العملية مكتملة بنجاح!")
        else:
            print("\n❌ فشلت العملية - راجع الأخطاء أعلاه")
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف العملية")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
    
    input("\nاضغط Enter للخروج...")
