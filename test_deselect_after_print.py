#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إلغاء التحديد بعد طباعة التوصيل الموحد
"""

import sys
import os

def test_deselect_functionality():
    """اختبار وظيفة إلغاء التحديد"""
    try:
        print("🧪 اختبار وظيفة إلغاء التحديد بعد الطباعة...")
        print("=" * 50)
        
        # استيراد النافذة
        from monthly_duties_window import MonthlyDutiesManagementWindow
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء النافذة
        window = MonthlyDutiesManagementWindow(student_id=1)
        print("✅ تم إنشاء النافذة بنجاح")
        
        # التحقق من وجود الجدول الموحد
        if not hasattr(window, 'unified_table'):
            print("❌ الجدول الموحد غير موجود")
            return False
        
        # تحميل البيانات
        window.load_unified_data()
        print("✅ تم تحميل البيانات")
        
        # التحقق من وجود سجلات
        row_count = window.unified_table.rowCount()
        if row_count == 0:
            print("⚠️ لا توجد سجلات للاختبار")
            return True
        
        print(f"📊 عدد السجلات: {row_count}")
        
        # اختبار تحديد بعض السجلات
        print("\n🔍 اختبار تحديد السجلات...")
        selected_count = 0
        for row in range(min(3, row_count)):  # تحديد أول 3 سجلات أو أقل
            checkbox = window.unified_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(True)
                selected_count += 1
        
        print(f"✅ تم تحديد {selected_count} سجل")
        
        # التحقق من التحديد
        window.update_selection_summary()
        current_selected = 0
        for row in range(row_count):
            checkbox = window.unified_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                current_selected += 1
        
        print(f"📋 السجلات المحددة حالياً: {current_selected}")
        
        if current_selected != selected_count:
            print("❌ عدد السجلات المحددة غير صحيح")
            return False
        
        # اختبار دالة إلغاء التحديد
        print("\n🔄 اختبار دالة إلغاء التحديد...")
        window.deselect_all_records()
        
        # التحقق من إلغاء التحديد
        window.update_selection_summary()
        final_selected = 0
        for row in range(row_count):
            checkbox = window.unified_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                final_selected += 1
        
        print(f"📋 السجلات المحددة بعد الإلغاء: {final_selected}")
        
        if final_selected == 0:
            print("✅ تم إلغاء تحديد جميع السجلات بنجاح")
            return True
        else:
            print("❌ لم يتم إلغاء تحديد جميع السجلات")
            return False
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_print_workflow():
    """اختبار سير عمل الطباعة مع إلغاء التحديد"""
    try:
        print("\n🖨️ اختبار سير عمل الطباعة...")
        print("=" * 50)
        
        # محاكاة سير العمل
        steps = [
            "1. تحديد السجلات المطلوبة",
            "2. الضغط على زر طباعة التوصيل الموحد",
            "3. إنشاء محتوى التوصيل",
            "4. إرسال التوصيل للطابعة الحرارية",
            "5. إلغاء تحديد جميع السجلات تلقائياً",
            "6. عرض رسالة نجاح العملية"
        ]
        
        print("📋 خطوات سير العمل:")
        for step in steps:
            print(f"   {step}")
        
        print("\n✅ سير العمل محدث ليشمل إلغاء التحديد التلقائي")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار سير العمل: {e}")
        return False

def test_user_experience():
    """اختبار تجربة المستخدم"""
    try:
        print("\n👤 اختبار تجربة المستخدم...")
        print("=" * 50)
        
        benefits = [
            "✅ لا حاجة لإلغاء التحديد يدوياً بعد الطباعة",
            "✅ تجنب طباعة نفس السجلات مرة أخرى بالخطأ",
            "✅ واجهة أكثر سهولة وأماناً",
            "✅ تدفق عمل أكثر سلاسة",
            "✅ تقليل الأخطاء البشرية"
        ]
        
        print("🎯 فوائد إلغاء التحديد التلقائي:")
        for benefit in benefits:
            print(f"   {benefit}")
        
        scenarios = [
            "📝 السيناريو 1: طباعة توصيل واحد - يتم إلغاء التحديد تلقائياً",
            "📝 السيناريو 2: طباعة عدة توصيلات - كل طباعة تلغي التحديد",
            "📝 السيناريو 3: إلغاء الطباعة - التحديد يبقى كما هو"
        ]
        
        print("\n🎬 سيناريوهات الاستخدام:")
        for scenario in scenarios:
            print(f"   {scenario}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تجربة المستخدم: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔍 اختبار إلغاء التحديد بعد طباعة التوصيل الموحد")
    print("=" * 60)
    
    tests = [
        ("وظيفة إلغاء التحديد", test_deselect_functionality),
        ("سير عمل الطباعة", test_print_workflow),
        ("تجربة المستخدم", test_user_experience)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} - نجح")
        else:
            print(f"❌ {test_name} - فشل")
    
    print("\n" + "=" * 60)
    print("📊 ملخص الاختبارات:")
    print("=" * 60)
    print(f"✅ نجح: {passed}/{total}")
    
    if passed == total:
        print("🎉 جميع اختبارات إلغاء التحديد نجحت!")
    else:
        print("⚠️ بعض الاختبارات تحتاج مراجعة")
    
    print("\n🔧 التحديث المطبق:")
    print("• إضافة self.deselect_all_records() بعد الطباعة الناجحة")
    print("• تحديث رسالة النجاح لتشمل إلغاء التحديد")
    print("• تحسين تجربة المستخدم وتقليل الأخطاء")
    
    print("\n💡 كيفية العمل:")
    print("1. المستخدم يحدد السجلات المطلوبة")
    print("2. المستخدم يضغط على 'طباعة التوصيل الموحد'")
    print("3. يتم إنشاء وطباعة التوصيل")
    print("4. يتم إلغاء تحديد جميع السجلات تلقائياً")
    print("5. يتم عرض رسالة تأكيد النجاح")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
    
    input("\nاضغط Enter للخروج...")
