#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("اختبار سريع للنظام")
print("=" * 30)

# اختبار Python
import sys
print(f"Python: {sys.version}")

# اختبار الملفات
import os
files = ["main_window.py", "01.ico", "data.db"]
for f in files:
    status = "موجود" if os.path.exists(f) else "مفقود"
    print(f"{f}: {status}")

# اختبار المكتبات
libraries = ["PyQt5", "sqlite3", "os", "sys"]
for lib in libraries:
    try:
        __import__(lib)
        print(f"{lib}: متوفر")
    except ImportError:
        print(f"{lib}: مفقود")

print("\nانتهى الاختبار")
input("اضغط Enter...")
